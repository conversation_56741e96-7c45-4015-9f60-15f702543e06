/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xinfei.vocmng.itl.rr;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @version $ CalllogsResponse, v 0.1 2024-01-16 16:28 junjie.yan Exp $
 */
@Data
public class CalllogsResponse {
    private Integer status;
    private Integer code;
    private String message;
    @JsonProperty("code_message")
    private String codeMessage;
    private Integer size;
    private Integer total;
    @JsonProperty("total_pages")
    private Integer totalPages;

    private AgentsResponse agent;

    private List<AgentsResponse> agents;
}