/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xinfei.vocmng.itl.rr;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.Serializable;

/**
 *
 * <AUTHOR>
 * @version $ MarketListResp, v 0.1 2024/8/16 16:08 wancheng.qu Exp $
 */
@Data
public class MarketListResp implements Serializable {
    private String id;
    private String mobile;
    @JsonProperty("mobile_md5")
    private String mobileMd5;
    private String app;
    private String status;
    @JsonProperty("created_time")
    private String createdTime;
    @JsonProperty("updated_time")
    private String updatedTime;
    @JsonProperty("enable_market_time")
    private String enableMarketTime;
    @JsonProperty("disable_market_time")
    private String disableMarketTime;
    @JsonProperty("disable_reason")
    private String disableReason;
    @JsonProperty("operator_id")
    private String operatorId;
    private String operator;


}