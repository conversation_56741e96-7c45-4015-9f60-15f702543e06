/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xinfei.vocmng.itl.rr;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 *
 * <AUTHOR>
 * @version $ LoanInfoRequest, v 0.1 2024/5/22 17:33 wancheng.qu Exp $
 */
@Data
public class LoanInfoRequest implements Serializable {

    @NotBlank(message = "订单号不能为空")
    @ApiModelProperty(value = "订单号")
    private String orderNumber;
    @NotBlank(message = "合同号不能为空")
    @ApiModelProperty(value = "合同号")
    private String contractNumber;
    @NotBlank(message = "结清时间不能为空")
    @ApiModelProperty(value = "结清时间")
    private String dateSettle;
    @NotBlank(message = "借款金额不能为空")
    @ApiModelProperty(value = "借款金额")
    private String loanAmount;
    @NotBlank(message = "app不能为空")
    @ApiModelProperty(value = "app")
    private String app;
    @ApiModelProperty("资金池,信飞结清证明不用传")
    private String capitalPool;
    @ApiModelProperty("资金池-未加密")
    private String capitalPoolStr;
    @ApiModelProperty("是否特殊资方,信飞结清证明不用传")
    private boolean isSpecial;

    private Integer access;//推送状态，1.已推送，2推送失败，3线下提交'
    private Integer type;

}