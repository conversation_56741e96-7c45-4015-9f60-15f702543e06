/*
 * Xinfei.com Inc.
 * Copyright (c) 2015-2024. All Rights Reserved.
 */

package com.xinfei.vocmng.itl.rr;

import com.google.gson.annotations.SerializedName;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR> 2024/8/28 11:46
 * DiversionOrderInfo
 */
@Data
public class DiversionOrderInfo {

    @SerializedName("order_no")
    private String orderNo;

    @SerializedName("product_name")
    private String productName;

    @SerializedName("product_no")
    private String productNo;

    @SerializedName("status")
    private String status;

    @SerializedName("loan_order_no")
    private String loanOrderNo;

    @SerializedName("audit_amount")
    private String auditAmount;

    @SerializedName("failed_reason")
    private String failedReason;

    private String period;

    private String amount;

    @SerializedName("user_no")
    private String userNo;

    @SerializedName("user_id")
    private String userId;

    private String app;

    @SerializedName("cust_no")
    private String custNo;

    @SerializedName("submit_time")
    private String submitTime;

    @SerializedName("audit_time")
    private String auditTime;

    @SerializedName("loan_apply_time")
    private String loanApplyTime;

    @SerializedName("loan_remit_time")
    private String loanRemitTime;

}
