package com.xinfei.vocmng.itl.rr;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR> 2024/8/12 11:53
 * SmsRecordsRequest
 */
@Data
public class SmsRecordsRequest {
    /**
     * 密文手机号号
     */
    private String mobile;

    /**
     * 模板业务类型
     */
    private String tplBizType;
    /**
     * 查询开始时间
     */
    private Long createStartTime;

    /**
     * 查询结束时间
     */
    private Long createEndTime;

    /**
     * 分页参数
     */
    private Integer page;

    /**
     * 分页参数
     */
    private Integer pageSize;


    /**
     * 默认0 正序 1 倒序
     */
    private Integer descFlag;

}
