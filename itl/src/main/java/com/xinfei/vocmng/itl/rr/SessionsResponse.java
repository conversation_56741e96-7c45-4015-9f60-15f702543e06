/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xinfei.vocmng.itl.rr;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @version $ SessionsRequest, v 0.1 2024-10-16 15:43 junjie.yan Exp $
 */
@Data
public class SessionsResponse {

    /**
     * 会话在企业内部系统中的唯一标识,udesk传的call_id
     */
    private String id;

    /**
     * 质检系统内部生成的call_id
     */
    @JsonProperty(value = "call_id")
    private String callId;

    /**
     * 元数据
     */
    private SessionsMeta meta;

}