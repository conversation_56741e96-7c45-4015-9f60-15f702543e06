package com.xinfei.vocmng.itl.rr;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * 添加反馈请求参数
 *
 * <AUTHOR>
 * @version $ AddFeedbackRequest, v 0.1 2025/07/17 shaohui.chen Exp $
 */
@Data
public class AddFeedbackRequest {
    
    /** 工单id 非 task_number */
    @JsonProperty(value = "task_id")
    private Long taskId;
    
    /** 固定传白旭 qa1:600 生产:970 */
    @JsonProperty(value = "user_id")
    private Long userId;
    
    /** 备注 */
    @JsonProperty(value = "comment")
    private String comment;
    
    /** 默认传 1 */
    @JsonProperty(value = "collection_status")
    private Integer collectionStatus;

    /** 默认传 1 */
    @JsonProperty(value = "is_client_sync")
    private Integer isClientSync;
}
