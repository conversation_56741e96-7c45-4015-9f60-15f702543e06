package com.xinfei.vocmng.itl.rr;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * 工单明细信息
 *
 * <AUTHOR>
 * @version $ WorkOrderDetail, v 0.1 2024/1/15 15:37 qu.lu Exp $
 */
@Data
public class WorkOrderDetail {
    /** 工单主键ID */
    private Integer id;
    /** 工单编号 */
    @JsonProperty(value = "task_number")
    private String workOrderNo;
    /**工单类型一级描述 */
    @JsonProperty(value = "task_type1_cn")
    private String workOrderTypeLv1;
    /** 工单类型二级描述 */
    @JsonProperty(value = "task_type2_cn")
    private String workOrderTypeLv2;
    /** 工单类型三级描述 */
    @JsonProperty(value = "task_type3_cn")
    private String workOrderTypeLv3;
    /** 工单状态，0未提交分配，1待分配，2跟进中，3转派，4退单，5终止， 6知悉结案，7催收结案，8已结案，9失联结案，10不接受方案 */
    @JsonProperty(value = "task_status")
    private String status;
    /** 创建时间 */
    @JsonProperty(value = "created_time")
    private String createdTime;
    /** 渠道 */
    @JsonProperty(value = "task_from_cn")
    private String source;
    /** 工单内容 */
    private String comment;
    /** 创建人 */
    @JsonProperty(value = "create_user")
    private String createUser;
    /** 处理人 */
    @JsonProperty(value = "process_user")
    private String processUser;
    /** 最近一次记录 */
    @JsonProperty(value = "last_record")
    private String lastRecord;
}
