package com.xinfei.vocmng.itl.rr;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR> 2024/8/13 11:12
 * CouponRecordDetail
 */
@Data
public class CouponDetail {

    /**
     * 优惠券id
     */
    @JsonProperty(value = "coupon_id")
    private String couponId;

    /**
     * 优惠券名称
     */
    @JsonProperty(value = "coupon_name")
    private String couponName;

    /**
     * 券类型 1:借款免息券 2:还款立减金 3:限时提额券 4:拉卡拉聚合支付 5:x天免息券
     */
    @JsonProperty(value = "coupon_type")
    private String couponType;

    /**
     * 减免科目 1 手续费，2 权益费，3通用 ,4:息费 5 免息券
     */
    @JsonProperty(value = "discount_category")
    private String discountCategory;

    /**
     * 折扣类型
     */
    @JsonProperty(value = "discount_type")
    private String discountType;

    /**
     * 优惠金额
     */
    @JsonProperty(value = "discount_amount")
    private String discountAmount;

    /**
     * 折扣比例
     */
    @JsonProperty(value = "discount_rate")
    private String discountRate ;

    /**
     * 有效期
     */
    @JsonProperty(value = "valid_days_after")
    private String validDaysAfter;

    /**
     * 到期时间
     */
    @JsonProperty(value = "expired_time")
    private String expiredTime;

    /**
     * 使用时间
     */
    @JsonProperty(value = "used_time")
    private String usedTime;

    /**
     * 期数信息
     */
    private List<PeriodInfo> period_info;


    /**
     * 使用说明
     */
    private List<String> desc;

}
