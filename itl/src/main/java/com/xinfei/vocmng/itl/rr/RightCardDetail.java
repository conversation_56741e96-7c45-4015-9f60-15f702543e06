package com.xinfei.vocmng.itl.rr;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * 权益卡明细信息
 *
 * <AUTHOR>
 * @version $ RightCardDetail, v 0.1 2024/1/11 12:02 qu.lu Exp $
 */
@Data
public class RightCardDetail {
    private Integer id;
    @JsonProperty(value = "rights_pack_id")
    private Integer rightsPackId;
    @JsonProperty(value = "user_id")
    private String userId;
    private String app;
    @JsonProperty(value = "inner_app")
    private String innerApp;
    @JsonProperty(value = "started_at")
    private String startTime;
    @JsonProperty(value = "ended_at")
    private String endTime;
    private Integer status;
    @JsonProperty(value = "is_deleted")
    private Integer isDeleted;
    private BigDecimal fee;
}
