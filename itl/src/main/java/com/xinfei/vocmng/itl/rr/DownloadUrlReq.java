/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xinfei.vocmng.itl.rr;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.Serializable;

/**
 *
 * <AUTHOR>
 * @version $ DownloadUrlReq, v 0.1 2024/6/4 17:50 wancheng.qu Exp $
 */

@Data
public class DownloadUrlReq implements Serializable {

    @JsonProperty("biz_type")
    private String bizType;

    @JsonProperty("biz_no")
    private String bizNo;

    private String app;

    @JsonProperty("user_id")
    private String userId;

    @JsonProperty("user_no")
    private String userNo;

    @JsonProperty("cust_no")
    private String custNo;

    private SettlementRecord data;

    @Data
    public static class SettlementRecord {
        private String name;
        @JsonProperty("id_card_protyle")
        private String idCardProtyle;
        private String amount;
        @JsonProperty("order_number")
        private String orderNumber;
        @JsonProperty("loan_year")
        private String loanYear;
        @JsonProperty("loan_month")
        private String loanMonth;
        @JsonProperty("loan_day")
        private String loanDay;
        @JsonProperty("loan_end_year")
        private String loanEndYear;
        @JsonProperty("loan_end_month")
        private String loanEndMonth;
        @JsonProperty("loan_end_day")
        private String loanEndDay;
        @JsonProperty("app_name")
        private String appName;
        @JsonProperty("pay_year")
        private String payYear;
        @JsonProperty("pay_month")
        private String payMonth;
        @JsonProperty("pay_day")
        private String payDay;
        @JsonProperty("created_time")
        private String createdTime;
    }
}