package com.xinfei.vocmng.itl.rr;

import com.xinfei.vocmng.itl.constants.FeignConstants;
import lombok.Data;

/**
 * <AUTHOR>
 * @version $ NPayResponse, v 0.1 2025/03/20 15:28 pengming.liu Exp $
 */
@Data
public class NPayResponse<T> {
    private String code;
    private String message;
    private T data;

    public boolean isSuccess(){
        return FeignConstants.WORK_ORDER_SUCCESS_CODE.equalsIgnoreCase(code);
    }

    public boolean isFailed(){
        return !isSuccess();
    }
}
