/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xinfei.vocmng.itl.rr;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @version $ ImSessionsResponse, v 0.1 2024-12-18 16:28 pengming.liu Exp $
 */
@Data
public class ImSessionsResponse {

    /** id（同 im_sub_session_id） */
    @JsonProperty("sub_session_id")
    private Long subSessionId;

    /** 会话 id */
    @JsonProperty("session_id")
    private Long sessionId;

    /** 客服系统侧机器人会话 id */
    @JsonProperty("robot_session_id")
    private Long robotSessionId;

    /** 业务记录 id */
    @JsonProperty("note_id")
    private Long noteId;

    /** 客户 id */
    @JsonProperty("customer_id")
    private Long customerId;

    /** 客户姓名加密 */
    @JsonProperty("customer_name")
    private String customerNameCipher;

    /** 客服 id */
    @JsonProperty("agent_id")
    private Long agentId;

    /** 客服姓名加密 */
    @JsonProperty("agent_nick_name")
    private String agentNickName;

    /** 响应时间，单位秒 */
    @JsonProperty("resp_seconds")
    private Integer respSeconds;

    /** 排队时间，单位秒 */
    @JsonProperty("queue_seconds")
    private String queueSeconds;

    /** 持续时间 */
    @JsonProperty("sustain_seconds")
    private Integer sustainSeconds;

    /** 满意度调查结果 id */
    @JsonProperty("survey_vote_id")
    private Integer surveyVoteId;

    /** 满意度-是否已解决，取值："0"、"1"，说明：解决、未解决 */
    @JsonProperty("resolved_state")
    private String resolvedState;

    /** 满意度*/
    private String optionName;

    /** 渠道，取值：web、微信、微博、android、ios、api */
    @JsonProperty("platform")
    private String platform;

    /** 创建时间 */
    @JsonProperty("created_at")
    private LocalDateTime createdAt;

    /** 关闭时间 */
    @JsonProperty("closed_at")
    private LocalDateTime closedAt;

    /** 对话结束方式，取值："agent_close"、"redirect_close"、"sys_close"、"customer_close"，说明：客服关闭、转接关闭、系统关闭、客户关闭 */
    @JsonProperty("close_method")
    private String closeMethod;

    /** 排队队列 */
    @JsonProperty("belong_queue")
    private String belongQueue;

    /** 客服消息数 */
    @JsonProperty("agent_msg_num")
    private Integer agentMsgNum;

    /** 客户消息数 */
    @JsonProperty("customer_msg_num")
    private Integer customerMsgNum;

    /** 来源 */
    @JsonProperty("source")
    private String source;

    /** 排队开始时间 */
    @JsonProperty("queue_start_time")
    private LocalDateTime queueStartTime;

    /** 当日对话次数 */
    @JsonProperty("conversations_num_today")
    private Integer conversationsNumToday;

    /** 搜索关键词 */
    @JsonProperty("search_keyword")
    private String searchKeyword;

    /** 自定义渠道信息 */
    @JsonProperty("custom_channel")
    private String customChannel;

    /** 客服邀评次数 */
    @JsonProperty("agent_invite_vote_count")
    private Integer agentInviteVoteCount;

    /** 最后消息发送方，取值：customer、agent、blank */
    @JsonProperty("last_response")
    private String lastResponse;

    /** 报警次数 */
    @JsonProperty("alert_num")
    private Integer alertNum;

    /** 报警项 */
    @JsonProperty("alert_desc")
    private String alertDesc;

    /** 工单数量 */
    @JsonProperty("ticket_num")
    private Integer ticketNum;

    /** 工单编号，已逗号","分割 */
    @JsonProperty("ticket_nos")
    private String ticketNos;

    /** 来源插件 ID */
    @JsonProperty("im_web_plugin_id")
    private Integer imWebPluginId;

    /** 对话发起方，取值："customer、agent、sys"，说明："客户、客服、系统" */
    @JsonProperty("sender")
    private String sender;

    /** 访客邀请，取值："agent、sys、blank"，说明："客服、自动、无" */
    @JsonProperty("active_guest")
    private String activeGuest;

    /** 公司id */
    @JsonProperty("organization_id")
    private Integer organizationId;

    /** 导航菜单名称 */
    @JsonProperty("menu_names")
    private String menuNames;

    @JsonProperty("resolved_state_title")
    private String resolvedStateTitle;

    @JsonProperty("resolved_state_name")
    private String resolvedStateName;

    @JsonProperty("resolved_state_value")
    private String resolvedStateValue;

    @JsonProperty("transfer_to_agent")
    private String transferToAgent;

    /** 机器人id */
    @JsonProperty("robot_id")
    private Integer robotId;

    /** 机器人名字 */
    @JsonProperty("robot_name")
    private String robotName;

    /** 机器人会话id */
    @JsonProperty("robot_side_session_id")
    private String robotSideSessionId;

    /** 客户自定义字段，详见上文 */
    @JsonProperty("customer_custom_fields")
    private String customerCustomFields;

    /** 浏览器访问信息，详见上文 */
    @JsonProperty("web_info")
    private String webInfo;

    /** 微信访问信息，详见上文 */
    @JsonProperty("weixin_info")
    private String weixinInfo;

    /** 微博访问信息，详见上文 */
    @JsonProperty("weibo_info")
    private String weiboInfo;

    /** API 访问信息 */
    @JsonProperty("api_info")
    private String apiInfo;

    /** iOS SDK 访问信息 */
    @JsonProperty("ios_info")
    private String iosInfo;

    /** Android SDK 访问信息 */
    @JsonProperty("android_info")
    private String androidInfo;

    /** 来源 URL */
    @JsonProperty("source_url")
    private String sourceUrl;

    /** 此对话记录对应的工单 */
    @JsonProperty("ticket_ids")
    private String ticketIds;
}