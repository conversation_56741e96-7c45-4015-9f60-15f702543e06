package com.xinfei.vocmng.itl.rr;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * 会员卡权益使用明细信息
 *
 * <AUTHOR>
 * @version $ MemberCardUseInfo, v 0.1 2023/12/25 20:42 qu.lu Exp $
 */
@Data
public class MemberCardUseInfo {
    /** 权益名称 */
    private String name;
    @JsonProperty(value = "limit_num")
    /** 可领取次数 */
    private Integer limitNum;
    /** 领取次数 */
    private Integer num;
    @JsonProperty(value = "can_show")
    /** 是否可展示 */
    private Boolean canShow;
    /** 备注 */
    private String remark;
}
