/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xinfei.vocmng.itl.rr;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @version $ SessionsMeta, v 0.1 2024-10-16 16:49 junjie.yan Exp $
 */
@Data
public class SessionsMeta {
    /**
     * 创建成功时必须,成功0，失败非0，错误码“1006”表示坐席超出授权范围
     */
    private Integer code;

    /**
     * 创建成功时必须,创建时间
     */
    private String created;

    /**
     * 创建成功时必须,更新时间
     */
    @JsonProperty(value = "last_modified")
    private String lastModified;

    /**
     * 创建成功时必须,该session在循环智能系统中的基地址
     */
    private String location;

    /**
     * 创建成功时必须,取值session
     */
    @JsonProperty(value = "resource_type")
    private String resourceType;

    /**
     * 失败时必须,失败原因描述
     */
    private String message;

}