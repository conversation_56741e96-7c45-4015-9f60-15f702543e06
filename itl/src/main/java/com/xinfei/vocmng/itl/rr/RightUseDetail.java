package com.xinfei.vocmng.itl.rr;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @version $ RightUseDetail, v 0.1 2024/1/11 13:25 qu.lu Exp $
 */
@Data
public class RightUseDetail {
    private String id;
    private String name;
    @JsonProperty(value = "sub_name")
    private String subName;
    private Integer type;
    private String img;
    private String price;
    @JsonProperty(value = "trigger_type")
    private String triggerType;
    @JsonProperty(value = "is_get")
    private Boolean isGet;
    @JsonProperty(value = "get_at")
    private String getAt;
    @JsonProperty(value = "is_used")
    private Boolean isUsed;
    @JsonProperty(value = "used_at")
    private String usedAt;
}
