package com.xinfei.vocmng.itl.rr;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * 工单强制结案请求参数
 *
 * <AUTHOR>
 * @version $ FinishTaskRequest, v 0.1 2025/07/17 shaohui.chen Exp $
 */
@Data
public class FinishTaskRequest {
    
    /** 工单id 非 task_number */
    @JsonProperty(value = "task_id")
    private Long taskId;
    
    /** 固定传白旭 qa1:600 生产:970 */
    @JsonProperty(value = "user_id")
    private Long userId;
    
    /** 默认传8 已结案 （6知悉结案，7催收结案，8已结案，9失联结案） */
    @JsonProperty(value = "task_status")
    private Integer taskStatus;
}
