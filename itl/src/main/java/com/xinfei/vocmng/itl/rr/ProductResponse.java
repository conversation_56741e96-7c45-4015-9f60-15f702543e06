/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2023 All Rights Reserved.
 */
package com.xinfei.vocmng.itl.rr;

import com.xinfei.vocmng.itl.constants.FeignConstants;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR> 2024/8/29 14:47
 * ProductResponse
 */
@Data
public class ProductResponse {

   private Integer status;
   private String message;
   private Long time;
   private List<ProductDetailInfo> response;

   public boolean isSuccess() {
      return FeignConstants.SUCCESS_CODE.equals(this.status);
   }

}