package com.xinfei.vocmng.itl.rr;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * <AUTHOR> 2024/8/14 10:43
 * VipBlackDetail
 */

@Data
public class VipBlackDetail {

    /**
     * id
     */
    private String id;

    /**
     * 手机号
     */
    private String mobile;

    /**
     * 姓名
     */
    private String name;

    /**
     * 操作人
     */
    @JsonProperty(value = "operate_user_name")
    private String operateUserName;

    /**
     * 操作人id
     */
    @JsonProperty(value = "operate_user_id")
    private String operateUserId;

    /**
     * 有效字段
     */
    @JsonProperty(value = "is_deleted")
    private String isDeleted;

    /**
     * 创建时间
     */
    @JsonProperty(value = "created_at")
    private String createdAt;

    /**
     * 修改时间
     */
    @JsonProperty(value = "updated_at")
    private String updatedAt;

}
