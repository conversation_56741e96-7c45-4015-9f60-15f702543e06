/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xinfei.vocmng.itl.rr;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 *
 * <AUTHOR>
 * @version $ AmountInfoHeafer, v 0.1 2024-04-23 19:35 junjie.yan Exp $
 */
@Data
public class AmountInfoHeader {
    private String requester;
    @JsonProperty("biz_type")
    private String bizType;
    @JsonProperty("biz_flow_number")
    private String bizFlowNumber;
    @JsonProperty("seq_id")
    private String seqId;
    @JsonProperty("source_type")
    private String sourceType;
    @JsonProperty("app_name")
    private String appName;
    @JsonProperty("inner_app")
    private String innerApp;
    private String app;
}