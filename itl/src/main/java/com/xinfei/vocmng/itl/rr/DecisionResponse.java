/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xinfei.vocmng.itl.rr;

import lombok.Data;

import java.util.Map;

/**
 * <AUTHOR>
 * @version $ DecisionRequest, v 0.1 2024-12-03 17:25 junjie.yan Exp $
 */
@Data
public class DecisionResponse {

    //SUCCESS、FAIL
    private String state;

    //PASS、REJECT、EXCEPTION（异常，需要重试）
    private String result;

    //策略id
    private String engineCode;

    private String msg;

    private String requestId;

    private String decisionId;

    private String code;

    private String respond;

    private Object in;

    private Map<String, String> out;
}