package com.xinfei.vocmng.itl.rr;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;

/**
 * 会员卡信息查询请求参数
 *
 * <AUTHOR>
 * @version $ MemberCardRequest, v 0.1 2023/12/22 18:41 qu.lu Exp $
 */
@Data
public class MemberCardRequest {
    /** 姓名 */
    private String name;
    /** 用户ID */
    @JsonProperty(value = "user_id")
    private List<Long> userId;
    /** 卡状态 */
    @JsonProperty(value = "card_status")
    private Integer cardStatus;
    /** 页数 */
    private Integer page;
    /** 每页条目 */
    @JsonProperty(value = "page_size")
    private Integer pageSize;
}
