/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xinfei.vocmng.itl.rr;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.Serializable;

/**
 *
 * <AUTHOR>
 * @version $ MarketUpdateReq, v 0.1 2024/8/20 14:23 wancheng.qu Exp $
 */

@Data
public class MarketUpdateReq implements Serializable {
    private String mobile;
    private String app;
    private String status;
    @JsonProperty("enable_market_time")
    private String enableMarketTime;
    private String operator;
    @JsonProperty("operator_id")
    private int operatorId;


    @JsonProperty("disable_market_time")
    private String disableMarketTime;
    @JsonProperty("disable_reason")
    private String disableReason;

}