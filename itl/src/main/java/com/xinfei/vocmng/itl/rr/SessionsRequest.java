/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xinfei.vocmng.itl.rr;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @version $ SessionsRequest, v 0.1 2024-10-16 15:43 junjie.yan Exp $
 */
@Data
public class SessionsRequest {

    /**
     * 会话在企业内部系统中的唯一标识,用udesk传的call_id
     */
    private String id;

    /**
     * 该会话对应的坐席在企业内部系统中的唯一标识
     */
    @JsonProperty(value = "staff_id")
    private String staffId;

    /**
     * call：语音，text：文本
     */
    private String mode;

    @JsonProperty(value = "customer_id")
    private String customerId;

    private String category;



}