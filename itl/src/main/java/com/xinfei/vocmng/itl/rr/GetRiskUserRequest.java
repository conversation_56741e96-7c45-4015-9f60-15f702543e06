/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xinfei.vocmng.itl.rr;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR> 2024/7/4 17:20
 * 
 */
@Data
public class GetRiskUserRequest {
    /**
     * 加密手机号
     */
    @ApiModelProperty(value = "mobile")
    @NotBlank(message = "mobile不能为空")
    private String mobile;
}