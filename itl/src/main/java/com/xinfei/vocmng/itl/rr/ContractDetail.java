package com.xinfei.vocmng.itl.rr;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * 合同明细信息
 *
 * <AUTHOR>
 * @since 2023/12/18
 */
@Data
public class ContractDetail {

    /** 合同标题 */
    private String title;
    /** 合同编号 */
    @JsonProperty(value = "contract_id")
    private String contractId;
    /** 合同简称 */
    @JsonProperty(value = "short_name")
    private String shortName;
    /** 合同查看链接 */
    @JsonProperty(value = "view_url")
    private String viewUrl;
}
