package com.xinfei.vocmng.itl.rr;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @version $ SmsSendDTO, v 0.1 2023/11/23 18:10 valiant.shaw Exp $
 * @Description:
 */
@Data
public class SmsSendArgs {

    public String mobile;//手机号
    @JsonProperty("template_id")
    @JSONField(name = "template_id")
    private String templateId;//短信模板ID
    public Object data;//短信内容参数。如果无参，则空数组
    public String app;//app标识，如：xyf
    @JsonProperty("inner_app")
    @JSONField(name = "inner_app")
    public String innerApp;//inner_app标识，如：xyf
}
