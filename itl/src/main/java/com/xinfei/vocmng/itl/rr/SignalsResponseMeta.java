/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xinfei.vocmng.itl.rr;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @version $ SignalsResponseMeta, v 0.1 2024-10-16 17:44 junjie.yan Exp $
 */
@Data
public class SignalsResponseMeta {

    /**
     * 取值session
     */
    @JsonProperty(value = "resource_type")
    private String resourceType;

    private String created;
    @JsonProperty(value = "last_modified")
    private String lastModified;
    private String location;
    private Integer code;
    private String message;
}