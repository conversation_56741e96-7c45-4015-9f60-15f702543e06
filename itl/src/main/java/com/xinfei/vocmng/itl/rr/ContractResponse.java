package com.xinfei.vocmng.itl.rr;

import com.xinfei.vocmng.itl.constants.FeignConstants;
import lombok.Data;

/**
 * 合同请求响应信息
 *
 * <AUTHOR>
 * @since 2023/12/18
 */
@Data
public class ContractResponse<T> {
    /** 响应状态码 */
    private Integer status;
    /** 响应描述信息 */
    private String message;
    /** 响应结果信息 */
    private T response;

    public boolean isSuccess(){
        return FeignConstants.CONTRACT_SUCCESS_CODE.equals(status);
    }
}
