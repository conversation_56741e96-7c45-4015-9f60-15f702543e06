/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xinfei.vocmng.itl.rr;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @version $ ApplyLoanProofResp, v 0.1 2024/6/6 16:27 wancheng.qu Exp $
 */
@Data
public class ApplyLoanProofResp implements Serializable {

    @JsonProperty("order_numbers")
    private String orderNumbers;
    private String status;
    private String message;
    @JsonProperty("oss_path")
    private String ossPath;
    @JsonProperty("proof_url")
    private String proofUrl;
    @JsonProperty("contract_number")
    private String contractNumber;

}