/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xinfei.vocmng.itl.rr;

import com.xinfei.vocmng.itl.rr.dto.DpsEncodeDto;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @version $ DpsEncodeReq, v 0.1 2024-01-10 17:41 junjie.yan Exp $
 */
@Data
public class DpsEncodeResp {
    private String requestId;

    private String apiName;
    private Long timestamp;
    private String msg;
    private Integer code;
    private List<DpsEncodeDto> payload;
}