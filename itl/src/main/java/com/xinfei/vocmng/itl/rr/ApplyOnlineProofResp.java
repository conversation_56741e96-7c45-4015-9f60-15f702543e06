/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xinfei.vocmng.itl.rr;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 *
 * <AUTHOR>
 * @version $ ApplyOnlineProofResp, v 0.1 2024/6/6 19:24 wancheng.qu Exp $
 */
@Data
public class ApplyOnlineProofResp {

    @JsonProperty("contract_no")
    private String contractNo;

    @JsonProperty("settle_time")
    private String settleTime;

    @JsonProperty("push_time")
    private String pushTime;

    @JsonProperty("is_settle")
    private boolean isSettle;

    @JsonProperty("loan_no")
    private String loanNo;

    @JsonProperty("is_push")
    private boolean isPush;

    private String trace;

    private String msg;

    private boolean status;

    @JsonProperty("xyf_settle_time")
    private String xyfSettleTime;

    @JsonProperty("order_number")
    private String orderNumber;

    private String name;

    @JsonProperty("id_card_number")
    private String idCardNumber;

    private BigDecimal amount;

    @JsonProperty("fund_request_time")
    private String fundRequestTime;
}