package com.xinfei.vocmng.itl.rr;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * 查询话单列表信息
 *
 * <AUTHOR>
 * @version $ CallListRequest, v 0.1 2023/12/27 21:43 qu.lu Exp $
 */
@Data
public class CallListRequest {
    /** 页码 */
    private Integer pageNumber;
    /** 每页展示大小 */
    private Integer pageSize;
    /** 用户编号 */
    private String userNo;
    /** 手机号 */
    private String originMobile;
    /** 系统key: 电销=telemkt;催收=collection_system; */
    private String appKey;
    /** 话单创建时间，开始时间 yyyy-MM-dd HH:mm:ss */
    @JsonProperty(value = "creatTimeStart")
    private String startTime;
    /** 话单创建时间，结束时间  yyyy-MM-dd HH:mm:ss */
    @JsonProperty(value = "creatTimeEnd")
    private String endTime;
}
