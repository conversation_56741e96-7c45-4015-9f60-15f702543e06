/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xinfei.vocmng.itl.rr;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @version $ GetTaskByMobileReq, v 0.1 2024-02-20 21:26 junjie.yan Exp $
 */
@Data
public class GetTaskByMobileResp {
    @ApiModelProperty("工单ID")
    private Long id;
    @ApiModelProperty("工单状态")
    @JsonProperty(value = "task_status")
    private String taskStatus;
    @ApiModelProperty("工单类型:业务主题/京东卡申请/投诉申请")
    @JsonProperty(value = "task_type")
    private String taskType;
    @ApiModelProperty("最近跟踪反馈")
    private String feedback;
    @ApiModelProperty("app")
    private String app;
    @ApiModelProperty("创建时间")
    @JsonProperty(value = "created_time")
    private String createdTime;
}