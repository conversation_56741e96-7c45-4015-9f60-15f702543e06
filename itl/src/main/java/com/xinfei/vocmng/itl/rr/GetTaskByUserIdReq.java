/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xinfei.vocmng.itl.rr;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.time.LocalDateTime;

/**
 *
 * <AUTHOR>
 * @version $ GetTaskByUserIdReq, v 0.1 2024-01-17 15:07 junjie.yan Exp $
 */
@Data
public class GetTaskByUserIdReq {
    @JsonProperty("user_id")
    private Long userId;

    @JsonProperty("created_start")
    private LocalDateTime createdStart;

    @JsonProperty("created_end")
    private LocalDateTime createdEnd;

}