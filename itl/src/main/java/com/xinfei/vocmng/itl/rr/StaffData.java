/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xinfei.vocmng.itl.rr;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 *
 * <AUTHOR>
 * @version $ StaffData, v 0.1 2024-10-15 16:36 junjie.yan Exp $
 */
@Data
public class StaffData {

    /**
     * 员工工号，员工的唯一标示
     */
    private String id;
    /**
     * 员工姓名
     */
    private String name;
    /**
     * 员工手机号，不可重复
     */
    private String phone;
    /**
     * 员工邮箱，<EMAIL>
     */
    private String email;
    /**
     * 员工所属部门id
     */
    @JsonProperty(value = "dept_id")
    private String deptId;
    /**
     * 员员工直属上级员工编号, 若无, 则传空字符串
     */
    private String manager;
    /**
     * 是否离职
     */
    @JsonProperty(value = "is_dimission")
    private Boolean isDimission;

}