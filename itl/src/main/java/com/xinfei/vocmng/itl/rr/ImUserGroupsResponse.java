/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xinfei.vocmng.itl.rr;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @version $ ImUserGroupsResponse, v 0.1 2025-03-10 11:28 pengming.liu Exp $
 */
@Data
public class ImUserGroupsResponse {
    /** 客服组id */
    private Long id;

    /** 客服组名称 */
    private String name;

    /** 客服agent */
    private List<Agents> agents;

    @Data
    public static class Agents{

        /**
         * 客服唯一标识
         */
        private Long id;

        /**
         * 客服姓名
         */
        @JsonProperty("nick_name")
        private String nickName;
    }
}