package com.xinfei.vocmng.itl.rr;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * 会员卡信息
 *
 * <AUTHOR>
 * @version $ MemberCard, v 0.1 2023/12/22 18:34 qu.lu Exp $
 */
@Data
public class OldMemberCard {
    /** 会员卡ID */
    @JsonProperty(value = "id")
    private Integer cardId;
    /** APP用户ID */
    @JsonProperty(value = "user_id")
    private String userId;
    /** 用户名称 */
    private String name;
    /** 手机号码 */
    private String mobile;
    /** app */
    private String app;
    /** 会员卡名称 */
    @JsonProperty(value = "card_name")
    private String cardName;
    /** 卡状态：1开卡，2退卡 ，3失效 */
    @JsonProperty(value = "card_status")
    private Integer cardStatus;
    /** 支付方式 */
    @JsonProperty(value = "pay_type")
    private Integer payType;
    /** 支付金额 */
    @JsonProperty(value = "amount")
    private Integer payAmount;
    /** 支付状态 */
    @JsonProperty(value = "pay_status")
    private Integer payStatus;
    /** 支付状态描述 */
    @JsonProperty(value = "message")
    private String payStatusDesc;
    /** 生效时间 */
    @JsonProperty(value = "begin_time")
    private String beginTime;
    /** 过期时间 */
    @JsonProperty(value = "end_time")
    private String endTime;
    //TODO:是否为该字段：拒就赔付资格
    /** 拒就赔付资格 */
    @JsonProperty(value = "refund_status")
    private String refundStatus;
    /** 创建时间 */
    @JsonProperty(value = "created_at")
    private String createTime;
    /** 操作人 */
    @JsonProperty(value = "operator")
    private String operatorName;
    /** 支付订单号 */
    @JsonProperty(value = "pay_order_no")
    private String orderNo;
}
