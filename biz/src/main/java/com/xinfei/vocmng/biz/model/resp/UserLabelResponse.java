/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2025 All Rights Reserved.
 */
package com.xinfei.vocmng.biz.model.resp;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 用户标签响应
 *
 * <AUTHOR>
 * @version $ UserLabelResponse, v 0.1 2025/5/15 18:30 shaohui.chen Exp $
 */
@Data
@ApiModel(description = "用户标签响应")
public class UserLabelResponse {

    @ApiModelProperty("系统标签列表")
    private List<String> systemLabelList;

    @ApiModelProperty("UDesk标签列表")
    private List<String> udeskLabelList;

    @ApiModelProperty("高风险用户标签")
    private boolean isHighRiskUser;

    @ApiModelProperty("易投诉用户标签")
    private boolean isEasyComplaintUser;

    @ApiModelProperty("疑似黑产用户标签")
    private boolean isBlackIndustryUser;
}
