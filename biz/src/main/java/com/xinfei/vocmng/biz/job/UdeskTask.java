/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xinfei.vocmng.biz.job;

import com.xinfei.vocmng.biz.component.RepaymentTaskManage;
import com.xinfei.vocmng.biz.service.UDeskService;
import com.xinfei.vocmng.biz.util.LocalDateTimeUtils;
import com.xinfei.vocmng.biz.util.RedisUtils;
import com.xinfei.vocmng.itl.util.LogUtil;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @version $ UdeskService, v 0.1 2024-12-25 17:22 pengming.liu Exp $
 */
@Component
@Slf4j
public class UdeskTask extends RepaymentTaskManage {

    @Resource
    private UDeskService uDeskService;

    @Value(value = "${udesk.Sync:false}")
    private Boolean isOpen;

    @Value(value = "${udesk.uDeskSync:0 0 4 * * ?}")
    private String cron;

    private static final String redisKey = "uDeskSync";

    @Override
    protected String getCron() {
        return cron;
    }

    @Override
    protected void processTask() {
        if (isOpen) {
            String startTime = LocalDateTimeUtils.format(LocalDateTime.now().minusDays(1).toLocalDate().atStartOfDay());
            String endTime = LocalDateTimeUtils.format(LocalDateTime.now().toLocalDate().atStartOfDay());
            log.info(LogUtil.infoLog("uDeskTask", "uDesk数据补充数据同步开始 startTime:{} endTime:{}", startTime, endTime));
            uDeskService.uDeskSyncAll(redisKey, startTime, endTime);
        }
    }
}