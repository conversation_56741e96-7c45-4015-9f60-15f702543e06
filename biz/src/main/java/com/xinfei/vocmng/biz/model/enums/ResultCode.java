package com.xinfei.vocmng.biz.model.enums;

/**
 * API返回码封装类
 * Created by macro on 2019/4/19.
 */
public enum ResultCode {
    SUCCESS(1, "操作成功"),
    FAILED(-1, "操作失败"),
    UNAUTHORIZED(401, "暂未登录或token已经过期"),
    FORBIDDEN(403, "没有相关权限"),
    ROUTE_NO_FOUND(404, "路由不存在"),
    SYSTEM_ERROR(500, "当前系统繁忙，请稍后再试"),
    UNKNOWN_ERROR(5000, "未知异常"),
    DINGTALK_ACCESSTOKEN_ERR(2000, "获取钉钉access token异常"),
    DINGTALK_CONTACTUSER_ERR(2001, "获取钉钉用户信息失败"),
    QUERY_GATEWAY_STATUS_ERROR(5001, "查询第三方服务状态异常"),
    CODE_180005(5002, "操作太频繁, 请稍后再试"),
    MAX_RETRY(5003, "重试失败次数过多"),
    VALIDATE_FAILED(4000, "缺少入参"),
    PARAM_INVALID(4001, "无效的入参"),
    QUERY_GATEWAY_ERROR(4002, "查询第三方服务异常"),
    QUERY_GATEWAY_CLIENT_ERROR(4003, "查询第三方服务客户端异常"),
    ;
    private final Long code;
    private final String message;

    private ResultCode(long code, String message) {
        this.code = code;
        this.message = message;
    }

    public long getCode() {
        return code;
    }

    public String getMessage() {
        return message;
    }

    /**
     * 查询不到，默认位置错误
     */
    public static ResultCode getByCode(Long code) {
        for (ResultCode enums : ResultCode.values()) {
            if (enums.getCode() == code) {
                return enums;
            }
        }
        return FAILED;
    }

    public static String getMessageByCode(Long code) {
        for (ResultCode enums : ResultCode.values()) {
            if (enums.getCode() == code) {
                return enums.getMessage();
            }
        }
        return FAILED.getMessage();
    }
}
