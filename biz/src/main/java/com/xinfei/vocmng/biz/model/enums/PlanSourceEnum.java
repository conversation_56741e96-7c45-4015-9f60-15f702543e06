/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xinfei.vocmng.biz.model.enums;

import lombok.Getter;

/**
 * <AUTHOR>
 * @version $ OfflineRefundMethodEnum, v 0.1 2024-05-21 14:38 junjie.yan Exp $
 */

@Getter
public enum PlanSourceEnum {
    VOCMNG("vocmng", "客服"),
    HUTTA("HUTTA", "催收");

    private final String code;
    private final String description;

    PlanSourceEnum(String code, String description) {
        this.code = code;
        this.description = description;
    }

}