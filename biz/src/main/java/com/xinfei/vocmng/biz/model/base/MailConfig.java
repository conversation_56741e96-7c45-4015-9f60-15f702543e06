/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xinfei.vocmng.biz.model.base;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 *
 * <AUTHOR>
 * @version $ MailConfig, v 0.1 2024/6/5 17:24 wancheng.qu Exp $
 */
@Data
public class MailConfig implements Serializable {

    private List<Config> config;

    @Data
    public static class Config {
        private String mail; //邮箱
        private String obj;  //1：用户 2： 资方
        private String password; //授权码
        private String app;
        private String port;
        private String name; //发送人名
        private String box; //抄送人

    }
}