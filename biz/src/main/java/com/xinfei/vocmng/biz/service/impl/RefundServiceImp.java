/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2023 All Rights Reserved.
 */
package com.xinfei.vocmng.biz.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.xinfei.lendtrade.facade.rr.ManageOrderListRequest;
import com.xinfei.lendtrade.facade.rr.dto.ManageOrderDetailDTO;
import com.xinfei.repaytrade.facade.rr.dto.RefundTrialDetail;
import com.xinfei.repaytrade.facade.rr.enums.FeeSubjectEnum;
import com.xinfei.repaytrade.facade.rr.request.RefundApplyRequest;
import com.xinfei.repaytrade.facade.rr.response.*;
import com.xinfei.vocmng.biz.config.VocConfig;
import com.xinfei.vocmng.biz.constants.ReqNoConstants;
import com.xinfei.vocmng.biz.log.DigestLogAnnotated;
import com.xinfei.vocmng.biz.mapstruct.RefundConverter;
import com.xinfei.vocmng.biz.model.base.ControlItemValue;
import com.xinfei.vocmng.biz.model.base.ControlRes;
import com.xinfei.vocmng.biz.model.base.PageResultResponse;
import com.xinfei.vocmng.biz.model.enums.*;
import com.xinfei.vocmng.biz.model.exception.IgnoreException;
import com.xinfei.vocmng.biz.model.exception.TechplayException;
import com.xinfei.vocmng.biz.model.req.*;
import com.xinfei.vocmng.biz.model.resp.CalRefundAmtManyResp;
import com.xinfei.vocmng.biz.model.resp.OrderRefundRecods;
import com.xinfei.vocmng.biz.model.resp.OverPayRecordsResp;
import com.xinfei.vocmng.biz.model.resp.RefundResp;
import com.xinfei.vocmng.biz.model.resp.RefundTrialResp;
import com.xinfei.vocmng.biz.rr.dto.RefundRequestDto;
import com.xinfei.vocmng.biz.rr.request.RefundFeeRatioProcessReq;
import com.xinfei.vocmng.biz.rr.response.CalRefundAmtManyResponse;
import com.xinfei.vocmng.biz.rr.response.RefundFeeRatioProcessResp;
import com.xinfei.vocmng.biz.service.ControlItemValueFactory;
import com.xinfei.vocmng.biz.service.EmployeeService;
import com.xinfei.vocmng.biz.strategy.dto.OrderRefundStrategyInput;
import com.xinfei.vocmng.biz.strategy.dto.StrategyExecutionResult;
import com.xinfei.vocmng.biz.util.LocalDateTimeUtils;
import com.xinfei.vocmng.biz.util.UserContextHolder;
import com.xinfei.vocmng.dal.dto.resp.ControlData;
import com.xinfei.vocmng.dal.dto.resp.FeeStrategyConfig;
import com.xinfei.vocmng.dal.mapper.RefundRequestMapper;
import com.xinfei.vocmng.dal.po.RefundRequest;
import com.xinfei.vocmng.itl.client.feign.CisExtFacadeClient;
import com.xinfei.vocmng.itl.client.feign.CisFacadeClient;
import com.xinfei.vocmng.itl.client.feign.LendQueryFacadeClient;
import com.xinfei.vocmng.itl.client.feign.RepayFacadeClient;
import com.xinfei.vocmng.itl.client.feign.impl.CstStartService;
import com.xinfei.vocmng.itl.client.feign.impl.RefundFacadeClientImpl;
import com.xinfei.vocmng.itl.client.feign.service.CisFacadeClientService;
import com.xinfei.vocmng.itl.client.feign.service.LendQueryClientService;
import com.xinfei.vocmng.itl.client.feign.service.RepayFacadeService;
import com.xinfei.vocmng.itl.rr.OverRefundApplyReq;
import com.xinfei.vocmng.itl.rr.RefundApplyCancelReq;
import com.xinfei.vocmng.itl.rr.RefundOrderRecordRes;
import com.xinfei.vocmng.itl.util.LogUtil;
import com.xyf.bank.dto.response.BankCardResponse;
import com.xyf.cis.query.facade.dto.standard.response.IdNoDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.io.IOException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.xinfei.vocmng.biz.constants.ReqNoConstants.getRequestNo;
import static com.xinfei.vocmng.biz.strategy.dto.OrderRefundProcessDto.FEE_AMOUNT_EXTRACTORS;

/**
 * <AUTHOR>
 * @version $ OrderBillServiceImp, v 0.1 2023-12-14 16:43 junjie.yan Exp $
 */
@Service
@Slf4j
public class RefundServiceImp {
    @Resource
    private RefundFacadeClientImpl refundFacadeClient;

    @Resource
    private ControlItemValueFactory factory;

    @Resource
    private CisExtFacadeClient cisExtFacadeClient;

    @Resource
    private CisFacadeClient cisFacadeClient;

    @Autowired
    private EmployeeService employeeService;

    @Resource
    private RefundRequestMapper refundRequestMapper;

    @Resource
    private CstStartService cstStartService;

    @Resource
    private CisFacadeClientService cisFacadeClientService;

    @Resource
    private LendQueryClientService lendQueryClientService;

    @Resource
    private RepayFacadeService repayFacadeService;

    @Resource
    private RefundProfitStrategy refundProfitStrategy;

    @Resource
    private LendQueryFacadeClient lendQueryFacadeClient;

    @Resource
    private RefundServiceHandle refundServiceHandle;

    @Resource
    private RepayFacadeClient repayFacadeClient;

    @Autowired
    private VocConfig vocConfig;

    @Resource
    private OrderRefundStrategyServiceImpl orderRefundStrategyService;


    public List<RefundResp> calculateRefundAmount(List<RefundReq> request) {
        List<RefundResp> respList = new ArrayList<>();
        ManageOrderListRequest manageOrderListRequest = new ManageOrderListRequest();
        manageOrderListRequest.setLoanNos(request.stream()
                .map(RefundReq::getLoanNo)
                .collect(Collectors.toList()));
        List<ManageOrderDetailDTO> dtoList = lendQueryFacadeClient.getOrderList(manageOrderListRequest).getPageList();
        for (RefundReq req : request) {
            RefundResp resp = new RefundResp();
            String loanNo = req.getLoanNo();
            try {
                //计算订单/账单总可退款金额
                List<FeeSubjectEnum> allFeeSubjects = getAllFeeSubjectEnums(req.getOrderType());
                CalculateRefundAmountResponse responseAll = refundFacadeClient.calculateRefundAmount(loanNo, allFeeSubjects, req.getBillNo(), req.getRepaymentNos());
                resp = RefundConverter.INSTANCE.calculateRefundToRefundResp(responseAll);
                for (ManageOrderDetailDTO dto : dtoList) {
                    if (loanNo.equals(dto.getLoanNo())) {
                        resp.setOrderNo(dto.getLoanReqNo());
                    }
                }
                resp.setGuaranteeFee(resp.getFee1Amt().add(resp.getFee2Amt()));
                resp.setLateFee(resp.getOintAmt().add(resp.getFee3Amt().add(resp.getFee6Amt())));
                resp.setCanRefundAmtLower(BigDecimal.ZERO);
                resp.setCanRefundAmtUpper(BigDecimal.ZERO);
                resp.setLoanNo(loanNo);
                resp.setBillNo(req.getBillNo());

                //获取坐席可退费项
                List<FeeSubjectEnum> reductionFeeList = refundServiceHandle.getFeeSubjectEnums(req.getOrderType(), req.getLoanNo());

                //现金订单，必须有可退费项
                if (OrderType.MAIN.getCode().equals(req.getOrderType()) && CollectionUtils.isEmpty(reductionFeeList)) {
                    respList.add(resp);
                    continue;
                }

                List<ControlData> controlDataList = UserContextHolder.getUserContext().getControlDataList();
                //营收订单，必须有百分比费控
                if (OrderType.PROFIT.getCode().equals(req.getOrderType())) {
                    controlDataList = controlDataList.stream().filter(r -> r.getControlScene() == 3 && r.getControlChildType() == 8).collect(Collectors.toList());
                    if (CollectionUtils.isEmpty(controlDataList)) {
                        respList.add(resp);
                        continue;
                    }
                }

                CalculateRefundAmountResponse response = refundFacadeClient.calculateRefundAmount(loanNo, reductionFeeList, req.getBillNo(), req.getRepaymentNos());
                if (response == null) {
                    respList.add(resp);
                    continue;
                }

                //营收订单上下限
                if (controlDataList.size() == 1 && controlDataList.get(0).getControlChildType() == 8 && response.getCanRefundAmt() != null) {
                    BigDecimal actRepayAmt = repayFacadeService.getActRepayAmt(loanNo);
                    ControlData controlData = controlDataList.get(0);
                    ControlEnum type = ControlEnum.getByControlChildType(controlData.getControlChildType());
                    ControlItemValue<?, ?, ?> values;
                    try {
                        values = factory.create(controlData.getControlValue(), type);
                        BigDecimal historyAmount = BigDecimal.ZERO;
                        RefundedAmountResponse refundedAmountResponse = refundFacadeClient.refundedAmount(loanNo);
                        if (refundedAmountResponse != null) {
                            historyAmount = historyAmount.add(refundedAmountResponse.getRefundTotalAmount());
                            historyAmount = historyAmount.add(refundedAmountResponse.getReductionTotalAmount());
                        }
                        ControlRes<BigDecimal, BigDecimal> result = refundProfitStrategy.calculateAmount((BigDecimal) values.getO(), (BigDecimal) values.getT(), actRepayAmt, historyAmount, response.getCanRefundAmt());
                        resp.setCanRefundAmtLower(result.getLeft());
                        resp.setCanRefundAmtUpper(result.getRight());
                    } catch (IOException e) {
                        log.error(LogUtil.clientErrorLog("RefundServiceImp", "calculateRefundAmount", null, null, "营收订单可退款费项获取错误"), e);
                        throw new TechplayException(TechplayErrDtlEnum.REFUND_ERROR);
                    }
                }

                //现金订单上限
                if (OrderType.MAIN.getCode().equals(req.getOrderType())) {
                    resp.setCanRefundAmtUpper(response.getCanRefundAmt());
                }

                if (vocConfig.getIsNewRefund() && OrderType.MAIN.getCode().equals(req.getOrderType())) {
                    OrderRefundStrategyInput input = OrderRefundStrategyInput
                            .builder()
                            .loanNo(loanNo)
                            .build();
                    StrategyExecutionResult<FeeStrategyConfig> strategyExecutionResult = orderRefundStrategyService.executeStrategy(FeeStrategyEnum.ORDER_REFUND, input);
                    Map<String, String> result = strategyExecutionResult.getResult();

                    BigDecimal totalRefundAmount = BigDecimal.ZERO;
                    for (Map.Entry<String, String> entry : result.entrySet()) {
                        String key = entry.getKey();
                        BigDecimal value;
                        try {
                            value = new BigDecimal(entry.getValue());
                        } catch (NumberFormatException e) {
                            log.warn(LogUtil.infoLog("Error parsing BigDecimal for key " + key + ", value: " + entry.getValue()));
                            continue; // 跳过这个条目
                        }

                        value = value.multiply(new BigDecimal("0.01"));
                        Function<RefundResp, BigDecimal> amountExtractor = FEE_AMOUNT_EXTRACTORS.get(key);
                        if (amountExtractor != null) {
                            BigDecimal baseAmountFromResp = amountExtractor.apply(resp);
                            totalRefundAmount = totalRefundAmount.add(baseAmountFromResp.multiply(value));
                        }
                    }
                    resp.setCanRefundAmtLower(BigDecimal.ZERO);
                    resp.setCanRefundAmtUpper(totalRefundAmount);
                }

                //判断是否存在审核中金额，若有，可退金额需减去审核中金额
                List<RefundRequest> refundRequests = refundRequestMapper.queryRefundRequestByLoanNoStatus(loanNo, req.getId());
                if (CollectionUtils.isNotEmpty(refundRequests)) {
                    resp.setReviewRefundAmt(refundRequests.stream().map(RefundRequest::getRefundAmount).collect(Collectors.toList())
                            .stream().map(r -> new BigDecimal(r).multiply(new BigDecimal("0.01"))).collect(Collectors.toList())
                            .stream().reduce(BigDecimal.ZERO, BigDecimal::add));
                }
                if (resp.getReviewRefundAmt() != null) {
                    resp.setCanRefundAmt(resp.getCanRefundAmt().subtract(resp.getReviewRefundAmt()));
                }

                //审核单赋值退款金额
                if (req.getId() != null) {
                    RefundRequest refundRequest = refundRequestMapper.selectById(req.getId());
                    if (refundRequest != null && refundRequest.getRefundAmount() != null) {
                        resp.setRefundAmt(new BigDecimal(refundRequest.getRefundAmount()).multiply(new BigDecimal("0.01")));
                    }
                }
                respList.add(resp);
            } catch (Exception e) {
                resp.setLoanNo(loanNo);
                resp.setReason(e.getMessage());
                respList.add(resp);
            }
        }
        return respList;
    }

    public List<RefundTrialResp> refundTrial(List<RefundTrialReq> request) {

        List<RefundTrialResp> resp = new ArrayList<>();
        for (RefundTrialReq req : request) {
            List<FeeSubjectEnum> reductionFeeList;
            RefundTrialResp refundTrialResp = new RefundTrialResp();
            try {
                if (req.getIsReview()) {
                    //获取所有可退费项
                    reductionFeeList = getAllFeeSubjectEnums(req.getOrderType());
                } else {
                    //获取坐席可退费项
                    reductionFeeList = refundServiceHandle.getFeeSubjectEnums(req.getOrderType(), req.getLoanNo());
                }
                RefundTrialResponse response = refundFacadeClient.refundTrial(req.getLoanNo(), reductionFeeList, req.getBillNo(), req.getTotalRefundAmt(), req.getRefundType(), req.getRepaymentNos());
                if (response == null) {
                    continue;
                }

                String name = "";
                if (StringUtils.isNotEmpty(response.getCustNo())) {
                    IdNoDTO idNoDTO = cisFacadeClient.queryIdNoByCustNo(response.getCustNo());
                    name = idNoDTO.getName();
                }

                for (RefundTrialDetail refundTrialDetail : response.getRefundTrialDetails()) {
                    refundTrialResp = RefundConverter.INSTANCE.refundTrialDetailToTrialResp(refundTrialDetail);
                    if (StringUtils.isNotEmpty(refundTrialResp.getBankCardId())) {
                        BankCardResponse bankCardResponse = cisExtFacadeClient.queryBankCardInfoById(response.getCustNo(), Long.parseLong(refundTrialResp.getBankCardId()));
                        if (bankCardResponse != null) {
                            refundTrialResp.setBankName(bankCardResponse.getBankName());
                            refundTrialResp.setBankCardNo(bankCardResponse.getCardNo());
                            refundTrialResp.setUserName(name);
                        }
                    }
                    refundTrialResp.setOrderNo(req.getOrderNo());
                    refundTrialResp.setLoanNo(req.getLoanNo());

                    if (refundTrialResp.getRefundAmt().compareTo(BigDecimal.ZERO) > 0) {
                        resp.add(refundTrialResp);
                    }

                }
            } catch (Exception e) {
                refundTrialResp.setOrderNo(req.getOrderNo());
                refundTrialResp.setLoanNo(req.getLoanNo());
                refundTrialResp.setReason(e.getMessage());
                resp.add(refundTrialResp);
            }
        }
        resp.sort((r1, r2) -> {
            return "OFFLINE_REFUND".equals(r1.getRefundType())
                    ? ("OFFLINE_REFUND".equals(r2.getRefundType()) ? 0 : -1)
                    : ("OFFLINE_REFUND".equals(r2.getRefundType()) ? 1 : 0);
        });

        return resp;
    }

    public List<RefundFeeRatioProcessResp> refundRateCalculation(List<RefundFeeRatioProcessReq> req) {
        List<RefundFeeRatioProcessResp> refundFeeRatioProcessResps = new ArrayList<>();
        for (RefundFeeRatioProcessReq feeRatioProcessReq : req) {
            BigDecimal targetFeeRatio = null;
            if (feeRatioProcessReq.getTargetFeeRatio() != null) {
                targetFeeRatio = feeRatioProcessReq.getTargetFeeRatio().multiply(new BigDecimal("0.01"));
            }

            RefundCalculateResponse response = repayFacadeClient.refundCalculate(feeRatioProcessReq.getLoanNo(), targetFeeRatio, feeRatioProcessReq.getTargetRefundAmt());

            if (response != null) {
                RefundFeeRatioProcessResp resp = new RefundFeeRatioProcessResp();
                resp.setSuggestedRefundAmt(response.getSuggestedRefundAmt());
                resp.setLoanNo(feeRatioProcessReq.getLoanNo());
                resp.setRefundAmtInProcess(response.getRefundAmtInProcess());
                resp.setFeeRatioAfterRefund(response.getFeeRatioAfterRefund() != null ? response.getFeeRatioAfterRefund().multiply(new BigDecimal("100")) : null);
                refundFeeRatioProcessResps.add(resp);
            }
        }

        return refundFeeRatioProcessResps;
    }

    @DigestLogAnnotated("refundApply")
    public String refundApply(List<RefundApplyReq> request) {
        StringBuilder refundMsg = new StringBuilder("操作成功");
        String creator = employeeService.getUserNameForIdentify(UserContextHolder.getUserIdentify());
        for (RefundApplyReq req : request) {
            try {
                refundServiceHandle.refundApplyHandle(req, creator);
            } catch (Exception e) {
                refundMsg.setLength(0);
                refundMsg.append(req.getLoanNo()).append("订单操作失败：").append(e.getMessage());
            }
        }
        return refundMsg.toString();
    }

    @Transactional
    public Boolean overPayRefundApply(OverPayRefundReq request) {
        String creator = employeeService.getUserNameForIdentify(UserContextHolder.getUserIdentify());
        RefundRequest refundRequest = RefundConverter.INSTANCE.overPayRefundReqToRefundRequest(request);
        refundRequest.setRefundAmount(request.getRefundAmount().multiply(new BigDecimal("100")).longValue());
        refundRequest.setCreator(UserContextHolder.getUserIdentify());
        refundRequestMapper.insert(refundRequest);

        OverRefundApplyReq refundApplyReq = new OverRefundApplyReq();
        refundApplyReq.setAmount(request.getRefundAmount().multiply(new BigDecimal("100")).longValue());
        refundApplyReq.setApplyOp(creator);
        refundApplyReq.setApplyReson(request.getRefundReason());
        refundApplyReq.setMqTag("tg_overRefund");
        refundApplyReq.setChannelCode(request.getChannelCode());
        refundApplyReq.setPayeeAccNo(request.getOfflineRefundAccount());
        refundApplyReq.setPayeeName(request.getOfflineRefundUserName());
        refundApplyReq.setRefundBankName(request.getOfflineRefundBank());
        refundApplyReq.setRefundChannelCode("ALIPAY".equals(request.getOfflineRefundMethod()) ? "zfb" : "bank");
        refundApplyReq.setTransNo(request.getTransNo());
        refundApplyReq.setReqNo(getRequestNo(ReqNoConstants.VOCORA, refundRequest.getId()));
        return cstStartService.offlineRefundApply(refundApplyReq);
    }

    public List<OrderRefundRecods> orderRefundApplyRecords(OrderRefundRecordsReq req) {
        List<OrderRefundRecods> recodsList = new ArrayList<>();
        //查询客服系统退款单
        List<OrderRefundRecods> list = getRefundRequests(req.getLoanNos());
        //查询还款引擎退款记录
        List<OrderRefundRecordsResp> resps = getRefundRecordsResp(req.getLoanNos(), req.getRefundStatus());
        if (CollectionUtils.isEmpty(resps) && CollectionUtils.isEmpty(list)) {
            return recodsList;
        }

        for (String loanNo : req.getLoanNos()) {
            //申请单
            List<OrderRefundRecods> refundRequestsLoan = new ArrayList<>();
            if (CollectionUtils.isNotEmpty(list)) {
                refundRequestsLoan = list.stream().filter(r -> r.getLoanNo().equals(loanNo)).collect(Collectors.toList());
            }
            List<String> applyRefundOrderIds = refundRequestsLoan.stream().map(OrderRefundRecods::getRefundOrderId).collect(Collectors.toList());

            //退款记录
            List<OrderRefundRecordsResp> refundRecordsLoan = new ArrayList<>();
            if (CollectionUtils.isNotEmpty(resps)) {
                refundRecordsLoan = resps.stream().filter(r -> r.getLoanNo().equals(loanNo)).collect(Collectors.toList());
            }
            List<String> refundOrderIds = refundRecordsLoan.stream().map(OrderRefundRecordsResp::getRefundOrderId).collect(Collectors.toList());

            //有申请单，没有退款记录
            List<OrderRefundRecods> result = new ArrayList<>();
            if (StringUtils.isEmpty(req.getRefundStatus())) {
                result = refundRequestsLoan.stream().filter(r -> StringUtils.isEmpty(r.getRefundOrderId())).collect(Collectors.toList());
            }

            //有申请单，有退款记录
            List<OrderRefundRecods> hasLog = refundRequestsLoan.stream().filter(r -> refundOrderIds.contains(r.getRefundOrderId())).collect(Collectors.toList());
            for (OrderRefundRecods refundRequest : hasLog) {
                List<OrderRefundRecordsResp> orderRefundRecordsResps = resps.stream().filter(r -> r.getRefundOrderId().equals(refundRequest.getRefundOrderId())).collect(Collectors.toList());
                refundRequest.setRecordsResps(orderRefundRecordsResps);
                result.add(refundRequest);
            }

            //无申请单，有退款记录
            List<OrderRefundRecordsResp> noApply = refundRecordsLoan.stream().filter(r -> !applyRefundOrderIds.contains(r.getRefundOrderId())).collect(Collectors.toList());
            Map<String, List<OrderRefundRecordsResp>> respMap = noApply.stream().collect(Collectors.groupingBy(OrderRefundRecordsResp::getRefundOrderId));
            for (Map.Entry<String, List<OrderRefundRecordsResp>> entry : respMap.entrySet()) {
                OrderRefundRecods orderRefundRecord = new OrderRefundRecods();
                OrderRefundRecordsResp recordsResp = entry.getValue().get(0);
                orderRefundRecord.setLoanNo(recordsResp.getLoanNo());
                orderRefundRecord.setOrderNo(lendQueryClientService.getOrderNoByLoanNo(recordsResp.getLoanNo()).getLoanReqNo());
                orderRefundRecord.setRefundType(1);
                orderRefundRecord.setRefundTime(recordsResp.getCreatedTime());
                orderRefundRecord.setCrateTime(recordsResp.getCreatedTime());
                orderRefundRecord.setCreator(recordsResp.getCreatedBy());
                orderRefundRecord.setRefundOrderId(recordsResp.getRefundOrderId());
                orderRefundRecord.setRefundReason(recordsResp.getRefundReason());
                orderRefundRecord.setRecordsResps(entry.getValue());
                orderRefundRecord.setRefundAmount(entry.getValue().stream().map(OrderRefundRecordsResp::getRefundAmt).reduce(BigDecimal.ZERO, BigDecimal::add));
                result.add(orderRefundRecord);
            }
            recodsList.addAll(result);
        }
        setRefundRequestStatus(recodsList);
        recodsList.sort((o1, o2) -> o2.getCrateTime().compareTo(o1.getCrateTime()));
        return recodsList;
    }

    private void setRefundRequestStatus(List<OrderRefundRecods> list) {
        for (OrderRefundRecods orderRefundRecods : list) {

            if (CollectionUtils.isEmpty(orderRefundRecods.getRecordsResps())) {
                orderRefundRecods.setStatusStr(OrderRefundStatusEnum.getDescription(orderRefundRecods.getStatus()));
                continue;
            }

            List<String> payStatus = orderRefundRecods.getRecordsResps().stream().map(OrderRefundRecordsResp::getRefundStatus).collect(Collectors.toList());
            if (payStatus.contains(RefundInstructionStatusEnum.FAILURE.getCode()) || payStatus.contains(RefundInstructionStatusEnum.REJECT.getCode())) {
                orderRefundRecods.setStatusStr(RefundInstructionStatusEnum.FAILURE.getOrderDescription());
                continue;
            }
            if (payStatus.contains(RefundInstructionStatusEnum.PENDING.getCode()) || payStatus.contains(RefundInstructionStatusEnum.PROCESSING.getCode())) {
                orderRefundRecods.setStatusStr(RefundInstructionStatusEnum.PENDING.getOrderDescription());
                continue;
            }
            if (payStatus.contains(RefundInstructionStatusEnum.CANCEL.getCode())) {
                orderRefundRecods.setStatusStr(RefundInstructionStatusEnum.CANCEL.getOrderDescription());
                continue;
            }
            orderRefundRecods.setStatusStr(RefundInstructionStatusEnum.SUCCESS.getOrderDescription());
        }
    }

    private List<OrderRefundRecordsResp> getRefundRecordsResp(List<String> loanNos, String status) {
        //查询还款引擎退款记录
        List<OrderRefundRecordsResp> resps = new ArrayList<>();
        QueryRefundApplyResponse response = refundFacadeClient.queryRefundApply(null, loanNos, status);
        if (response == null || CollectionUtils.isEmpty(response.getRefundInstructionInfos())) {
            return resps;
        }

        //获取用户姓名
        String name = "";
        if (StringUtils.isNotEmpty(response.getRefundInstructionInfos().get(0).getCustNo())) {
            IdNoDTO idNoDTO = cisFacadeClient.queryIdNoByCustNo(response.getRefundInstructionInfos().get(0).getCustNo());
            name = idNoDTO.getName();
        }

        for (QueryRefundApplyResponse.RefundInstructionInfo instructionInfo : response.getRefundInstructionInfos()) {
            OrderRefundRecordsResp recordsResp = RefundConverter.INSTANCE.instructionInfoToApplyRecords(instructionInfo);
            if (StringUtils.isNotEmpty(instructionInfo.getBankCardId())) {
                BankCardResponse bankCardResponse = cisExtFacadeClient.queryBankCardInfoById(response.getCustNo(), Long.parseLong(instructionInfo.getBankCardId()));
                if (bankCardResponse != null) {
                    recordsResp.setRefundBank(bankCardResponse.getBankName());
                    recordsResp.setRefundAccount(bankCardResponse.getCardNo());
                    recordsResp.setRefundUserName(name);
                }
            }
            resps.add(recordsResp);
        }

        return resps;
    }

    private List<OrderRefundRecods> getRefundRequests(List<String> loanNos) {
        List<OrderRefundRecods> list = new ArrayList<>();
        List<RefundRequest> refundRequests = refundRequestMapper.queryRefundRequestByLoanNo(loanNos);
        for (RefundRequest refundRequest : refundRequests) {
            OrderRefundRecods orderRefundRecods = RefundConverter.INSTANCE.refundRequestToOrderRefundRecods(refundRequest);
            orderRefundRecods.setCreator(employeeService.getUserNameForIdentify(refundRequest.getCreator()));
            orderRefundRecods.setRefundAmount(orderRefundRecods.getRefundAmount().multiply(new BigDecimal("0.01")));
            list.add(orderRefundRecods);
        }
        return list;
    }

    public PageResultResponse<RefundRequestDto> orderRefundReview(OrderRefundReviewReq req) {
        List<RefundRequestDto> refundRequestDtos = new ArrayList<>();
        LambdaQueryWrapper<RefundRequest> wrapper = new LambdaQueryWrapper<>();
        getRefundRequestReviewWrapper(req, wrapper);
        IPage<RefundRequest> page = new Page<>(req.getCurrentPage(), req.getPageSize());
        IPage<RefundRequest> pageList = refundRequestMapper.selectPage(page, wrapper);
        for (RefundRequest refundRequest : pageList.getRecords()) {
            RefundRequestDto refundRequestDto = RefundConverter.INSTANCE.refundRequestToRefundRequestDto(refundRequest);
            refundRequestDto.setRefundAmount(new BigDecimal(refundRequest.getRefundAmount()).multiply(new BigDecimal("0.01")));
            if (StringUtils.isNotEmpty(refundRequestDto.getReviewer())) {
                refundRequestDto.setReviewer(employeeService.getUserNameForIdentify(refundRequestDto.getReviewer()));
            }
            if (StringUtils.isNotEmpty(refundRequestDto.getCreator())) {
                refundRequestDto.setCreator(employeeService.getUserNameForIdentify(refundRequestDto.getCreator()));
            }
            refundRequestDtos.add(refundRequestDto);
        }
        return new PageResultResponse<>(refundRequestDtos, pageList.getCurrent(), pageList.getSize(), pageList.getTotal());
    }

    private void getRefundRequestReviewWrapper(OrderRefundReviewReq req, LambdaQueryWrapper<RefundRequest> wrapper) {
        wrapper.eq(Objects.nonNull(req.getId()), RefundRequest::getId, req.getId());
        wrapper.eq(StringUtils.isNotBlank(req.getOrderNo()), RefundRequest::getOrderNo, req.getOrderNo());
        wrapper.in(CollectionUtils.isNotEmpty(req.getStatus()), RefundRequest::getStatus, req.getStatus());
        wrapper.ne(RefundRequest::getRequestType, 3);
        wrapper.eq(RefundRequest::getIsDel, 0);
        wrapper.ne(RefundRequest::getStatus, 0);
        wrapper.orderByDesc(RefundRequest::getCrateTime);
    }

    @Transactional
    public Boolean reviewOrderRefund(ReviewOrderRefundReq req) {

        RefundRequest refundRequest = refundRequestMapper.selectById(req.getId());
        if (refundRequest.getStatus() != 1) {
            throw new IgnoreException(TechplayErrDtlEnum.REFUND_ERROR, "只有审核中申请单可操作审批");
        }

        if (refundRequest.getStatus().equals(req.getStatus()) || (req.getStatus() != 2 && req.getStatus() != 3 && req.getStatus() != 4)) {
            throw new IgnoreException(TechplayErrDtlEnum.REFUND_ERROR, "请求审批状态有误");
        }

        refundRequest.setStatus(req.getStatus());
        refundRequest.setReviewReason(req.getReason());
        refundRequest.setReviewer(UserContextHolder.getUserIdentify());

        LocalDateTime executeTime;
        if (refundRequest.getStatus() == 2) {
            List<FeeSubjectEnum> reductionFeeList = getAllFeeSubjectEnums(refundRequest.getOrderType());
            RefundApplyRequest refundApplyRequest = new RefundApplyRequest();
            refundApplyRequest.setLoanNo(refundRequest.getLoanNo());
            refundApplyRequest.setPlanNo(refundRequest.getBillNo());
            refundApplyRequest.setRefundType(RefundTypeEnum.getCodeStrByCode(refundRequest.getRefundType()));
            refundApplyRequest.setTotalRefundAmt(new BigDecimal(refundRequest.getRefundAmount()).multiply(new BigDecimal("0.01")));
            if (refundRequest.getRefundTime().isBefore(LocalDateTime.now())) {
                executeTime = LocalDateTime.now().plusMinutes(1L);
            } else {
                executeTime = refundRequest.getRefundTime();
            }
            refundApplyRequest.setPlanExecuteTime(LocalDateTimeUtils.parseDateByLocalDateTime(executeTime));
            refundApplyRequest.setRefundReason(refundRequest.getRefundReason());
            refundApplyRequest.setOfflineRefundMethod(OfflineRefundMethodEnum.getCodeStrByCode(refundRequest.getOfflineRefundMethod()));
            refundApplyRequest.setOfflineRefundAccount(refundRequest.getOfflineRefundAccount());
            refundApplyRequest.setOfflineRefundBank(refundRequest.getOfflineRefundBank());
            refundApplyRequest.setOfflineRefundUserName(refundRequest.getOfflineRefundUserName());
            refundApplyRequest.setRefundFeeList(reductionFeeList);
            refundApplyRequest.setCreatedBy(employeeService.getUserNameForIdentify(refundRequest.getCreator()));
            refundApplyRequest.setRequestNo(getRequestNo(ReqNoConstants.VOCRA, refundRequest.getId()));
            getDecryptInfo(refundApplyRequest);

            RefundApplyResponse response = refundFacadeClient.refundApply(refundApplyRequest);
            if (response != null) {
                refundRequest.setRefundOrderId(response.getRefundOrderId());
                refundRequest.setRefundTime(executeTime);
            }
        }
        refundRequest.setUpdateTime(LocalDateTime.now());
        refundRequestMapper.updateById(refundRequest);
        return true;
    }

    private void getDecryptInfo(RefundApplyRequest refundApplyRequest) {
        if (StringUtils.isNotEmpty(refundApplyRequest.getOfflineRefundAccount())) {
            refundApplyRequest.setOfflineRefundAccount(cisFacadeClientService.getDecryptByField("bankcard", Collections.singletonList(refundApplyRequest.getOfflineRefundAccount())));
        }

        if (StringUtils.isNotEmpty(refundApplyRequest.getOfflineRefundUserName())) {
            refundApplyRequest.setOfflineRefundUserName(cisFacadeClientService.getDecryptByField("name", Collections.singletonList(refundApplyRequest.getOfflineRefundUserName())));
        }
    }


    public RefundRightNowResponse refundRightNow(RefundRightNowReq req) {
        return refundFacadeClient.refundRightNow(req.getRefundInstructionNo(), employeeService.getUserNameForIdentify(UserContextHolder.getUserIdentify()));
    }

    public RefundCancelResponse refundCancel(RefundCancellationReq req) {
        return refundFacadeClient.refundCancel(req.getRefundInstructionNo(), employeeService.getUserNameForIdentify(UserContextHolder.getUserIdentify()));
    }

    public Boolean overPayCancellation(OverPayCancellationReq req) {
        RefundApplyCancelReq refundApplyCancelReq = RefundConverter.INSTANCE.reqToRefundApplyCancelReq(req);
        refundApplyCancelReq.setAmount(req.getAmount().multiply(new BigDecimal("100")).longValue());
        refundApplyCancelReq.setReqNo(req.getOriReqNo());
        return cstStartService.refundApplyCancel(refundApplyCancelReq);
    }

    public List<OverPayRecordsResp> overPayRecords(OverPayRecordsReq req) {
        List<OverPayRecordsResp> overPayRecordsResps = new ArrayList<>();
        List<RefundOrderRecordRes> recordRes = cstStartService.queryRefundOrderRecordListByTransNo(req.getTransNo(), req.getChannelCode());
        if (CollectionUtils.isEmpty(recordRes)) {
            return overPayRecordsResps;
        }

        for (RefundOrderRecordRes refundOrderRecordRes : recordRes) {
            OverPayRecordsResp recordsResp = RefundConverter.INSTANCE.recordResToOverPayRecords(refundOrderRecordRes);
            recordsResp.setRefundAmount(recordsResp.getRefundAmount().multiply(new BigDecimal("0.01")));
            overPayRecordsResps.add(recordsResp);
        }
        return overPayRecordsResps;
    }


    /**
     * 所有退款可退费项
     *
     * @return
     */
    private List<FeeSubjectEnum> getAllFeeSubjectEnums(String orderType) {

        List<FeeSubjectEnum> reductionFeeList = new ArrayList<>();

        if (OrderType.PROFIT.getCode().equals(orderType)) {
            return reductionFeeList;
        }

        reductionFeeList.add(FeeSubjectEnum.FEE1);
        reductionFeeList.add(FeeSubjectEnum.FEE2);
        reductionFeeList.add(FeeSubjectEnum.OINT_AMT);
        reductionFeeList.add(FeeSubjectEnum.FEE3);
        reductionFeeList.add(FeeSubjectEnum.FEE6);
        reductionFeeList.add(FeeSubjectEnum.FEE4);
        reductionFeeList.add(FeeSubjectEnum.PRIN_AMT);
        reductionFeeList.add(FeeSubjectEnum.INT_AMT);

        return reductionFeeList;
    }

    public List<CalRefundAmtManyResponse> calRefundAmtMany(List<CalRefundAmtManyResp> calRefundAmtManyRespList) {

        //按可退金额从小到大排序
        List<CalRefundAmtManyResp> sortedList = new ArrayList<>(calRefundAmtManyRespList);
        sortedList.sort(Comparator.comparing(CalRefundAmtManyResp::getRefundAmtUpper));

        //初始化每个订单的已退金额
        Map<String, BigDecimal> refundMap = new LinkedHashMap<>();
        for (CalRefundAmtManyResp resp : sortedList) {
            refundMap.put(resp.getLoanNo()+resp.getOrderNo()+resp.getBillNo()+resp.getVipCardId(), BigDecimal.ZERO);
        }

        BigDecimal remainAmt = calRefundAmtManyRespList.get(0).getTotalRefundAmt();
        int remainCount = sortedList.size();

        while (remainAmt.compareTo(BigDecimal.ZERO) > 0 && remainCount > 0) {
            //计算当前平均可退额
            BigDecimal avg = remainAmt.divide(BigDecimal.valueOf(remainCount), 2, RoundingMode.HALF_UP);

            boolean hasFill = false;
            for (CalRefundAmtManyResp resp : sortedList) {
                String orderNo =resp.getLoanNo()+resp.getOrderNo()+resp.getBillNo();
                BigDecimal upper = resp.getRefundAmtUpper();
                BigDecimal already = refundMap.get(resp.getLoanNo()+resp.getOrderNo()+resp.getBillNo()+resp.getVipCardId());

                // 跳过已分配满的订单
                if (already.compareTo(upper) >= 0) continue;

                BigDecimal remainForThis = upper.subtract(already);
                if (remainForThis.compareTo(avg) <= 0) {
                    // 情况1：可退余额 ≤ 当前平均，将剩余可退金额全退
                    refundMap.put(orderNo+resp.getVipCardId(), upper);
                    remainAmt = remainAmt.subtract(remainForThis);
                    remainCount--;
                    hasFill = true;
                }
            }

            if (!hasFill) {
                BigDecimal allocated = BigDecimal.ZERO;
                int idx = 0;
                for (CalRefundAmtManyResp resp : sortedList) {
                    String orderNo = resp.getLoanNo()+resp.getOrderNo()+resp.getBillNo()+resp.getVipCardId();
                    BigDecimal upper = resp.getRefundAmtUpper();
                    BigDecimal already = refundMap.get(orderNo);

                    if (already.compareTo(upper) >= 0) continue;

                    if (idx == remainCount - 1) {
                        // 最后一笔补足误差
                        BigDecimal lastAmt = remainAmt.subtract(allocated).setScale(2, RoundingMode.HALF_UP);
                        refundMap.put(orderNo, already.add(lastAmt));
                    } else {
                        refundMap.put(orderNo, already.add(avg));
                        allocated = allocated.add(avg);
                    }
                    idx++;
                }
                break;
            }
        }

        List<CalRefundAmtManyResponse> result = new ArrayList<>();
        for (CalRefundAmtManyResp resp : sortedList) {
            CalRefundAmtManyResponse response = new CalRefundAmtManyResponse();
            response.setRefundAmt(refundMap.get(resp.getLoanNo()+resp.getOrderNo()+resp.getBillNo()+resp.getVipCardId()));
            response.setBillNo(resp.getBillNo());
            response.setOrderNo(resp.getOrderNo());
            response.setVipCardId(resp.getVipCardId());
            response.setLoanNo(resp.getLoanNo());
            result.add(response);        }
        return result;
    }
}