/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2023 All Rights Reserved.
 */
package com.xinfei.vocmng.biz.model.resp;

import com.xinfei.vocmng.dal.dto.resp.ControlData;
import com.xinfei.vocmng.dal.dto.resp.FeeStrategyConfig;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 *
 * <AUTHOR>
 * @version $ UserInfo, v 0.1 2023/12/23 16:52 wancheng.qu Exp $
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class UserInfo implements Serializable {

    private String userIdentify;

    private String role;

    private Long id;

    private List<String> dataAuth;

    private List<String> resourceAuth;

    private Integer state;

    private List<ControlData> controlDataList;

    private List<FeeStrategyConfig> feeStrategyConfigs;

    private String name;

    public UserInfo(String userIdentify) {
        this.userIdentify = userIdentify;
    }
}