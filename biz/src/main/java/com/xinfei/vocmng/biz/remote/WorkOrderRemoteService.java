package com.xinfei.vocmng.biz.remote;

import com.xinfei.vocmng.biz.model.base.PageResultResponse;
import com.xinfei.vocmng.biz.rr.dto.WorkOrderDetailDto;
import com.xinfei.vocmng.biz.rr.dto.WorkOrderSimpleDto;
import com.xinfei.vocmng.biz.rr.request.QueryWorkOrderMobileRequest;
import com.xinfei.vocmng.biz.rr.request.QueryWorkOrderRequest;
import com.xinfei.vocmng.biz.rr.request.QueryWorkOrderUrlRequest;
import com.xinfei.vocmng.itl.rr.AddFeedbackRequest;
import com.xinfei.vocmng.itl.rr.FinishTaskRequest;

/**
 * <AUTHOR>
 * @version $ WorkOrderRemoteService, v 0.1 2024/1/15 20:09 qu.lu Exp $
 */
public interface WorkOrderRemoteService {
    /**
     * 查询工单明细信息
     * @param request
     * @return
     */
    PageResultResponse<WorkOrderDetailDto> queryWorkOrderDetail(QueryWorkOrderRequest request);

    /**
     * 查询工单明细信息
     * @param request
     * @return
     */
    PageResultResponse<WorkOrderDetailDto> queryWorkMobileDetail(QueryWorkOrderMobileRequest request);

    /**
     * 查询工单跳转链接信息
     * @param request
     * @return
     */
    WorkOrderSimpleDto loadWorkOrderUrl(QueryWorkOrderUrlRequest request);

    /**
     * 添加工单反馈
     * @param request
     * @return
     */
    void addWorkOrderFeedback(AddFeedbackRequest request);

    /**
     * 工单强制结案
     * @param request
     * @return
     */
    void finishWorkOrder(FinishTaskRequest request);
}
