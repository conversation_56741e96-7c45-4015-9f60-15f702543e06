/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2023 All Rights Reserved.
 */
package com.xinfei.vocmng.biz.model.resp;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @version $ DepartmentResp, v 0.1 2023/12/20 14:13 wancheng.qu Exp $
 */

@Data
public class DepartmentResp implements Serializable {

    @ApiModelProperty(value = "id")
    private Long id;

    @ApiModelProperty(value = "组织名称")
    private String name;

    @ApiModelProperty(value = "父部门ID")
    private Long parentDepartmentId;

    @ApiModelProperty(value = "部门级别：0根部门 1一级部门 2二级部门 ")
    private Integer level;

    @ApiModelProperty(value = "部门负责人")
    private String directorIdentify;

    @ApiModelProperty(value = "部门负责人名字")
    private String director;

    @ApiModelProperty(value = "部门类型（1：内勤 2：外包）")
    private Integer departmentType;


}