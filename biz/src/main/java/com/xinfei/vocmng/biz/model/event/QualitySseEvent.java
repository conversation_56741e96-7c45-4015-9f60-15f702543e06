/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xinfei.vocmng.biz.model.event;

import lombok.Getter;
import org.springframework.context.ApplicationEvent;

/**
 * <AUTHOR>
 * @version $ SseEvent, v 0.1 2024/2/7 15:28 wancheng.qu Exp $
 */

@Getter
public class QualitySseEvent extends ApplicationEvent {

    private String userIdentify;
    private String customerPhone;
    private String displayNumber;
    private String callId;
    private String status;

    public QualitySseEvent(Object source, String userIdentify, String customerPhone, String displayNumber, String callId, String status) {
        super(source);
        this.userIdentify = userIdentify;
        this.customerPhone = customerPhone;
        this.displayNumber = displayNumber;
        this.callId = callId;
        this.status = status;
        // 初始化其他信息
    }

    public QualitySseEvent(Object source) {
        super(source);
    }

    public void setUserIdentify(String userIdentify) {
        this.userIdentify = userIdentify;
    }

    public void setCustomerPhone(String customerPhone) {
        this.customerPhone = customerPhone;
    }

    public void setDisplayNumber(String displayNumber) {
        this.displayNumber = displayNumber;
    }
}