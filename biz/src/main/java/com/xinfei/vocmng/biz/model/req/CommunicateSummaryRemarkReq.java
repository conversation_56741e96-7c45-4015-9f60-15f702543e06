/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2023 All Rights Reserved.
 */
package com.xinfei.vocmng.biz.model.req;

import com.xinfei.vocmng.biz.model.base.PageRequestDto;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 *
 * <AUTHOR>
 * @version $ CommunicateSummaryRemarkReq, v 0.1 2023/12/19 14:26 wancheng.qu Exp $
 */

@Data
public class CommunicateSummaryRemarkReq extends PageRequestDto {

    @ApiModelProperty(value = "小结id")
    private Long communicateSummaryId;


}