/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xinfei.vocmng.biz.model.req;

import com.xinfei.vocmng.biz.model.base.PageRequestDto;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 *
 * <AUTHOR>
 * @version $ DictReq, v 0.1 2024/4/9 17:39 wancheng.qu Exp $
 */

@Data
public class DictReq extends PageRequestDto implements Serializable {

    @ApiModelProperty(value = "字典id")
    private Long id ;

    @ApiModelProperty(value = "字典名")
    private String name;


}