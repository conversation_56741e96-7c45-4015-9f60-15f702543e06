/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2023 All Rights Reserved.
 */
package com.xinfei.vocmng.biz.util;

import com.xinfei.vocmng.dal.po.DictDetail;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.Objects;

/**
 *
 * <AUTHOR>
 * @version $ DataMaskingUtil, v 0.1 2023/12/28 19:48 wancheng.qu Exp $
 */

public class DataMaskingUtil {
    public static String maskNone(String data) {
        return "none";
    }

    public static String maskTele(String data) {
        if (data != null && data.length() >= 5) {
            return data.substring(0, 2) + "****" + data.substring(data.length() - 3);
        }
        return data;
    }

    public static String maskName(String data) {
        if (data != null && data.length() >= 2) {
            return data.substring(0, data.length() - 1) + "*";
        }
        return data;
    }

    public static String maskIdCard(String data) {
        if (data != null && data.length() == 18) {
            return data.substring(0, 5) + "**********" + data.substring(14);
        }
        return data;
    }

    public static String maskAddress(String data) {
        // 简化示例，实际情况可能需要更复杂的逻辑
        if (data != null && data.length() > 5) {
            return data.substring(0, 5) + "******";
        }
        return data;
    }

    public static String maskMoney(String data, List<DictDetail> list) {
        return Optional.ofNullable(data)
                .map(str -> str.indexOf("@"))
                .filter(index -> index != -1)
                .flatMap(index -> {
                    List<DictDetail> nonNullList = list != null ? list : new ArrayList<>();
                    String key = data.substring(index + 1);
                    return nonNullList.stream()
                            .filter(detail -> detail.getDictKey().equals(key))
                            .map(DictDetail::getDictValue)
                            .findFirst()
                            .map(Optional::of)
                            .orElseGet(() -> Optional.of(key));
                })
                .orElse(data);
    }

    public static List<String> maskMoneyList(List<String> datas, List<DictDetail> list) {
        List<String> dataList = new ArrayList<>();
        for (String data : datas) {
            String newData = Optional.ofNullable(data)
                    .map(str -> str.indexOf("@"))
                    .filter(index -> index != -1)
                    .flatMap(index -> {
                        List<DictDetail> nonNullList = list != null ? list : new ArrayList<>();
                        String key = data.substring(index + 1);
                        return nonNullList.stream()
                                .filter(detail -> detail.getDictKey().equals(key))
                                .map(DictDetail::getDictValue)
                                .findFirst()
                                .map(Optional::of)
                                .orElseGet(() -> Optional.of(key));
                    })
                    .orElse(data);
            dataList.add(newData);
        }

        return dataList;
    }

    public static String maskCardNo(String data) {
        if (data != null && data.length() >= 12) {
            return data.substring(0, 6) + "******" + data.substring(12);
        }
        return data;
    }

    public static String maskAlipay(String data) {
        if (data != null && data.length() >= 5) {
            return data.substring(0, 3) + "****" + data.substring(data.length() - 2);
        }
        return data;
    }


}