/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xinfei.vocmng.biz.job;

import com.xinfei.vocmng.biz.component.RepaymentTaskManage;
import com.xinfei.vocmng.biz.service.UDeskService;
import com.xinfei.vocmng.biz.util.LocalDateTimeUtils;
import com.xinfei.vocmng.itl.util.LogUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @version $ UdeskService, v 0.1 2025-03-06 14:22 pengming.liu Exp $
 */
@Component
@Slf4j
public class UdeskTimelyTask extends RepaymentTaskManage {

    @Resource
    private UDeskService uDeskService;

    @Value(value = "${udesk.Sync:false}")
    private Boolean isOpen;

    @Value(value = "${udesk.uDeskTimelySync:0 0/1 * * * ?}")
    private String cron;

    private static final String redisKey = "uDeskTimelySync";

    @Override
    protected String getCron() {
        return cron;
    }

    @Override
    protected void processTask() {
        if (isOpen) {
            LocalDateTime now = LocalDateTime.now();
            String startTime = LocalDateTimeUtils.format(now.minusMinutes(61));
            String endTime = LocalDateTimeUtils.format(now.minusHours(1));
            uDeskService.uDeskSyncAll(redisKey, startTime, endTime);
        }
    }
}