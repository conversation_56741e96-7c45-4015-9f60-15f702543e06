/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2023 All Rights Reserved.
 */
package com.xinfei.vocmng.biz.api;

import com.xinfei.vocmng.biz.model.annotation.DataPermission;
import com.xinfei.vocmng.biz.model.annotation.LoginRequired;
import com.xinfei.vocmng.biz.model.annotation.OperateLogAnnotation;
import com.xinfei.vocmng.biz.model.annotation.ResourcePermission;
import com.xinfei.vocmng.biz.model.base.PageResultResponse;
import com.xinfei.vocmng.biz.model.enums.DataPermissionType;
import com.xinfei.vocmng.biz.model.enums.OperateType;
import com.xinfei.vocmng.biz.model.enums.TechplayErrDtlEnum;
import com.xinfei.vocmng.biz.model.exception.IgnoreException;
import com.xinfei.vocmng.biz.model.req.*;
import com.xinfei.vocmng.biz.model.resp.ApiResponse;
import com.xinfei.vocmng.biz.model.resp.CommunicateSummaryRemarkResp;
import com.xinfei.vocmng.biz.model.resp.CommunicateSummaryResp;
import com.xinfei.vocmng.biz.model.resp.EagleEyeDataResp;
import com.xinfei.vocmng.biz.service.CommunicateSummaryService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.time.LocalDate;

/**
 * 小结页面接口类
 *
 * <AUTHOR>
 * @version $ CommunicateSummaryApi, v 0.1 2023/12/18 11:46 wancheng.qu Exp $
 */

@Api(tags = "小结列表")
@RestController
@LoginRequired
@RequestMapping("/communicateSummary")
public class CommunicateSummaryApi {

    @Resource
    private CommunicateSummaryService communicateSummaryService;

    @ApiOperation("获取小结列表")
    @PostMapping("/list")
    @DataPermission(type = DataPermissionType.NONE)
    public ApiResponse<PageResultResponse<CommunicateSummaryResp>> list(@RequestBody CommunicateSummaryReq communicateSummaryReq) {
        return ApiResponse.success(communicateSummaryService.list(communicateSummaryReq));
    }

    @ApiOperation("获取userNo")
    @PostMapping("/getUserNo")
    public ApiResponse<String> getUserNo(@RequestBody GetUserNoReq getUserNoReq) {
        return ApiResponse.success(communicateSummaryService.getUserNo(getUserNoReq));
    }

    @ApiOperation("获取小结明细")
    @PostMapping("/detail")
    public ApiResponse<CommunicateSummaryDetailRsp> getCommunicateSummaryDetail(@RequestBody CommunicateSummaryDetailReq request) {
        if (request.getSummaryId() == null || StringUtils.isEmpty(request.getTelephone())) {
            throw new IgnoreException(TechplayErrDtlEnum.REQ_PARAM_NOT_VALID, "小结id或者手机号未传");
        }
        return ApiResponse.success(communicateSummaryService.detail(request));
    }

    @ApiOperation("创建小结")
    @PostMapping("/create")
    @ResourcePermission("addsummary")
    @OperateLogAnnotation(type = OperateType.TYPE_ONE, description = "创建小结")
    public ApiResponse<Long> create(@RequestBody CommunicateSummaryCreateReq communicateSummaryCreateReq) {
        return ApiResponse.success(communicateSummaryService.create(communicateSummaryCreateReq));
    }

    @ApiOperation("小结备注列表")
    @PostMapping("/remarkList")
    public ApiResponse<PageResultResponse<CommunicateSummaryRemarkResp>> remarkList(@RequestBody CommunicateSummaryRemarkReq communicateSummaryRemarkReq) {
        return ApiResponse.success(communicateSummaryService.remarkList(communicateSummaryRemarkReq));
    }

    @ApiOperation("新增小结备注")
    @PostMapping("/createRemark")
    @ResourcePermission("remarksummary")
    @OperateLogAnnotation(type = OperateType.TYPE_ONE, description = "新增小结备注")
    public ApiResponse<Boolean> createRemark(@RequestBody CommunicateRemarkCreateReq communicateRemarkCreateReq) {
        return ApiResponse.success(communicateSummaryService.createRemark(communicateRemarkCreateReq));
    }

    @ApiOperation("鹰眼大盘")
    @PostMapping("/eagleEyeData")
    @DataPermission(type = DataPermissionType.NONE)
    public ApiResponse<EagleEyeDataResp> eagleEyeData(@RequestBody EagleEyeDataReq eagleEyeDataReq) {
        if (eagleEyeDataReq.getNowDate() == null) {
            eagleEyeDataReq.setNowDate(LocalDate.now());
        }

        return ApiResponse.success(communicateSummaryService.getEagleEyeData(eagleEyeDataReq));
    }

}