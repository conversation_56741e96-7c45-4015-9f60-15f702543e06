package com.xinfei.vocmng.biz.remote;

import com.xinfei.vocmng.biz.model.resp.ApiResponse;
import com.xinfei.vocmng.biz.rr.dto.CallRecordDto;
import com.xinfei.vocmng.biz.rr.dto.MarketingBlackDto;
import com.xinfei.vocmng.biz.rr.dto.SmsRecordsDto;
import com.xinfei.vocmng.biz.rr.dto.StopMarketDto;
import com.xinfei.vocmng.biz.rr.request.*;
import com.xinfei.vocmng.biz.rr.response.Paging;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * 用户触达记录相关接口
 *
 * <AUTHOR>
 * @version $ UserReachRemoteService, v 0.1 2023/12/28 14:38 qu.lu Exp $
 */
public interface UserReachRemoteService {
    ApiResponse<Paging<CallRecordDto>> queryCallRecordList(@RequestBody QueryCallListRequest request);

    ApiResponse<Paging<SmsRecordsDto>> querySmsRecordList(@RequestBody QuerySmsRecordRequest request);

    ApiResponse<Paging<StopMarketDto>> queryStopMarketList(StopMarketRequest request);

    ApiResponse<Boolean> addStopMarket(StopMarketRequest request);

    ApiResponse<Boolean> updateStopMarket(StopMarketRequest request);

    ApiResponse<List<MarketingBlackDto>> marketingBlackList(MarketingBlackListRequest request);

    ApiResponse<Boolean> marketingBlackCreate(List<MarketingBlackRequest> request);

    ApiResponse<Boolean> marketingBlackUpdate(MarketingBlackRequest request);
}
