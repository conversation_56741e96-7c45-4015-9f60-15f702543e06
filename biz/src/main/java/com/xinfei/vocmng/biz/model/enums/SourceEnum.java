package com.xinfei.vocmng.biz.model.enums;

/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2025 All Rights Reserved.
 */
public enum SourceEnum {
    // 热线
    HOTLINE(1, "客服热线"),
    // 在线
    ONLINE(2, "在线客服"),
    // 其他
    OTHER(3, "其他"),
    // 贷后IB
    IB_POST_LOAN(4, "贷后IB"),
    // 投诉
    COMPLAINT(5, "投诉"),
    // 回访
    VISIT(6, "客户回访");
    /**
     * 枚举值
     */
    private final Integer code;
    /**
     * 显示名称
     */
    private final String name;

    SourceEnum(Integer code, String name) {
        this.code = code;
        this.name = name;
    }

    /**
     * 获取枚举值
     */
    public Integer getCode() {
        return code;
    }

    /**
     * 获取显示名称
     */
    public String getName() {
        return name;
    }

    /**
     * 根据code获取枚举
     */
    public static SourceEnum getByCode(Integer code) {
        for (SourceEnum value : values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return null;
    }
}
