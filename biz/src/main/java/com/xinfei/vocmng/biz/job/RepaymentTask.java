/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xinfei.vocmng.biz.job;


import com.xinfei.vocmng.biz.component.RepaymentTaskManage;
import com.xinfei.vocmng.biz.service.RepaymentTaskService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.beans.factory.annotation.Value;



/**
 * <AUTHOR>
 * @version $ RepaymentTask, v 0.1 2024/3/29 16:22 wancheng.qu Exp $
 */
@Configuration
public class RepaymentTask extends RepaymentTaskManage {

    @Value(value = "${repay.task.switch:true}")
    private Boolean isSwitch;

    @Value(value = "${repay.task.cron:0 */30 * * * ?}")
    private String cron;

    @Autowired
    private RepaymentTaskService repaymentTaskService;

    @Override
    protected void processTask() {
        if (isSwitch) {
            repaymentTaskService.updateRepaymentStatus();
        }
    }

    @Override
    protected String getCron() {
        return cron;
    }

}