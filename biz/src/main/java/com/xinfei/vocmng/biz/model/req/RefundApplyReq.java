/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xinfei.vocmng.biz.model.req;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @version $ RefundTrialReq, v 0.1 2024-05-20 15:39 junjie.yan Exp $
 */
@Data
public class RefundApplyReq {

    @ApiModelProperty("借据号")
    @NotBlank(message = "借据号不能为空")
    private String loanNo;

    @ApiModelProperty(value = "还款单号列表")
    private List<String> repaymentNos;

    @ApiModelProperty("账单号")
    private String billNo;

    @ApiModelProperty("期数")
    private Integer term;

    @ApiModelProperty("退款方式，ONLINE_REFUND:线上原路原退， OFFLINE_REFUND:线下退款")
    @NotBlank(message = "退款方式不能为空")
    private String refundType;

    @ApiModelProperty("总退款金额")
    @NotNull(message = "总退款金额不能为空")
    private BigDecimal refundAmount;

    @ApiModelProperty("还款时间，1：立即(2小时)，2：50天，3：15天 4:自定义天数")
    @NotNull(message = "还款时间不能为空")
    private Integer executeType;

    @ApiModelProperty("自定义天数")
    private Integer executeDay;

    @ApiModelProperty("线下退款类型，ALIPAY:支付宝,BANK_CARD:银行卡")
    private String offlineRefundMethod;

    @ApiModelProperty("线下退款账号")
    private String offlineRefundAccount;

    @ApiModelProperty("线下退款银行")
    private String offlineRefundBank;

    @ApiModelProperty("线下退款客户名")
    private String offlineRefundUserName;


    @ApiModelProperty("订单号")
    @NotBlank(message = "订单号不能为空")
    private String orderNo;

    @ApiModelProperty("审核状态，0：无需审核，1：待审核，2：审核通过，3：审核拒绝")
    @NotNull(message = "审核状态不能为空")
    private Integer status;

    @ApiModelProperty("custNo")
    @NotBlank(message = "custNo不能为空")
    private String custNo;

    @ApiModelProperty("userNo")
    @NotBlank(message = "userNo不能为空")
    private String userNo;

    @ApiModelProperty("退款类型（1：订单，2：账单，3：溢缴款）")
    @NotNull(message = "退款类型不能为空")
    private Integer requestType;

    @ApiModelProperty("订单类型，PROFIT:营收订单（权益产品订单），MAIN:现金订单（信贷产品订单）")
    @NotBlank(message = "订单类型不能为空")
    private String orderType;

    @ApiModelProperty("退款原因")
    @NotBlank(message = "退款原因不能为空")
    private String refundReason;

    @ApiModelProperty("实际费率")
    private BigDecimal realRatio;
}