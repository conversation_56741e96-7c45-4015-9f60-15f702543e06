/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xinfei.vocmng.biz.util;

import com.fasterxml.jackson.core.type.TypeReference;
import com.xinfei.vocmng.biz.constants.RedisKeyConstants;
import com.xinfei.vocmng.itl.client.feign.impl.PayFeignClientImpl;
import com.xinfei.vocmng.itl.client.feign.impl.ProductFeignService;
import com.xinfei.vocmng.itl.rr.FinServiceType;
import com.xinfei.vocmng.itl.rr.dto.BankDto;
import com.xinfei.xfframework.common.JsonUtil;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @version $ RedisServiceUtils, v 0.1 2024-04-16 15:01 junjie.yan Exp $
 */
@Component
public class RedisServiceUtils {

    @Resource
    private RedisUtils redisUtils;

    @Resource
    private ProductFeignService productFeignService;

    @Resource
    private PayFeignClientImpl payFeignClientImpl;

    public FinServiceType getFinBaseInfo() {
        String serviceType = redisUtils.get(RedisKeyConstants.SERVICE_TYPE_FOR_APP);
        FinServiceType finServiceType;
        if (serviceType == null) {
            finServiceType = productFeignService.baseInfo();
            redisUtils.set(RedisKeyConstants.SERVICE_TYPE_FOR_APP, JsonUtil.toJson(finServiceType), 1, TimeUnit.HOURS);
        } else {
            finServiceType = JsonUtil.parseJson(serviceType, FinServiceType.class);
        }

        return finServiceType;
    }

    public List<BankDto> getBankList() {
        String bankList = redisUtils.get(RedisKeyConstants.BANK_LIST);
        List<BankDto> bankDtos;
        if (bankList == null) {
            bankDtos = payFeignClientImpl.bankList();
            redisUtils.set(RedisKeyConstants.BANK_LIST, JsonUtil.toJson(bankDtos), 1, TimeUnit.HOURS);
        } else {
            bankDtos = JsonUtil.parseJson(bankList, new TypeReference<List<BankDto>>() {
            });
        }

        return bankDtos;
    }

}