package com.xinfei.vocmng.biz.api;

import com.xinfei.vocmng.biz.model.resp.ApiResponse;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * Redis管理接口
 *
 * <AUTHOR>
 * @version $ RedisManageApi, v 0.1 2025/5/1 $
 */
@Api(tags = "Redis管理接口")
@RequestMapping("/api/redis")
public interface RedisManageApi {

    /**
     * 查询Redis中的Set数据
     *
     * @param key Set的key
     * @return Set中的所有元素
     */
    @ApiOperation("查询Redis中的Set数据")
    @GetMapping("/set/{key}")
    ApiResponse<Set<String>> getSetMembers(
            @ApiParam(value = "Set的key", required = true) @PathVariable("key") String key);

    /**
     * 删除Redis中的Set数据
     *
     * @param key Set的key
     * @return 是否删除成功
     */
    @ApiOperation("删除Redis中的Set数据")
    @DeleteMapping("/set/{key}")
    ApiResponse<Boolean> deleteSet(
            @ApiParam(value = "Set的key", required = true) @PathVariable("key") String key);

    /**
     * 从Set中删除指定元素
     *
     * @param key Set的key
     * @param value 要删除的元素
     * @return 是否删除成功
     */
    @ApiOperation("从Set中删除指定元素")
    @DeleteMapping("/set/{key}/member")
    ApiResponse<Boolean> deleteSetMember(
            @ApiParam(value = "Set的key", required = true) @PathVariable("key") String key,
            @ApiParam(value = "要删除的元素", required = true) @RequestParam("value") String value);

    /**
     * 查询Redis中的Stream数据
     *
     * @param key Stream的key
     * @param count 获取的消息数量，默认10条
     * @return Stream中的消息列表
     */
    @ApiOperation("查询Redis中的Stream数据")
    @GetMapping("/stream/{key}")
    ApiResponse<List<Map<String, Object>>> getStreamMessages(
            @ApiParam(value = "Stream的key", required = true) @PathVariable("key") String key,
            @ApiParam(value = "获取的消息数量", defaultValue = "10") @RequestParam(value = "count", defaultValue = "10") int count);

    /**
     * 删除Redis中的Stream数据
     *
     * @param key Stream的key
     * @return 是否删除成功
     */
    @ApiOperation("删除Redis中的Stream数据")
    @DeleteMapping("/stream/{key}")
    ApiResponse<Boolean> deleteStream(
            @ApiParam(value = "Stream的key", required = true) @PathVariable("key") String key);

    /**
     * 从Stream中删除指定消息
     *
     * @param key Stream的key
     * @param messageId 要删除的消息ID
     * @return 是否删除成功
     */
    @ApiOperation("从Stream中删除指定消息")
    @DeleteMapping("/stream/{key}/message")
    ApiResponse<Boolean> deleteStreamMessage(
            @ApiParam(value = "Stream的key", required = true) @PathVariable("key") String key,
            @ApiParam(value = "要删除的消息ID", required = true) @RequestParam("messageId") String messageId);

    /**
     * 查询Redis中的所有key
     *
     * @param pattern 匹配模式，例如"voc.merge.*"
     * @return 匹配的key列表
     */
    @ApiOperation("查询Redis中的所有key")
    @GetMapping("/keys")
    ApiResponse<Set<String>> getKeys(
            @ApiParam(value = "匹配模式", defaultValue = "*") @RequestParam(value = "pattern", defaultValue = "*") String pattern);
}
