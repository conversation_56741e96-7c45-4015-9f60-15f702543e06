package com.xinfei.vocmng.biz.api;

import com.xinfei.huttalegal.facade.rr.res.LegalAgencyDetail;
import com.xinfei.vocmng.biz.model.annotation.OperateLogAnnotation;
import com.xinfei.vocmng.biz.model.enums.OperateType;
import com.xinfei.vocmng.biz.model.resp.ApiResponse;
import com.xinfei.vocmng.biz.rr.dto.DiversionOrderDto;
import com.xinfei.vocmng.biz.rr.dto.OrderDto;
import com.xinfei.vocmng.biz.rr.dto.OrderFilterFactorDto;
import com.xinfei.vocmng.biz.rr.dto.RepaymentsDto;
import com.xinfei.vocmng.biz.rr.dto.bill.LoanPlanDto;
import com.xinfei.vocmng.biz.rr.request.*;
import com.xinfei.vocmng.biz.rr.response.CanRepayResponse;
import com.xinfei.vocmng.biz.rr.response.Paging;
import com.xinfei.vocmng.itl.rr.ProductDetailInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.time.LocalDate;
import java.util.List;
import java.util.Map;

/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2023 All Rights Reserved.
 */
@Api(tags = "信贷相关接口")
@RequestMapping("/loan")
public interface LoanApi {
    @ApiOperation("订单列表")
    @PostMapping(value = "/order/list")
    ApiResponse<Paging<OrderDto>> getOrderList(@RequestBody GetOrderListRequest request);

    @ApiOperation("订单详情")
    @PostMapping(value = "/order/detail")
    ApiResponse<OrderDto> getOrderDetail(@RequestBody GetOrderDetailRequest request);

    @ApiOperation("订单取消")
    @PostMapping(value = "/order/cancel")
    ApiResponse<Boolean> orderCancel(@RequestBody OrderCancelRequest request);

    @ApiOperation("账单列表")
    @PostMapping(value = "/bill/list")
    ApiResponse<List<LoanPlanDto>> getBillList(@RequestBody GetBillListRequest request);

    @ApiOperation("分页账单列表")
    @PostMapping(value = "/bill/list/paging")
    ApiResponse<Paging<LoanPlanDto>> getBillListWithPaging(@RequestBody GetBillListPageRequest request);

    @ApiOperation("测算接口")
    @PostMapping(value = "/canRepay")
    ApiResponse<CanRepayResponse> canRepay(@RequestBody CanRepayRequest request);

    @ApiOperation("还款记录")
    @PostMapping(value = "/repayment/list")
    ApiResponse<Paging<RepaymentsDto>> getRepayments(@RequestBody GetRepaymentsRequest request);

    @ApiOperation("资方额度取消")
    @PostMapping(value = "/logoutQuota")
    ApiResponse<Boolean> logoutQuota(@Validated @RequestBody LogoutQuotaRequest request);

    @ApiOperation("API导流订单列表")
    @PostMapping(value = "/api/order/list")
    ApiResponse<Paging<DiversionOrderDto>> getApiOrderList(@Validated @RequestBody ApiOrderRequest apiOrderRequest);

    @ApiOperation("产品列表")
    @PostMapping(value = "/api/queryProductList")
    ApiResponse<List<ProductDetailInfo>> queryProductList();

    @ApiOperation("法诉机构查询")
    @PostMapping(value = "/queryAgencyDetail")
    ApiResponse<LegalAgencyDetail> queryAgencyDetail(@Validated @RequestBody AgencyDetailRequest agencyDetailRequest);

    @ApiOperation("订单账单下载")
    @PostMapping("/orderListDownload")
    @OperateLogAnnotation(type = OperateType.TYPE_FOUR, description = "订单账单下载")
    void orderListDownload(HttpServletResponse response,@RequestBody GetBillListRequest request);

    @ApiOperation("获取订单筛选条件列表")
    @PostMapping(value = "/order/getOrderFilterFactor")
    ApiResponse<OrderFilterFactorDto> getOrderFilterFactor(@Validated @RequestBody OrderFilterFactorRequest request);
}
