/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xinfei.vocmng.biz.util;

/**
 * <AUTHOR>
 * @version $ PaginationUtils, v 0.1 2024/3/30 16:29 wancheng.qu Exp $
 */

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.tuple.ImmutableTriple;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.function.Consumer;
import java.util.function.Supplier;

@Slf4j
public class PaginationUtils {

    @Data
    @NoArgsConstructor
    public static class PaginationParam {

        private long id = 0;

        private long page = 1;

        private long pageSize = 300;

        public PaginationParam(long pageSize) {
            this.pageSize = pageSize;
        }
    }

    public enum PaginationTypeEnum {
        /**
         * 正常 LIMIT分页
         */
        NORMAL,
        /**
         * 自增ID排序分页
         */
        ID_PAGING
    }

    public static <S> void invokePaginationNew(PaginationTypeEnum paginationTypeEnum, PaginationParam invokePage,
                                               Supplier<ImmutableTriple<List<S>, Integer, Long>> supplier, Consumer<List<S>> consumer) {

        while (true) {

            ImmutableTriple<List<S>, Integer, Long> trip = supplier.get();
            List<S> bos = trip.getLeft();
            Integer total = trip.getMiddle();
            Long lastIndex = trip.getRight();
            if (!CollectionUtils.isEmpty(bos)) {
                consumer.accept(bos);
            }
            if (total <= 0) {
                break;
            }
            if (paginationTypeEnum == PaginationTypeEnum.ID_PAGING) {
                invokePage.setId(lastIndex);
            } else {
                invokePage.setPage(++invokePage.page);
            }
        }
    }

}
