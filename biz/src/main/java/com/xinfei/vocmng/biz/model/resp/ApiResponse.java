package com.xinfei.vocmng.biz.model.resp;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.xinfei.vocmng.biz.model.enums.ResultCodeEnum;
import com.xinfei.vocmng.biz.model.enums.TechplayErrDtlEnum;
import com.xinfei.vocmng.util.exception.ErrorContextUtil;
import com.xinfei.vocmng.util.exception.VocmngException;
import com.xinfei.vocmng.util.trace.TraceUtil;
import com.xinfei.xfframework.common.ErrorContext;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @create 2019/11/04
 */
@Data
public class ApiResponse<T> implements Serializable {

    private static final long serialVersionUID = 8066713293020583492L;

    /**
     * 状态码
     */
    private Integer code;
    /**
     * 服务提示信息
     */
    private String msg;

    /**
     * 服务错误code
     */
    private String errCode;
    /**
     * 服务返回结果信息
     */
    private T data;

    private String traceId;

    public ApiResponse() {
        traceId = TraceUtil.getTraceId();
    }


    public ApiResponse(TechplayErrDtlEnum resultCode,String msg) {
        this.traceId = TraceUtil.getTraceId();
        this.code = Integer.parseInt(resultCode.getCode());
        this.msg = (StringUtils.isEmpty(msg) ? resultCode.getDescription() : msg);
    }

    public ApiResponse(com.xinfei.vocmng.itl.client.exeception.TechplayErrDtlEnum resultCode, String msg) {
        this.traceId = TraceUtil.getTraceId();
        this.code = Integer.parseInt(resultCode.getCode());
        this.msg = (StringUtils.isEmpty(msg) ? resultCode.getDescription() : msg);
    }

    public ApiResponse(ResultCodeEnum resultCode, String msg) {
        this.traceId = TraceUtil.getTraceId();
        this.code = resultCode.getCode();
        this.msg = (StringUtils.isEmpty(msg) ? resultCode.getMsg() : msg);
    }

    public static <T> ApiResponse<T> responseWithCode(ResultCodeEnum resultCode, String msg) {
        return new ApiResponse<T>(resultCode, msg);
    }

    public static <T> ApiResponse<T> success() {
        ApiResponse<T> result = ApiResponse.create();
        result.setMsg(ResultCodeEnum.SUCCESS.getMsg());
        result.setCode(ResultCodeEnum.SUCCESS.getCode());
        return result;
    }

    public static <T> ApiResponse<T> success(T t) {
        ApiResponse<T> result = ApiResponse.create();
        result.setData(t);
        result.setMsg(ResultCodeEnum.SUCCESS.getMsg());
        result.setCode(ResultCodeEnum.SUCCESS.getCode());
        return result;
    }

    public static <T> ApiResponse<T> fail(String msg) {
        return responseWithCode(ResultCodeEnum.FAILED, msg);
    }


    public static <T> ApiResponse<T> fail(VocmngException ex) {
        ErrorContext errorContext = ErrorContextUtil.genErrorContext(ex.getResultCodeEnum(), ex.getMessage());
        return ApiResponse.fail(errorContext.getErrCode(), errorContext.getErrDesc());
    }

    public static <T> ApiResponse<T> fail(String errorCode, String msg) {
        ApiResponse<T> result = create();
        result.setCode(ResultCodeEnum.FAILED.getCode());
        result.setErrCode(errorCode);
        result.setMsg(msg);
        return result;
    }

    public static <T> ApiResponse<T> paramIllegal(String msg) {
        return responseWithCode(ResultCodeEnum.PARAM_ILLEGAL, msg);
    }

    public static <T> ApiResponse<T> create() {
        return new ApiResponse<T>();
    }

    @JsonIgnore
    public boolean isSuccess() {
        return this.code == ResultCodeEnum.SUCCESS.getCode();
    }
}
