/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xinfei.vocmng.biz.model.req;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 *
 * <AUTHOR>
 * @version $ RefundRightNowReq, v 0.1 2024-05-22 18:58 junjie.yan Exp $
 */
@Data
public class RefundRightNowReq {

    @ApiModelProperty("退款指令号")
    @NotBlank(message = "退款指令号不能为空")
    private String refundInstructionNo;

}