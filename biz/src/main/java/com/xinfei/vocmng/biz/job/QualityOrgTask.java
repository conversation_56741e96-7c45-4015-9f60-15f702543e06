/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xinfei.vocmng.biz.job;

import com.xinfei.vocmng.biz.component.RepaymentTaskManage;
import com.xinfei.vocmng.biz.service.LoginService;
import com.xinfei.vocmng.itl.util.LogUtil;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @version $ QuanlityTask, v 0.1 2024-10-18 16:18 junjie.yan Exp $
 */
@Configuration
@Slf4j
public class QualityOrgTask extends RepaymentTaskManage {

    @Resource
    private LoginService loginService;

    @Value(value = "${quality.org.switch:false}")
    private Boolean isQuality;

    @Value(value = "${quality.org.cron:* 0/3 * * * ?}")
    private String cron;

    @Autowired
    private RedissonClient redissonClient;

    @Override
    protected String getCron() {
        return cron;
    }

    @Override
    protected void processTask() {
        RLock lock = redissonClient.getLock("qualityOrgTaskLock");
        try {
            // 尝试获取锁，等待时间为0秒，锁的自动释放时间为60秒
            boolean isLocked = lock.tryLock(0, 60, java.util.concurrent.TimeUnit.SECONDS);
            if (isLocked) {
                if (isQuality) {
                    log.info(LogUtil.infoLog("syncQualityOrg", "开始全量同步客服组织信息至质检平台"));
                    loginService.syncQualityOrg();
                }
            }
        } catch (InterruptedException e) {
            log.error("Thread interrupted while trying to acquire lock");
        } finally {
            if (lock.isHeldByCurrentThread()) {
                lock.unlock();
                log.info(LogUtil.infoLog("syncQualityOrg", "Lock released"));
            }
        }
    }
}