/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2023 All Rights Reserved.
 */
package com.xinfei.vocmng.biz.util;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * 异常处理工具
 *
 * <AUTHOR>
 * @version $ ExceptionUtil, v 0.1 2023/8/28 20:39 <PERSON><PERSON>.Huang Exp $
 */
public final class ExceptionUtil {

    /**
     * COMMON-ERROR
     */
    private static final Logger LOGGER = LoggerFactory.getLogger("COMMON-ERROR");

    /**
     * 私有构造器
     */
    private ExceptionUtil() {
        // 禁止创建实例
    }

    /**
     * 异常处理
     *
     * @param e       异常对象
     * @param message 异常说明
     */
    public static void error(Throwable e, Object... message) {
        LOGGER.error(LoggerUtil.getLogString(message), e);
    }
}
