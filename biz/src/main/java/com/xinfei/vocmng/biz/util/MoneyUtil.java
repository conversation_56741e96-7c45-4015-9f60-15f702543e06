/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2023 All Rights Reserved.
 */
package com.xinfei.vocmng.biz.util;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

public final class MoneyUtil {

    /**
     * 使用四舍五入将分转为元，默认保留2位小数
     * @param f 分
     * @return
     */
    public static BigDecimal f2Y(BigDecimal f){
        if (f == null){
            return null;
        }
        return BigDecimalUtil.divide(f, BigDecimal.valueOf(100), 2);
    }


    /**
     * 使用四舍五入将分转为元
     * @param f 分
     * @param scale 保留小数位
     * @return
     */
    public static BigDecimal f2Y(BigDecimal f, int scale){
        if (f == null){
            return null;
        }
        return BigDecimalUtil.divide(f, BigDecimal.valueOf(100), scale);
    }


    /**
     * 金额元转为分
     * @param y 元
     * @return
     */
    public static Long y2F(BigDecimal y){
        if (y == null){
            return null;
        }
        BigDecimal multiply = BigDecimalUtil.multiply(y, BigDecimal.valueOf(100));
        return Long.parseLong(multiply.stripTrailingZeros().toPlainString());
    }

    /**
     * 多个金额元转为分
     * @param yList 元
     * @return
     */
    public static List<Long> y2FList(List<Long> yList){
        List<Long> resultList = new ArrayList<>();
        if (yList.isEmpty()){
            return null;
        }
        for(Long y : yList) {
            if(y != null){
                y = y*100;
            }
            resultList.add(y);
        }
        return resultList;
    }

    /**
     * 多个金额分转为元
     * @param fList 分
     * @return
     */
    public static List<Long> f2YList(List<Long> fList){
        List<Long> resultList = new ArrayList<>();
        if (fList.isEmpty()){
            return null;
        }
        for(Long f : fList) {
            if(f != null){
                f = f/100;
            }
            resultList.add(f);
        }
        return resultList;
    }
}