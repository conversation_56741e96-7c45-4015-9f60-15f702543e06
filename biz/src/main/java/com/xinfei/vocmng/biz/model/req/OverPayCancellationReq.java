/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xinfei.vocmng.biz.model.req;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
 *
 * <AUTHOR>
 * @version $ OverPayCancellationReq, v 0.1 2024-05-23 13:55 junjie.yan Exp $
 */
@Data
public class OverPayCancellationReq {

    @ApiModelProperty(value = "金额")
    @NotNull(message = "金额不为空")
    private BigDecimal amount;

    @ApiModelProperty(value = "转账渠道,zfb,bank")
    @NotBlank(message = "转账渠道不为空")
    private String channelCode;

    @ApiModelProperty(value = "退款申请请求唯一流水")
    @NotBlank(message = "退款申请请求唯一流水不为空")
    private String oriReqNo;

    @ApiModelProperty(value = "转账流水")
    @NotBlank(message = "转账流水不为空")
    private String transNo;
}