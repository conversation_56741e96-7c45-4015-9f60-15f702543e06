/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2025 All Rights Reserved.
 */
package com.xinfei.vocmng.biz.model.req;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.time.LocalDate;
import java.util.List;

/**
 * <AUTHOR>
 * @version $ EagleEyeDataReq, v 0.1 2025-04-09 14:09 junjie.yan Exp $
 */
@Data
public class EagleEyeDataReq {

    @ApiModelProperty(value = "进线来源列表")
    private List<Integer> sourceList;

    @ApiModelProperty(value = "创建人列表")
    private List<String> createUserIdentifyList;

    @ApiModelProperty(value = "小结状态列表")
    private List<Integer> statusList;

    @ApiModelProperty(value = "当前日期")
    private LocalDate nowDate;

    @ApiModelProperty(value = "对比日期")
    @NotNull(message = "对比日期必传")
    private LocalDate periodDate;

}