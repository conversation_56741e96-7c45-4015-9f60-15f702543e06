package com.xinfei.vocmng.biz.model.resp;

import com.xinfei.vocmng.biz.model.annotation.DataPermission;
import com.xinfei.vocmng.biz.model.enums.DataPermissionType;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR> 2024/10/9 17:27
 * 会员卡原路原退明细
 */
@Data
public class VipCardRefundResp implements Serializable {
    @ApiModelProperty("退款金额(元)")
    private BigDecimal refundAmt;

    @ApiModelProperty("退款方式")
    private String refundType;

    @ApiModelProperty("退款类型,ONLINE_REFUND:线上原路原退,OFFLINE_REFUND:线下退款")
    private String refundMethod;

    @ApiModelProperty("绑卡")
    private String bankCardId;

    @ApiModelProperty("退款客户姓名")
    private String userName;

    @ApiModelProperty("退款银行")
    private String bankName;

    @ApiModelProperty("退款银行账号")
    @DataPermission(type = DataPermissionType.MASK_CARDNO)
    private String bankCardNo;
}
