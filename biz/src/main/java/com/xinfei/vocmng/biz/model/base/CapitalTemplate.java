/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xinfei.vocmng.biz.model.base;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @version $ CapitalTemplate, v 0.1 2024/12/27 11:03 wancheng.qu Exp $
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class CapitalTemplate implements Serializable {

    private String type; //附件类型：excel,word
    private String text;//正文
    private boolean sendText; //正文是否是文本
    private Word word;
    private Excel excel;

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Word implements Serializable {
        private String content;

    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Excel implements Serializable {
        private List<Entry> fields;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Entry implements Serializable {
        private String name;
        private String key;
    }
}
