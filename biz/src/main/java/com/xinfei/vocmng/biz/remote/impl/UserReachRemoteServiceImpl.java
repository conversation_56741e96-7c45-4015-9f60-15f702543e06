package com.xinfei.vocmng.biz.remote.impl;

import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.core.lang.UUID;
import com.alibaba.fastjson2.util.DateUtils;
import com.google.common.collect.Lists;
import com.xinfei.listcore.facade.rr.UserListRequest;
import com.xinfei.listcore.facade.rr.dto.UserListDto;
import com.xinfei.vocmng.biz.model.exception.TechplayException;
import com.xinfei.vocmng.biz.model.resp.ApiResponse;
import com.xinfei.vocmng.biz.remote.UserReachRemoteService;
import com.xinfei.vocmng.biz.rr.dto.CallRecordDto;
import com.xinfei.vocmng.biz.rr.dto.MarketingBlackDto;
import com.xinfei.vocmng.biz.rr.dto.SmsRecordsDto;
import com.xinfei.vocmng.biz.rr.dto.StopMarketDto;
import com.xinfei.vocmng.biz.rr.request.*;
import com.xinfei.vocmng.biz.rr.response.Paging;
import com.xinfei.vocmng.biz.util.LocalDateTimeUtils;
import com.xinfei.vocmng.biz.util.UserContextHolder;
import com.xinfei.vocmng.itl.CallCenterFeignClient;
import com.xinfei.vocmng.itl.client.exeception.ClientException;
import com.xinfei.vocmng.itl.client.exeception.ErrorLevelsEnum;
import com.xinfei.vocmng.itl.client.exeception.TechplayErrDtlEnum;
import com.xinfei.vocmng.itl.client.feign.ListCoreFacadeClient;
import com.xinfei.vocmng.itl.client.feign.service.CisFacadeClientService;
import com.xinfei.vocmng.itl.client.http.SmsCenterFeignClient;
import com.xinfei.vocmng.itl.client.http.UnionClient;
import com.xinfei.vocmng.itl.model.enums.SmsDeliveryStatusEnum;
import com.xinfei.vocmng.itl.rr.*;
import com.xinfei.vocmng.itl.util.LogUtil;
import com.xinfei.xfframework.common.JsonUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version $ UserReachRemoteServiceImpl, v 0.1 2023/12/28 14:39 qu.lu Exp $
 */
@Slf4j
@Service
public class UserReachRemoteServiceImpl implements UserReachRemoteService {

    @Autowired
    private CallCenterFeignClient callCenterFeignClient;

    @Autowired
    private SmsCenterFeignClient smsCenterFeignClient;

    @Resource
    private CisFacadeClientService cisFacadeClientService;

    @Resource
    private UnionClient unionClient;

    @Resource
    private ListCoreFacadeClient listCoreFacadeClient;

    @Override
    public ApiResponse<Paging<CallRecordDto>> queryCallRecordList(QueryCallListRequest request) {
        String validateResult = validateCallListRequest(request);
        if (StringUtils.isNotEmpty(validateResult)) {
            return ApiResponse.paramIllegal(validateResult);
        }

        try {
            CallListRequest callListRequest = buildCallListRequest(request);
            CallCenterBaseResponse<PageDataInfo<List<CallRecordDetail>>> response = callCenterFeignClient.queryCallRecordList(callListRequest);
            log.info(LogUtil.clientLog("CallCenterFeignClient", "queryCallRecordList", callListRequest, response));
            if (!response.isSuccess()) {
                log.error("query call list record failed, request={},response={}", request, response);
                return ApiResponse.fail("query call list record failed.");
            }

            PageDataInfo<List<CallRecordDetail>> pageDataInfo = response.getData();
            if (pageDataInfo == null || CollectionUtils.isNotEmpty(pageDataInfo.getRows())) {
                return ApiResponse.success(new Paging<>(request.getPageNumber(), request.getPageSize()));
            }

            List<CallRecordDto> recordList = convertCallRecord(pageDataInfo.getRows());
            return ApiResponse.success(new Paging<>(recordList, request.getPageNumber(), request.getPageSize(), pageDataInfo.getTotal()));
        } catch (Exception e) {
            log.error("query call list failed, request=" + request, e);
        }

        return ApiResponse.fail("query call list failed.");
    }

    @Override
    public ApiResponse<Paging<SmsRecordsDto>> querySmsRecordList(QuerySmsRecordRequest request) {
        String validateResult = validateSmsRecordRequest(request);
        if (StringUtils.isNotEmpty(validateResult)) {
            return ApiResponse.paramIllegal(validateResult);
        }

        try {
            SmsRecordsRequest baseRequest = buildSmsRequest(request);
            SmsResponse<PageResultInfo<List<SmsRecordsDetail>>> response = smsCenterFeignClient.queryUserSmsRecordsList(baseRequest);
            log.info(LogUtil.clientLog("SmsCenterFeignClient", "queryUserSmsRecordsList", baseRequest, response));
            if (!response.isSuccess()) {
                log.error("query sms record list failed, request={},response={}", request, response);
                return ApiResponse.fail("query sms record list failed.");
            }

            return ApiResponse.success(convertSmsRecord(response.getResponse()));
        } catch (Exception e) {
            log.error("query sms record failed, request=" + request, e);
        }

        return ApiResponse.fail("query sms record failed.");
    }

    @Override
    public ApiResponse<Paging<StopMarketDto>> queryStopMarketList(StopMarketRequest request) {
        UnionBaseReq<MarketListReq> r = buildReq(request);
        try {
            SmsResponse<PageResultInfo<List<MarketListResp>>> res = unionClient.list(r);
            log.info(LogUtil.clientLog("UnionClient", "marketList", r, res));
            if (!res.isSuccess()) {
                log.warn("queryStopMarketList failed, request={},response={}", JsonUtil.toJson(r), JsonUtil.toJson(res));
                return ApiResponse.fail("query failed");
            }
            return ApiResponse.success(convertData(res.getResponse(), request.getPageSize(), request.getCurrentPage()));
        } catch (Exception e) {
            log.error(" queryStopMarketList convert data error", e);
            return ApiResponse.fail("query failed");
        }
    }

    private Paging<StopMarketDto> convertData(PageResultInfo<List<MarketListResp>> response, int pageSize, int currentPage) {
        if (Objects.nonNull(response) && CollectionUtils.isNotEmpty(response.getList())) {
            List<StopMarketDto> res = response.getList().stream().map(r -> {
                StopMarketDto dto = new StopMarketDto();
                BeanUtils.copyProperties(r, dto);
                return dto;
            }).collect(Collectors.toList());
            return new Paging<>(res, currentPage, pageSize, response.getTotal());
        }
        return new Paging<>();
    }

    private UnionBaseReq<MarketListReq> buildReq(StopMarketRequest request) {
        UnionBaseReq<MarketListReq> u = new UnionBaseReq<>();
        u.setUa("vocmng");
        u.setCall("/market/list");
        MarketListReq m = new MarketListReq();
        m.setMobile(request.getMobile());
        m.setStatus(request.getStatus());
        m.setBeginUpdatedTime(request.getBeginTime());
        m.setEndUpdatedTime(request.getEndTime());
        m.setPage(request.getCurrentPage());
        m.setPageNum(request.getPageSize());
        u.setArgs(m);
        u.setSign(UUID.fastUUID().toString());
        u.setTimestamp(System.currentTimeMillis());

        return u;
    }

    @Override
    public ApiResponse<Boolean> addStopMarket(StopMarketRequest request) {
        if (StringUtils.isBlank(request.getMobile()) || StringUtils.isBlank(request.getDisableReason()) || CollectionUtils.isEmpty(request.getAppList())) {
            return ApiResponse.paramIllegal("mobile,disableReason,appList can not be null");
        }
        UnionBaseReq<MarketUpdateReq> r = buildMarketUpdateReq(request);
        for (String app : request.getAppList()) {
            MarketUpdateReq m = new MarketUpdateReq();
            m.setMobile(request.getMobile());
            m.setApp(app);
            m.setStatus(request.getStatus());
            m.setOperator(UserContextHolder.getUserContext().getName());
            m.setOperatorId(0);
            m.setDisableMarketTime(LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
            m.setDisableReason(request.getDisableReason());
            r.setArgs(m);
            saveStopMarket(r);
        }
        return ApiResponse.success(Boolean.TRUE);
    }

    private UnionBaseReq<MarketUpdateReq> buildMarketUpdateReq(StopMarketRequest request) {
        UnionBaseReq<MarketUpdateReq> u = new UnionBaseReq<>();
        u.setUa("vocmng");
        u.setCall("/market/mobile");
        u.setSign(UUID.fastUUID().toString());
        u.setTimestamp(System.currentTimeMillis());
        return u;
    }

    private void saveStopMarket(UnionBaseReq<MarketUpdateReq> r) {
        try {
            SmsResponse<String> res = unionClient.marketMobile(r);
            log.info(LogUtil.clientLog("UnionClient", "marketMobile", r, res));
            if (!res.isSuccess()) {
                log.warn("marketMobile failed, request={},response={}", JsonUtil.toJson(r), JsonUtil.toJson(res));
                throw new ClientException(TechplayErrDtlEnum.CLIENT_CODE_ERROR, res.getMessage(), ErrorLevelsEnum.ERROR);
            }
        } catch (Exception e) {
            log.error(" marketMobile  error", e);
            throw new TechplayException(com.xinfei.vocmng.biz.model.enums.TechplayErrDtlEnum.CLIENT_CODE_ERROR, r.getArgs().getMobile() + e.getMessage());
        }
    }

    @Override
    public ApiResponse<Boolean> updateStopMarket(StopMarketRequest request) {
        if (StringUtils.isBlank(request.getMobile()) || StringUtils.isBlank(request.getStatus()) || StringUtils.isBlank(request.getApp())) {
            return ApiResponse.paramIllegal("mobile,status,app can not be null");
        }
        UnionBaseReq<MarketUpdateReq> r = buildMarketUpdateReq(request);
        MarketUpdateReq m = new MarketUpdateReq();
        m.setMobile(request.getMobile());
        m.setApp(request.getApp());
        m.setStatus(request.getStatus());
        m.setOperator(UserContextHolder.getUserContext().getName());
        m.setOperatorId(0);
        if (Objects.equals("0", request.getStatus())) {
            m.setEnableMarketTime(LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        } else {
            m.setDisableMarketTime(LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
            m.setDisableReason(request.getDisableReason());
        }

        r.setArgs(m);
        saveStopMarket(r);
        return ApiResponse.success(Boolean.TRUE);
    }

    @Override
    public ApiResponse<List<MarketingBlackDto>> marketingBlackList(MarketingBlackListRequest request) {
        List<String> mobiles = request.getMobiles();

        Map<String, List<MarketingBlackDto>> result = new HashMap<>();

        // 处理用户列表
        fetchUserList(mobiles, result, request.getMobile());

        // 处理短信黑名单
        fetchSmsBlackList(request, result , request.getMobile());

        // 过滤出日期结束时间较大的记录并计算剩余天数
        List<MarketingBlackDto> finalResult = filterAndCalculateDays(result);
        return ApiResponse.success(finalResult);
    }

    private void fetchUserList(List<String> mobiles, Map<String, List<MarketingBlackDto>> resultMap, String myMobile) {
        Map<String, String> mobileMap = cisFacadeClientService.getEncodeMobileMapKey(mobiles);
        for (String mobile : mobiles) {
            UserListRequest userListRequest = new UserListRequest();
            userListRequest.setListType("mobile");
            userListRequest.setListDetail(mobileMap.get(mobile));
            List<MarketingBlackDto> result = new ArrayList<>();
            for (UserListDto userListDto : listCoreFacadeClient.getList(userListRequest)) {
                if (userListDto.getStatus() != 0) {
                    MarketingBlackDto dto = new MarketingBlackDto();
                    BeanUtils.copyProperties(userListDto, dto);
                    dto.setDateStart(DateUtils.format(userListDto.getDateStart(), "yyyy-MM-dd HH:mm:ss"));
                    dto.setDateEnd(DateUtils.format(userListDto.getDateEnd(), "yyyy-MM-dd HH:mm:ss"));
                    dto.setUpdatedTime(DateUtils.format(userListDto.getUpdatedTime(), "yyyy-MM-dd HH:mm:ss"));
                    dto.setSource("1");
                    dto.setMobile(mobile);
                    dto.setMobileEncrypted(mobile);
                    dto.setId(String.valueOf(userListDto.getId()));
                    dto.setRelatedRelationships(mobile.equals(myMobile) ? "本人手机号" : "关联手机号");
                    result.add(dto);
                }
            }
            resultMap.put(mobile, result);
        }
    }

    private void fetchSmsBlackList(MarketingBlackListRequest request, Map<String, List<MarketingBlackDto>> resultMap ,String myMobile) {
        for (String mobile : request.getMobiles()) {
            SmsBlackListRequest smsRequest = new SmsBlackListRequest();
            smsRequest.setMobile(mobile);
            smsRequest.setApp(request.getApp());
            List<MarketingBlackDto> result = resultMap.get(mobile);
            for (SmsBlackListDetail smsBlackListDetail : listCoreFacadeClient.smsBlackList(smsRequest)) {
                if (smsBlackListDetail != null) {
                    Date expireDate = DateUtils.parseDate(smsBlackListDetail.getExpireTime(), "yyyy-MM-dd HH:mm:ss");
                    if (expireDate.after(new Date())) {
                        for (String type : smsBlackListDetail.getBizTypeList()) {
                            MarketingBlackDto dto = new MarketingBlackDto();
                            dto.setSource("2");
                            dto.setMobile(mobile);
                            dto.setMobileEncrypted(mobile);
                            dto.setDateEnd(smsBlackListDetail.getExpireTime());
                            dto.setStatus(2);
                            dto.setRelatedRelationships(mobile.equals(myMobile) ? "本人手机号" : "关联手机号");
                            assignLabelKeys(dto, type);
                            result.add(dto);
                        }
                    }
                }
            }
            resultMap.put(mobile, result);
        }
    }

    private List<SmsBlackListDetail> fetchSmsBlack(MarketingBlackListRequest request) {
        SmsBlackListRequest smsRequest = new SmsBlackListRequest();
        smsRequest.setMobile(request.getMobile());
        smsRequest.setApp(request.getApp());
        return listCoreFacadeClient.smsBlackList(smsRequest);
    }

    private void assignLabelKeys(MarketingBlackDto dto, String type) {
        if (type.contains("notify")) {
            dto.setMainLabelKey("Notification_Text_Blacklist");
            dto.setSubLabelKey("Sys_Text_Blacklist");
        } else if (type.contains("marketing")) {
            dto.setMainLabelKey("Notification_Text_Blacklist");
            dto.setSubLabelKey("Market_Text_Blacklist");
        } else if (type.contains("collection") || type.equals("collection_heavy")) {
            dto.setMainLabelKey("Notification_Text_Blacklist");
            dto.setSubLabelKey("LoanPost_Text_Blacklist");
        }
    }

    private List<MarketingBlackDto> filterAndCalculateDays(Map<String, List<MarketingBlackDto>> resultMap) {
        LocalDateTime now = LocalDateTime.now();
        List<MarketingBlackDto> result = new ArrayList<>();
        for (Map.Entry<String, List<MarketingBlackDto>> entry : resultMap.entrySet()) {
            Map<String, MarketingBlackDto> uniqueMap = new HashMap<>();
            for (MarketingBlackDto dto : entry.getValue()) {
                uniqueMap.merge(dto.getSubLabelKey(), dto, (existingDto, newDto) ->
                        DateUtils.parseDate(existingDto.getDateEnd(), "yyyy-MM-dd HH:mm:ss")
                                .compareTo(DateUtils.parseDate(newDto.getDateEnd(), "yyyy-MM-dd HH:mm:ss")) < 0 ? newDto : existingDto
                );

                LocalDateTime endDate = LocalDateTimeUtils.toLocalDateTime(
                        DateUtils.parseDate(dto.getDateEnd(), "yyyy-MM-dd HH:mm:ss"));
                long daysBetween = ChronoUnit.DAYS.between(now, endDate);
                if (now.until(endDate, ChronoUnit.HOURS) % 24 > 0) {
                    daysBetween += 1; // 向上取整
                }
                dto.setDay(daysBetween);
            }
            result.addAll(new ArrayList<>(uniqueMap.values()));
        }

        return result;
    }

    @Override
    public ApiResponse<Boolean> marketingBlackCreate(List<MarketingBlackRequest> request) {
        for (MarketingBlackRequest marketingBlackRequest : request) {
            UserListDto userListDto = new UserListDto();
            BeanUtils.copyProperties(marketingBlackRequest, userListDto);
            userListDto.setListType("mobile");
            userListDto.setDepartment("客服");
            userListDto.setOrgId("12");
            userListDto.setListDetail(cisFacadeClientService.getEncodeMobileLocal(marketingBlackRequest.getMobile()));
            userListDto.setDateStart(new Date());
            userListDto.setDateEnd(LocalDateTimeUtils.parseDateByLocalDateTime(LocalDateTime.now().plusDays(marketingBlackRequest.getDay())));
            listCoreFacadeClient.create(userListDto);
        }
        return ApiResponse.success(true);
    }

    @Override
    public ApiResponse<Boolean> marketingBlackUpdate(MarketingBlackRequest request) {
        boolean isSuccess = false;

        if ("1".equals(request.getSource())) {
            isSuccess = updateUserStatus(request);
        } else {
            MarketingBlackListRequest blackListRequest = new MarketingBlackListRequest();
            blackListRequest.setMobile(request.getMobile());
            blackListRequest.setApp(request.getApp());
            List<SmsBlackListDetail> smsListDetails = fetchSmsBlack(blackListRequest);
            if (CollectionUtils.isNotEmpty(smsListDetails)) {
                isSuccess = updateSmsBlacklist(request, smsListDetails);
            }
        }
        return ApiResponse.success(isSuccess);
    }

    private boolean updateUserStatus(MarketingBlackRequest request) {
        UserListDto userListDto = new UserListDto();
        userListDto.setId(Long.parseLong(request.getId()));
        userListDto.setStatus(0);
        userListDto.setOrgId("12");
        userListDto.setOperator(request.getOperator());
        return listCoreFacadeClient.update(userListDto);
    }

    private boolean updateSmsBlacklist(MarketingBlackRequest request, List<SmsBlackListDetail> smsListDetails) {
        SmsBlackListDetail detail = smsListDetails.get(0);
        SmsBlackEditRequest smsBlackEditRequest = new SmsBlackEditRequest();
        smsBlackEditRequest.setMobile(request.getMobile());
        smsBlackEditRequest.setApp(request.getApp());
        String bizType = getString(request, detail.getBizTypeList());
        if (StringUtils.isNotBlank(bizType)) {
            smsBlackEditRequest.setExpireTime(detail.getExpireTime());
            smsBlackEditRequest.setBizType(bizType);
        } else {
            smsBlackEditRequest.setBizType(String.join(",", detail.getBizTypeList()));
            smsBlackEditRequest.setExpireTime(DateUtils.format(new Date()));
        }
        return listCoreFacadeClient.smsBlackEdit(smsBlackEditRequest);
    }

    private String getString(MarketingBlackRequest request, List<String> bizTypeList) {
        String bizType = String.join(",", bizTypeList);
        List<String> toRemove = new ArrayList<>();
        toRemove.addAll(Arrays.asList("batch_marketing", "batch_notify", "batch_collection", "batch_collection_heavy"));
        if (request.getSubLabelKey().equals("Sys_Text_Blacklist")) {
            toRemove.add("notify");
        } else if (request.getSubLabelKey().equals("Market_Text_Blacklist")) {
            toRemove.add("marketing");
        } else if (request.getSubLabelKey().equals("LoanPost_Text_Blacklist")) {
            toRemove.add("collection_heavy");
            toRemove.add("collection");
        } else {
            return bizType;
        }
        bizType = filterBizType(bizType, toRemove);
        return bizType;
    }

    public static String filterBizType(String bizType, List<String> toRemove) {
        return Arrays.stream(bizType.split(",")).filter(item -> !toRemove.contains(item))
                .collect(Collectors.joining(","));
    }

    private List<CallRecordDto> convertCallRecord(List<CallRecordDetail> recordDetails) {
        if (CollectionUtils.isEmpty(recordDetails)) {
            return Collections.emptyList();
        }

        List<CallRecordDto> result = Lists.newArrayListWithCapacity(recordDetails.size());
        CallRecordDto dto;
        for (CallRecordDetail detail : recordDetails) {
            dto = new CallRecordDto();
            BeanUtils.copyProperties(detail, dto);
            result.add(dto);
        }

        return result;
    }

    private CallListRequest buildCallListRequest(QueryCallListRequest request) {
        CallListRequest result = new CallListRequest();
        BeanUtils.copyProperties(request, result);

        return result;
    }

    private String validateCallListRequest(QueryCallListRequest request) {
        if (request == null) {
            return "param is required.";
        }
        if (request.getPageNumber() == null || request.getPageNumber() <= 0) {
            return "pageNumber is required.";
        }
        if (request.getPageSize() == null || request.getPageSize() <= 0) {
            return "pageSize is required.";
        }
        if (StringUtils.isEmpty(request.getOriginMobile())) {
            return "originMobile is required.";
        }
        if (StringUtils.isEmpty(request.getAppKey())) {
            return "appKey is required.";
        }
        if (StringUtils.isEmpty(request.getStartTime())) {
            return "startTime is required.";
        }
        if (StringUtils.isEmpty(request.getEndTime())) {
            return "endTime is required.";
        }

        return null;
    }

    private Paging<SmsRecordsDto> convertSmsRecord(PageResultInfo<List<SmsRecordsDetail>> recordDetails) {
        if (CollectionUtils.isEmpty(recordDetails.getList())) {
            return null;
        }

        Paging<SmsRecordsDto> result = new Paging<>();
        List<SmsRecordsDto> smsResult = Lists.newArrayListWithCapacity(recordDetails.getList().size());
        SmsRecordsDto dto;
        for (SmsRecordsDetail detail : recordDetails.getList()) {
            dto = new SmsRecordsDto();
            BeanUtils.copyProperties(detail, dto);
            dto.setDeliveryStatus(SmsDeliveryStatusEnum.getDeliveryStatusMsgByStatus(detail.getDeliveryStatus()));
            dto.setCreatedTime(LocalDateTimeUtils.format(LocalDateTimeUtil.parse(detail.getCreatedTime())));
            dto.setMobile(cisFacadeClientService.batchDecrypt(detail.getMobileEncrypt()));
            smsResult.add(dto);
        }
        result.setList(smsResult);
        result.setPageSize(recordDetails.getPage_size());
        Integer totalPage = 0;
        if (recordDetails.getTotal() <= 0 || recordDetails.getPage_size() <= 0) {
            totalPage = 0;
        } else {
            totalPage = (recordDetails.getTotal() + recordDetails.getPage_size() - 1) / recordDetails.getPage_size();
        }
        result.setTotal(recordDetails.getTotal());
        result.setTotalPage(totalPage);
        result.setCurrentPage(recordDetails.getCur_page());
        return result;
    }

    private SmsRecordsRequest buildSmsRequest(QuerySmsRecordRequest request) {

        SmsRecordsRequest smsRequest = new SmsRecordsRequest();
        BeanUtils.copyProperties(request, smsRequest);
        if (request.getCreateEndTime() != null) {
            smsRequest.setCreateEndTime(LocalDateTimeUtils.parseDateByLocalDateTime(request.getCreateEndTime()).getTime());
        }

        if (request.getCreateStartTime() != null) {
            smsRequest.setCreateStartTime(LocalDateTimeUtils.parseDateByLocalDateTime(request.getCreateStartTime()).getTime());
        }

        smsRequest.setMobile(cisFacadeClientService.getEncodeMobileLocal(smsRequest.getMobile()));
        smsRequest.setPage(request.getCurrentPage());
        smsRequest.setDescFlag(1);
        return smsRequest;
    }

    private String validateSmsRecordRequest(QuerySmsRecordRequest request) {
        if (request == null) {
            return "param is required.";
        }
        if (StringUtils.isEmpty(request.getMobile())) {
            return "mobile is required.";
        }

        return null;
    }
}
