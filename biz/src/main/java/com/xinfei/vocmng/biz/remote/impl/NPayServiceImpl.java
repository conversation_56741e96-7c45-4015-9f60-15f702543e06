package com.xinfei.vocmng.biz.remote.impl;

import com.xinfei.vocmng.biz.constants.VocConstants;
import com.xinfei.vocmng.biz.remote.NPayService;
import com.xinfei.vocmng.itl.NPayFeignClient;
import com.xinfei.vocmng.itl.rr.NPayResponse;
import com.xinfei.vocmng.itl.rr.PublicAccountInfo;
import com.xinfei.vocmng.itl.rr.PublicAccountRequest;
import com.xinfei.vocmng.itl.util.LogUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.UUID;


/**
 * <AUTHOR>
 * @version $ NPayServiceImpl, v 0.1 2025/03/20 15:28 pengming.liu Exp $
 */
@Slf4j
@Service
public class NPayServiceImpl implements NPayService {

    @Autowired
    private NPayFeignClient nPayFeignClient;

    @Override
    public PublicAccountInfo queryPublicAccountInfo(PublicAccountRequest request) {
        try {
            request.setRequestId(UUID.randomUUID().toString());
            request.setBizChannel(VocConstants.APP_NAME);
            NPayResponse<PublicAccountInfo> nPayResponse = nPayFeignClient.queryPublicAccountInfo(request);
            log.info(LogUtil.clientLog("NPayFeignClient", "queryPublicAccountInfo", request, nPayResponse));
            if (nPayResponse == null || !nPayResponse.isSuccess() || nPayResponse.getData() == null) {
                log.error("query queryPublicAccountInfo failed, request={},response={}", request, nPayResponse);
                return null;
            }
            return nPayResponse.getData();
        } catch (Exception e) {
            log.error("queryPublicAccountInfo, request=" + request, e);
            return null;
        }
    }
}
