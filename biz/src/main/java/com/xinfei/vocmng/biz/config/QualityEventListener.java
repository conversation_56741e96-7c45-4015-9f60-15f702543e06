/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xinfei.vocmng.biz.config;


import com.xinfei.vocmng.biz.constants.LoginUserConstants;
import com.xinfei.vocmng.biz.model.event.QualitySseEvent;
import com.xinfei.vocmng.biz.mq.SseProducer;
import com.xinfei.vocmng.biz.util.RandomUtil;
import com.xinfei.xfframework.common.JsonUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @version $ CustomEventListener, v 0.1 2024/2/7 15:35 wancheng.qu Exp $
 */
@Slf4j
@Component
public class QualityEventListener {

    @Resource
    private SseProducer sseProducer;

    @EventListener
    public void handleQualityEvent(QualitySseEvent event) {
        String userIdentify = event.getUserIdentify();
        log.info("receive quality event,useridentify={},data={}", userIdentify, JsonUtil.toJson(event));
        Map<String, Object> data = new HashMap<>();
        data.put("userIdentify", userIdentify);
        data.put("customerPhone", event.getCustomerPhone());
        data.put("displayNumber", event.getDisplayNumber());
        data.put("callId", event.getCallId());
        data.put("status", event.getStatus());
        String key = getRedisKey(userIdentify, event.getCallId());
        data.put("mesgId", key);
        log.info("sse quality redis key={},value={}", key, userIdentify);

        sseProducer.sendSseMsg(data);
    }

    private String getRedisKey(String userIdentify, String callId) {
        String random = RandomUtil.get5Random();
        return LoginUserConstants.SSE_KEY + random + userIdentify + "CALLID" + callId;
    }
}

