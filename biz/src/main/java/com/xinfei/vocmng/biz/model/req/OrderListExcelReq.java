/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2023 All Rights Reserved.
 */
package com.xinfei.vocmng.biz.model.req;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 *
 * <AUTHOR>
 * @version $ OrderListExcelReq, v 0.1 2025/01/03 17:23 pengming.liu Exp $
 */

@Data
public class OrderListExcelReq {

    @ExcelProperty("期数")
    private Integer term;

    @ExcelProperty("状态信息")
    private String rpyFlag;

    @ExcelProperty("账单号")
    private String planNo;

    @ExcelProperty("订单号")
    private String oderNo;

    @ExcelProperty("初始本金")
    private BigDecimal prinAmt;

    @ExcelProperty("实还本金")
    private BigDecimal prinAmtFact;

    @ExcelProperty("初始利息")
    private BigDecimal intAmt;

    @ExcelProperty("已还利息")
    private BigDecimal intAmtFact;

    @ExcelProperty("减免费用")
    private BigDecimal deductSum;

    @ExcelProperty("结清手续费")
    private BigDecimal fee4Amt;

    @ExcelProperty("初始担保费")
    private BigDecimal fee1Amt;

    @ExcelProperty("实还担保费")
    private BigDecimal fee1AmtFact;

    @ExcelProperty("初始反担保费")
    private BigDecimal fee2Amt;

    @ExcelProperty("实还反担保费")
    private BigDecimal fee2AmtFact;

    @ExcelProperty("实还催费")
    private BigDecimal fee6Amt;

    @ExcelProperty("实还罚息")
    private BigDecimal ointAmt;

    @ExcelProperty("实还贷后逾期管理费")
    private BigDecimal fee3Amt;

    @ExcelProperty("初始到期应还")
    private BigDecimal sumAmt;

    @ExcelProperty("实还总额")
    private BigDecimal sumAmtFact;

    @ExcelProperty("创建时间")
    private LocalDateTime dateCreated;

    @ExcelProperty("账单日")
    private LocalDate dateDue;

    @ExcelProperty("容时期限")
    private LocalDate dateGrace;

    @ExcelProperty("还款时间")
    private LocalDateTime dateSettle;

}