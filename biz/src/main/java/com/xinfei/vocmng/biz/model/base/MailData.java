/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xinfei.vocmng.biz.model.base;

import lombok.Data;

import java.io.ByteArrayOutputStream;
import java.io.Serializable;
import java.util.List;

/**
 *
 * <AUTHOR>
 * @version $ MailData, v 0.1 2024/12/30 19:26 wancheng.qu Exp $
 */
@Data
public class MailData implements Serializable {

    private String text;//正文
    private boolean sendText; //正文是否是文本
    private List<ByteArrayOutputStream> attachment; //附件文件
    private List<String> fileName; //文件名

}