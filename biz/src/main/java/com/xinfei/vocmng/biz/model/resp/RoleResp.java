/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2023 All Rights Reserved.
 */
package com.xinfei.vocmng.biz.model.resp;


import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.xinfei.vocmng.biz.model.base.TimeFormat;
import com.xinfei.vocmng.biz.model.req.ControlReq;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 *
 * <AUTHOR>
 * @version $ RoleResp, v 0.1 2023/12/20 16:04 wancheng.qu Exp $
 */

@Data
public class RoleResp implements Serializable {

    @ApiModelProperty(value = "角色id")
    private Long id;

    @ApiModelProperty(value = "角色名称")
    private String name;

    @ApiModelProperty(value = "角色描述")
    private String description;

    @ApiModelProperty(value = "创建时间")
    @JsonFormat(pattern = TimeFormat.DATE_TIME)
    private LocalDateTime createdTime;

    @ApiModelProperty(value = "修改时间")
    @JsonFormat(pattern = TimeFormat.DATE_TIME)
    private LocalDateTime updatedTime;

    @ApiModelProperty(value = "创建人")
    private String createdUser;

    @ApiModelProperty(value = "修改人")
    private String updateUser;

    @ApiModelProperty(value = "资源id")
    private List<Long> resourceList;

    @ApiModelProperty(value = "数据资源列表id")
    private List<Long> dataList;

    @ApiModelProperty(value = "费控列表")
    private List<ControlReq> controlList;

    @ApiModelProperty(value = "新费控列表")
    private List<FeeStrategyConfig> feeStrategyConfigs;


}