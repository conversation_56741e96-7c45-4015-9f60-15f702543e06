/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xinfei.vocmng.biz.model.req;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @version $ SetComSummaryRequest, v 0.1 2024-02-04 11:38 junjie.yan Exp $
 */
@Data

public class SetComSummaryRequest {

    @ApiModelProperty(value = "通话编号")
    private String call_id;

    @ApiModelProperty(value = "呼入号码")
    private String customer_phone;

    @ApiModelProperty(value = "坐席id")
    private String agent_id;

    @ApiModelProperty(value = "中继号")
    private String display_number;

    @ApiModelProperty(value = "事件类型")
    private String workflow;

    @ApiModelProperty(value = "接通类型")
    private String call_result;

    @ApiModelProperty(value = "接通类型(接听:agent_incall,挂断:hangup)")
    private String occasion;

}