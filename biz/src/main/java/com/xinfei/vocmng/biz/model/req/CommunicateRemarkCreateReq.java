/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2023 All Rights Reserved.
 */
package com.xinfei.vocmng.biz.model.req;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 *
 * <AUTHOR>
 * @version $ CommunicateRemarkCreateReq, v 0.1 2023/12/19 15:07 wancheng.qu Exp $
 */
@Data
public class CommunicateRemarkCreateReq implements Serializable {

    @ApiModelProperty(value = "会话小结id")
    @NotBlank(message = "会话小结id为空")
    private Long communicateSummaryId;

    @ApiModelProperty(value = "备注")
    @NotBlank(message = "备注不能为空")
    @Size(max = 100, message = "备注长度不能超过100")
    private String remark;

}