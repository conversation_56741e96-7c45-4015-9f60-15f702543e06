/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xinfei.vocmng.biz.model.req;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.Getter;

import java.io.Serializable;

/**
 *
 * <AUTHOR>
 * @version $ UserConsultationReq, v 0.1 2024/5/13 16:35 wancheng.qu Exp $
 */
@Data
public class UserConsultationReq implements Serializable {

    @ApiModelProperty(value = "ip地址")
    private String ip;

    @ApiModelProperty(value = "userNo")
    private Long userNo;

    @ApiModelProperty(value = "openId")
    private String openId;

    @ApiModelProperty(value = "设备id")
    private String deviceId;

    @ApiModelProperty(value = "custNo")
    private String custNo;

}