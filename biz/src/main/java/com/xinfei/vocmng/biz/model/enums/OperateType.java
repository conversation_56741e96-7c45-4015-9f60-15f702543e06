package com.xinfei.vocmng.biz.model.enums;

/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2023 All Rights Reserved.
 */
public enum OperateType {
    TYPE_ONE(1, "新增操作"),
    TYPE_TWO(2, "修改操作"),
    TYPE_THREE(3, "上传操作"),
    TYPE_FOUR(4, "下载操作"),
    TYPE_FIVE(5, "数据关联操作"),
    TYPE_SIX(6, "修改数据关联操作"),
    TYPE_SEVEN(7, "还款关联操作"),
    TYPE_EIGHT(8, "退款关联操作");

    private final int code;
    private final String description;

    OperateType(int code, String description) {
        this.code = code;
        this.description = description;
    }

    public int getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }
}