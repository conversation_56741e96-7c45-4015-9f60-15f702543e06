package com.xinfei.vocmng.biz.model.enums;

/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2025 All Rights Reserved.
 */
public enum SummaryStatusEnum {
    // 待补充
    PENDING(1, "待补充"),
    // 跟进中
    ONGOING(2, "跟进中"),
    // 已完成
    COMPLETED(3, "已完成");

    /**
     * 枚举值
     */
    private final Integer code;
    /**
     * 显示名称
     */
    private final String name;

    SummaryStatusEnum(Integer code, String name) {
        this.code = code;
        this.name = name;
    }

    /**
     * 获取枚举值
     */
    public Integer getCode() {
        return code;
    }

    /**
     * 获取显示名称
     */
    public String getName() {
        return name;
    }

    /**
     * 根据code获取枚举
     */
    public static SummaryStatusEnum getByCode(Integer code) {
        for (SummaryStatusEnum value : values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return null;
    }
}
