/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2023 All Rights Reserved.
 */
package com.xinfei.vocmng.biz.model.resp;

import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.xinfei.vocmng.biz.model.base.TimeFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 *
 * <AUTHOR>
 * @version $ LabelUserResp, v 0.1 2023/12/20 21:16 wancheng.qu Exp $
 */
@Data
public class LabelUserResp implements Serializable {

    @ApiModelProperty(value = "id")
    private Long id;

    @ApiModelProperty(value = "联系方式")
    private String mobile;

    @ApiModelProperty(value = "插入时间")
    @JsonFormat(pattern = TimeFormat.DATE_TIME)
    private LocalDateTime createdTime;

    @ApiModelProperty(value = "创建人")
    private String createdUser;




}