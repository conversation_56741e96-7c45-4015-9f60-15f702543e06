/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2023 All Rights Reserved.
 */
package com.xinfei.vocmng.biz.service.impl;

import apollo.com.google.gson.reflect.TypeToken;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fasterxml.jackson.core.type.TypeReference;
import com.google.gson.Gson;
import com.xinfei.cashiercore.common.service.facade.request.management.ShortLinkCloseRequest;
import com.xinfei.cashiercore.common.service.facade.request.management.ShortLinkCreateRequest;
import com.xinfei.cashiercore.common.service.facade.request.management.WhitelistMarkRequest;
import com.xinfei.cashiercore.common.service.facade.vo.management.ShortLinkVO;
import com.xinfei.lendtrade.facade.rr.ManageOrderDetailRequest;
import com.xinfei.lendtrade.facade.rr.ManageOrderListRequest;
import com.xinfei.lendtrade.facade.rr.dto.ManageOrderDetailDTO;
import com.xinfei.psenginecore.facade.rr.dto.AdvanceOrderListOuterDTO;
import com.xinfei.repaytrade.facade.rr.dto.*;
import com.xinfei.repaytrade.facade.rr.enums.FeeSubjectEnum;
import com.xinfei.repaytrade.facade.rr.request.CreateRepaymentPlanRequest;
import com.xinfei.repaytrade.facade.rr.request.GroupPaymentApplyRequest;
import com.xinfei.repaytrade.facade.rr.request.RepayApplyRequest;
import com.xinfei.repaytrade.facade.rr.request.RepayLoanCalcRequest;
import com.xinfei.repaytrade.facade.rr.request.live.LiveRepayApplyRequest;
import com.xinfei.repaytrade.facade.rr.response.QueryLoansAreRepayingResponse;
import com.xinfei.repaytrade.facade.rr.response.QueryRepaymentPlanResponse;
import com.xinfei.repaytrade.facade.rr.response.QueryRepaymentPlanResultResponse;
import com.xinfei.repaytrade.facade.rr.response.RepayLoanCalcResponse;
import com.xinfei.repaytrade.facade.rr.response.live.LiveRecordResponse;
import com.xinfei.repaytrade.facade.rr.response.reduction.CalculateReductionAmountResponse;
import com.xinfei.repaytrade.facade.rr.response.reduction.DetailListResponse;
import com.xinfei.repaytrade.facade.rr.response.reduction.ReductionAmountResponse;
import com.xinfei.repaytrade.facade.vo.repay.RepayCancelVO;
import com.xinfei.vocmng.biz.config.LoginUserConfig;
import com.xinfei.vocmng.biz.config.VocConfig;
import com.xinfei.vocmng.biz.constants.RedisKeyConstants;
import com.xinfei.vocmng.biz.constants.ReqNoConstants;
import com.xinfei.vocmng.biz.mapstruct.RepayConverter;
import com.xinfei.vocmng.biz.mapstruct.RepayTradeConverter;
import com.xinfei.vocmng.biz.model.base.ControlItemValue;
import com.xinfei.vocmng.biz.model.base.ControlRes;
import com.xinfei.vocmng.biz.model.base.PageResultResponse;
import com.xinfei.vocmng.biz.model.enums.*;
import com.xinfei.vocmng.biz.model.exception.IgnoreException;
import com.xinfei.vocmng.biz.model.exception.TechplayException;
import com.xinfei.vocmng.biz.remote.NPayService;
import com.xinfei.vocmng.biz.rr.CalculationContext;
import com.xinfei.vocmng.biz.rr.dto.*;
import com.xinfei.vocmng.biz.rr.dto.bill.DeductDetailDto;
import com.xinfei.vocmng.biz.rr.dto.bill.LoanPlanDto;
import com.xinfei.vocmng.biz.rr.dto.bill.PlanDetailDto;
import com.xinfei.vocmng.biz.rr.dto.bill.PlanDto;
import com.xinfei.vocmng.biz.rr.request.*;
import com.xinfei.vocmng.biz.rr.response.*;
import com.xinfei.vocmng.biz.service.*;
import com.xinfei.vocmng.biz.strategy.ExemptionApplicationStrategy;
import com.xinfei.vocmng.biz.strategy.ExemptionStrategyFactory;
import com.xinfei.vocmng.biz.strategy.dto.EmptyStrategyInput;
import com.xinfei.vocmng.biz.strategy.dto.OrderReductionStrategyInput;
import com.xinfei.vocmng.biz.strategy.dto.StrategyExecutionResult;
import com.xinfei.vocmng.biz.strategy.dto.ToDeductionStrategyInput;
import com.xinfei.vocmng.biz.util.LocalDateTimeUtils;
import com.xinfei.vocmng.biz.util.RedisUtils;
import com.xinfei.vocmng.biz.util.UserContextHolder;
import com.xinfei.vocmng.dal.dto.resp.ControlData;
import com.xinfei.vocmng.dal.dto.resp.FeeStrategyConfig;
import com.xinfei.vocmng.dal.mapper.FlowLoanMappingMapper;
import com.xinfei.vocmng.dal.mapper.RepaymentPlanDetailMapper;
import com.xinfei.vocmng.dal.mapper.RepaymentPlanMapper;
import com.xinfei.vocmng.dal.po.FlowLoanMapping;
import com.xinfei.vocmng.dal.po.RepaymentPlan;
import com.xinfei.vocmng.dal.po.RepaymentPlanDetail;
import com.xinfei.vocmng.itl.NPayFeignClient;
import com.xinfei.vocmng.itl.client.exeception.DeductPlanChannelEnum;
import com.xinfei.vocmng.itl.client.feign.*;
import com.xinfei.vocmng.itl.client.feign.impl.CstStartService;
import com.xinfei.vocmng.itl.client.feign.impl.LcsFeignService;
import com.xinfei.vocmng.itl.client.feign.impl.ManagementFacadeClient;
import com.xinfei.vocmng.itl.client.feign.impl.SmsFeignService;
import com.xinfei.vocmng.itl.client.feign.service.CisFacadeClientService;
import com.xinfei.vocmng.itl.client.feign.service.LendQueryClientService;
import com.xinfei.vocmng.itl.client.feign.service.RepayFacadeService;
import com.xinfei.vocmng.itl.client.feign.service.dto.RepayCalculateDto;
import com.xinfei.vocmng.itl.model.enums.SettleBaffleScene;
import com.xinfei.vocmng.itl.rr.*;
import com.xinfei.vocmng.itl.util.LogUtil;
import com.xinfei.vocmng.itl.util.SerialNumberGeneratorUtil;
import com.xinfei.xfframework.common.JsonUtil;
import com.xyf.cis.query.facade.dto.standard.response.ThreeElementsDTO;
import com.xyf.cis.query.facade.dto.standard.response.UserSearchDTO;
import com.xyf.user.facade.common.model.PageResult;
import io.kyoto.pillar.lcs.loan.domain.request.LoanPlanRequest;
import io.kyoto.pillar.lcs.loan.domain.response.LoanPlanResponse;
import io.kyoto.pillar.lcs.loan.domain.response.PlanResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.io.IOException;
import java.lang.reflect.Type;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.xinfei.vocmng.biz.constants.ReqNoConstants.getRequestNo;
import static com.xinfei.vocmng.biz.service.CommonService.getPlanStatus;

/**
 * <AUTHOR>
 * @version $ OrderBillServiceImp, v 0.1 2023-12-14 16:43 junjie.yan Exp $
 */
@Service
@Slf4j
public class RepayServiceImp implements RepayService {

    @Value("${validityPeriod}")
    private Integer validityPeriod;

    @Value("${isAbleSettle:false}")
    private Boolean isAbleSettle;

    @Value("${isBatchLoanLink:false}")
    private Boolean isBatchLoanLink;

    @Value("${advSwitch:false}")
    private Boolean advSwitch;

    @Value("${newRepayCancel:true}")
    private Boolean newRepayCancel;

    @Autowired
    protected VocConfig vocConfig;

    private final Gson gson = new Gson();
    private final Type typeListString = new TypeToken<List<String>>() {
    }.getType();

    private final Type typeListMap = new TypeToken<List<Map<String, Object>>>() {
    }.getType();

    private final DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

    @Resource
    private LendQueryFacadeClient lendQueryFacadeClient;

    @Resource
    private ManagementFacadeClient managementFacadeClient;

    @Resource
    private LendQueryClientService lendQueryClientService;

    @Resource
    private RepayFacadeClient repayFacadeClient;

    @Resource
    private RepayFacadeService repayFacadeService;


    @Resource
    private LcsFeignService lcsFeignService;

    @Resource
    private RepaymentPlanDetailMapper repaymentPlanDetailMapper;

    @Resource
    private RepaymentPlanMapper repaymentPlanMapper;

    @Resource
    private ControlItemValueFactory factory;

    @Resource
    private LoanService loanService;

    @Autowired
    private EmployeeService employeeService;

    @Resource
    private LoginUserConfig loginUserConfig;

    @Resource
    private SmsFeignService smsFeignService;

    @Resource
    private CisFacadeClient cisFacadeClient;

    @Resource
    private RedisUtils redisUtils;

    @Resource
    private CisFacadeClientService cisFacadeClientService;

    @Resource
    private CstStartService cstStartService;

    @Resource
    private FlowLoanMappingMapper flowLoanMappingMapper;

    @Resource
    private RepayTradeClient repayTradeClient;

    @Resource
    private CommonService commonService;

    @Resource
    private CustomerServiceImpl customerServiceImpl;

    @Resource
    private OrderStrategyServiceImpl orderStrategyServiceImpl;

    @Resource
    private FromDeductionStrategyServiceImpl fromDeductionStrategyServiceImpl;

    @Resource
    private ToDeductionStrategyServiceImpl toDeductionStrategyServiceImpl;
    @Resource
    private OuterApiFacadeClient outerApiFacadeClient;

    @Resource
    private NPayFeignClient nPayFeignClient;

    @Resource
    private NPayService nPayService;

    @Override
    public RepaymentProcessResp repaymentProcess(RepaymentProcessReq req) {
        RepaymentProcessResp repaymentProcessResp = new RepaymentProcessResp();

        GetBillListRequest getBillListRequest = new GetBillListRequest();
        getBillListRequest.setLoanNos(req.getLoanNos());
        List<LoanPlanDto> loanPlans = loanService.queryBillList(getBillListRequest);

        if (req.getRepayProcessEnums().contains(RepayProcessEnum.INIT)) {
            getRepayInitFees(loanPlans, repaymentProcessResp, req.getLoanOrderNos(),req.getLoanNoTerms());
        }

        if (req.getRepayProcessEnums().contains(RepayProcessEnum.CALCULATE)) {
            getCalculateFees(req, loanPlans, repaymentProcessResp);
        }

        if (req.getRepayProcessEnums().contains(RepayProcessEnum.EXEMPTION)) {
            getExemptionLimit(req, loanPlans, repaymentProcessResp);
        }
        return repaymentProcessResp;
    }

    /**
     * 获取未结清最早一期
     *
     * @param loanPlanDto
     * @return
     */
    private PlanDto getCurTermPlan(LoanPlanDto loanPlanDto) {
        List<PlanDto> planList = loanPlanDto.getPlanList().stream().filter(r -> "0".equals(r.getRpyFlag()) || "1".equals(r.getRpyFlag())).collect(Collectors.toList());
        PlanDto planDto;
        if (CollectionUtils.isEmpty(planList)) {
            throw new TechplayException(TechplayErrDtlEnum.LOAN_FLAG_ERROR);
        } else {
            planDto = planList.stream().sorted(Comparator.comparing(PlanDto::getTerm)).collect(Collectors.toList()).get(0);
        }
        return planDto;
    }

    /**
     * 获取未到期最早一期
     *
     * @param loanPlanDto
     * @return
     */
    private PlanDto getCurTermPlan2(LoanPlanDto loanPlanDto) {
        List<PlanDto> planList = loanPlanDto.getPlanList().stream().filter(r -> "0".equals(r.getRpyFlag())).collect(Collectors.toList());
        PlanDto planDto;
        if (CollectionUtils.isEmpty(planList)) {
            return null;
        } else {
            planDto = planList.stream().sorted(Comparator.comparing(PlanDto::getTerm)).collect(Collectors.toList()).get(0);
        }
        return planDto;
    }

    /**
     * 获取期数
     *
     * @param loanPlanDto
     * @param termNum
     * @return
     */
    private List<PlanDto> getPlanDtos(LoanPlanDto loanPlanDto, Integer termNum) {
        List<PlanDto> planList = loanPlanDto.getPlanList().stream().filter(r -> "0".equals(r.getRpyFlag()) || "1".equals(r.getRpyFlag())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(planList) || planList.size() < termNum) {
            throw new IgnoreException(TechplayErrDtlEnum.REPAY_PLAN_ERROR, "还多期期数应小于等于未还总期数");
        }
        planList = planList.stream().sorted(Comparator.comparing(PlanDto::getTerm)).collect(Collectors.toList());
        return planList.stream().limit(termNum).collect(Collectors.toList());
    }

    private void getRepayInitFees(List<LoanPlanDto> loanPlanDtos, RepaymentProcessResp repaymentProcessResp, Map<String, String> loanOrderNos ,Map<String, Integer> loanNoTerms) {
        List<RepayInitFee> repayInitFees = new ArrayList<>();
        List<String> sourcList = gson.fromJson(vocConfig.getBuyBackFundSource(), typeListString);
        for (LoanPlanDto loanPlanDto : loanPlanDtos) {
            RepayInitFee repayInitFee = new RepayInitFee();
            try {
                PlanDto planDto = getCurTermPlan(loanPlanDto);
                repayInitFee.setDateDue(planDto.getDateDue());
            } catch (TechplayException e) {
                log.warn("该订单下账单都已结清");
            }
            repayInitFee.setLoanNo(loanPlanDto.getLoanNo());
            if (loanPlanDto.getLoanFeeDetail() != null && loanPlanDto.getLoanFeeDetail().getInitPlan() != null) {
                PlanDetailDto planDetailInit = loanPlanDto.getLoanFeeDetail().getInitPlan();
                repayInitFee.setPrinAmt(planDetailInit.getPrinAmt());
                repayInitFee.setIntAmt(planDetailInit.getIntAmt());
                repayInitFee.setGuaranteeFee(planDetailInit.getGuaranteeFee());
                repayInitFee.setFee1Amt(planDetailInit.getFee1Amt());
                repayInitFee.setFee2Amt(planDetailInit.getFee2Amt());
                repayInitFee.setLateFee(planDetailInit.getLateFee());
                repayInitFee.setOintAmt(planDetailInit.getOintAmt());
                repayInitFee.setFee3Amt(planDetailInit.getFee3Amt());
                repayInitFee.setFee6Amt(planDetailInit.getFee6Amt());
            }
            if (loanPlanDto.getRpyFlagTerm() != null) {
                repayInitFee.setActTerms(loanPlanDto.getRpyFlagTerm().get("2") == null ? 0 : loanPlanDto.getRpyFlagTerm().get("2").size());
            }
            if (loanPlanDto.getRpyFlagTerm() != null) {
                repayInitFee.setOverDueTerms(loanPlanDto.getRpyFlagTerm().get("1") == null ? 0 : loanPlanDto.getRpyFlagTerm().get("1").size());
            }

            List<PlanDto> planDtos = loanPlanDto.getPlanList().stream().filter(r -> !LocalDate.now().isBefore(r.getDateStart())).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(planDtos)) {
                repayInitFee.setMaxAbleTerms(planDtos.size() - repayInitFee.getActTerms());
            } else {
                repayInitFee.setMaxAbleTerms(0);
            }
            repayInitFee.setStatus(loanPlanDto.getStatus());
            repayInitFee.setTotalTerms(loanPlanDto.getTotalTerms());

            //根据借据号查订单，获取订单创建时间
            ManageOrderDetailRequest request = new ManageOrderDetailRequest();
            request.setOrderNo(loanOrderNos.get(loanPlanDto.getLoanNo()));
            ManageOrderDetailDTO manageOrderDetailDTO = lendQueryFacadeClient.getOrderDetail(request);
            if (manageOrderDetailDTO == null) {
                throw new IgnoreException(TechplayErrDtlEnum.CLIENT_CODE_ERROR, "订单号无法查询到有效订单");
            }
            loanPlanDto.setOrderType(manageOrderDetailDTO.getOrderType());
            repayInitFee.setOrderNo(manageOrderDetailDTO.getLoanReqNo());
            repayInitFee.setOrderType(manageOrderDetailDTO.getOrderType());
            repayInitFee.setCreateTime(LocalDateTimeUtils.parseLocalDateTimeByDate(manageOrderDetailDTO.getDateCreated()));
            repayInitFee.setFundSource(manageOrderDetailDTO.getFundSource());

            //判断渠道是否是JD渠道
            if ("xyf01".equals(manageOrderDetailDTO.getApp()) && "xyf01_jdjq".equals(manageOrderDetailDTO.getInnerApp()) && "XYF01-API-JINGDONGJIEQIAN".equals(manageOrderDetailDTO.getUtmSource())) {
                repayInitFee.setIsJDWithhold(true);
                //判断最后一期是否逾期超过90天
                Optional<PlanDto> lastTermPlanOpt = loanPlanDto.getPlanList().stream()
                        .max(Comparator.comparingInt(PlanDto::getTerm));
                if (lastTermPlanOpt.isPresent()) {
                    PlanDto lastTermPlan = lastTermPlanOpt.get();
                    if ("1".equals(lastTermPlan.getRpyFlag()) && lastTermPlan.getOverdueDays() > 90) {
                        repayInitFee.setIsJDWithhold(false);
                    }
                }
            }

            //判断反诈渠道限制
            repayInitFee.setIsAntiFraud(false);
            LocalDateTime dateCashTime = LocalDateTimeUtils.toLocalDateTime(manageOrderDetailDTO.getDateCash());
            LocalDateTime dateToCompare = dateCashTime.plusDays(vocConfig.getAntiFraudDay()).withHour(23).withMinute(59).withSecond(59).withNano(0);
            LocalDateTime now = LocalDateTime.now();
            boolean isInRange = now.isAfter(dateCashTime) && now.isBefore(dateToCompare);
            List<String> whiteList = gson.fromJson(vocConfig.getAntiFraudApp(), typeListString);
            if (whiteList.contains(manageOrderDetailDTO.getInnerApp()) && isInRange) {
                repayInitFee.setIsAntiFraud(true);
            }

            //资方回购限制
            repayInitFee.setIsBuyBack(false);
            if(sourcList.contains(manageOrderDetailDTO.getFundSource())){
                repayInitFee.setIsBuyBack(loanPlanDto.getPlanList().stream()
                        .filter(plan -> !"2".equals(plan.getRpyFlag())) // 剔除 rpyFlag="2" 已结清
                        .anyMatch(plan -> "0".equals(plan.getCompensatedType()))); // 判断是否存在 compensatedType="0" 未回购
            }

            RepaymentPlan effectPlan = repaymentPlanDetailMapper.queryEffectPlan(loanPlanDto.getLoanNo(), null);
            repayInitFee.setIsEffectPlan(effectPlan != null);
            if (effectPlan == null) {
                repayInitFee.setIsEffectPlan(commonService.hasEffectivePlan(loanPlanDto.getLoanNo()));
            }
            repayInitFees.add(repayInitFee);
        }

        if(CollectionUtils.isNotEmpty(repayInitFees)){
            // 检查是否同时存在 fundSource 在 whiteList 中和不在 whiteList 中的对象
            boolean hasInWhiteList = repayInitFees.stream()
                    .anyMatch(fee -> sourcList.contains(fee.getFundSource()));
            boolean hasNotInWhiteList = repayInitFees.stream()
                    .anyMatch(fee -> !sourcList.contains(fee.getFundSource()));
            if (hasInWhiteList && hasNotInWhiteList) {
                // 将 fundSource 在 whiteList 中的对象设置 toast
                repayInitFees = repayInitFees.stream()
                        .filter(fee -> !sourcList.contains(fee.getFundSource())).collect(Collectors.toList());
                repaymentProcessResp.setToast("存在不支持合并还款的订单，已剔除，需单独设置方案");
            }
            if(hasInWhiteList){
                if(loanNoTerms != null && !loanNoTerms.isEmpty()){
                    for (Map.Entry<String, Integer> entry : loanNoTerms.entrySet()) {
                        Integer term = entry.getValue();
                        // 检查 term 是否为 null
                        if (term == null) {
                            continue; // 跳过 null 值
                        }
                        // 检查 term > 2
                        if (term > 1) {
                            throw new IgnoreException(TechplayErrDtlEnum.REPAY_PLAN_ERROR, "存在不支持多期还款的订单，需单期还款。");
                        }
                    }
                }
            }
        }

        repaymentProcessResp.setRepayInitFees(repayInitFees);
    }

    public void getCalculateFees(RepaymentProcessReq req, List<LoanPlanDto> loanPlanDtos, RepaymentProcessResp repaymentProcessResp) {
        if (CollectionUtils.isEmpty(repaymentProcessResp.getRepayInitFees())) {
            throw new IgnoreException(TechplayErrDtlEnum.FEE_SHOW_ERROR, "无有效初始费项借据");
        }

        //只试算非结清且无生效中方案的订单
        List<RepayInitFee> repayInitFees = repaymentProcessResp.getRepayInitFees().stream()
                .filter(r -> !"FP".equals(r.getStatus()) && !r.getIsEffectPlan())
                .collect(Collectors.toList());

        if (CollectionUtils.isEmpty(repayInitFees)) {
            return;
        }

        List<CalculateFee> calculateFees = new ArrayList<>();
        for (String loanNo : repayInitFees.stream().map(RepayInitFee::getLoanNo).collect(Collectors.toList())) {
            CalculateFee calculateFee = new CalculateFee();
            calculateFee.setLoanNo(loanNo);
            //结清费字段设置初始值
            calculateFee.setRedTransFee4(BigDecimal.ZERO);
            calculateFee.setAdvanceSettlementFee(BigDecimal.ZERO);
            LoanPlanDto loanPlanDto = loanPlanDtos.stream().filter(r -> loanNo.equals(r.getLoanNo())).collect(Collectors.toList()).get(0);
            RepayLoanCalcResponse response;

            //设置失效时间
            PlanDto planDtoEndTime = getCurTermPlan2(loanPlanDto);
            LocalDate endTime;
            if (planDtoEndTime == null || planDtoEndTime.getDateDue() == null) {
                endTime = LocalDate.now().plusDays(validityPeriod - 1);
            } else {
                LocalDate dateDue = planDtoEndTime.getDateDue();
                endTime = dateDue.isAfter(LocalDate.now().plusDays(validityPeriod - 1)) ? LocalDate.now().plusDays(validityPeriod - 1) : dateDue;
            }
            LocalDateTime end = LocalDateTime.of(endTime, LocalTime.MAX).withNano(0);
            calculateFee.setEndTime(end);

            if (req.getRepayType() == 1) {
                //还多期
                Integer termNum = req.getLoanNoTerms().get(loanNo);
                List<PlanDto> planDtos = getPlanDtos(loanPlanDto, termNum);
                List<String> terms = planDtos.stream().map(PlanDto::getTerm).collect(Collectors.toList()).stream().map(Object::toString).collect(Collectors.toList());
                if (!isAbleSettle && terms.size() > 1 && terms.contains(loanPlanDto.getPlanList().stream().max(Comparator.comparing(PlanDto::getTerm)).get().getTerm().toString())) {
                    throw new IgnoreException(TechplayErrDtlEnum.FEE_SHOW_ERROR, "还多期存在订单已选择所有未还账单，请选择提前结清");
                }
                response = commonService.getRepayCalculate(req.getRepayType(), terms, calculateFee.getLoanNo(), req.getLoanNoDeductions().get(loanNo));
                if (!response.getCanOfflineRepay()) {
                    throw new IgnoreException(TechplayErrDtlEnum.FEE_SHOW_ERROR, "所选账单存在未到起息日账单，请选择提前结清");
                }
                if (response.getRepayCheck() != null) {
                    calculateFee.setIsAdvanceCurr(response.getRepayCheck().getCanRepay());
                }
                calculateFee.setTerms(terms);

                List<PlanCalculateFee> planCalculateFees = new ArrayList<>();
                for (PlanDto planDto : planDtos) {
                    PlanCalculateFee planCalculateFee = new PlanCalculateFee();
                    planCalculateFee.setBillNo(planDto.getPlanNo());
                    planCalculateFee.setRpyFlag(planDto.getRpyFlag());
                    planCalculateFee.setTerm(planDto.getTerm());
                    planCalculateFee.setDateDue(planDto.getDateDue());

                    if (planDto.getPlanFeeDetail() != null && planDto.getPlanFeeDetail().getActShouldRepay() != null) {
                        planCalculateFee.setPlanPrinAmt(planDto.getPlanFeeDetail().getActShouldRepay().getPrinAmt());
                        planCalculateFee.setPlanIntAmt(planDto.getPlanFeeDetail().getActShouldRepay().getIntAmt());
                        planCalculateFee.setPlanGuaranteeFee(planDto.getPlanFeeDetail().getActShouldRepay().getGuaranteeFee());
                        planCalculateFee.setPlanLateFee(planDto.getPlanFeeDetail().getActShouldRepay().getLateFee());
                        planCalculateFee.setPlanTotalAmt(planDto.getPlanFeeDetail().getActShouldRepay().getSumAmt());
                    }

                    planCalculateFees.add(planCalculateFee);
                }
                calculateFee.setPlanCalculateFees(planCalculateFees);
            } else {
                //结清
                calculateFee.setLoanStatus(loanPlanDto.getStatus());
                response = commonService.getRepayCalculate(req.getRepayType(), null, calculateFee.getLoanNo(), req.getLoanNoDeductions().get(loanNo));
                calculateFee.setRealAdvSettFee(BigDecimal.ZERO);
                if (response.getRealAmtDetail() != null && CollectionUtils.isNotEmpty(response.getPlanList()) && !advSwitch) {
                    BigDecimal fee4 = response.getPlanList().stream()
                            .map(RepayLoanCalcResponse.CalcPlan::getTransDetail)
                            .collect(Collectors.toList()).stream()
                            .map(FeeAmountDto::getTransFee4)
                            .reduce(BigDecimal.ZERO, BigDecimal::add);
                    calculateFee.setAdvanceSettlementFee(fee4);
                    calculateFee.setRealAdvSettFee(response.getRealAmtDetail().getTransFee4());
                    //结清费，强制 - 红线减免
                    if (SettleBaffleScene.TRIAL_PROCESS.getCode().equals(response.getSettleBaffleScene()) && response.getRedDeductAmt() != null && calculateFee.getAdvanceSettlementFee() != null) {
                        calculateFee.setAdvanceSettlementFee(calculateFee.getAdvanceSettlementFee().subtract(calculateSum(response, FeeAmountDto::getTransFee4)));
                        calculateFee.setRedTransFee4(calculateSum(response, FeeAmountDto::getTransFee4));
                    }
                }

                if (response.getRealAmtDetail() != null && CollectionUtils.isNotEmpty(response.getPlanList()) && advSwitch) {
                    calculateFee.setAdvanceSettlementFee(response.getRealAmtDetail().getTransFee4());
                    calculateFee.setRealAdvSettFee(response.getRealAmtDetail().getTransFee4());
                    //结清费，非强制 + 红线减免
                    if (!SettleBaffleScene.TRIAL_PROCESS.getCode().equals(response.getSettleBaffleScene()) && response.getRedDeductAmt() != null && calculateFee.getAdvanceSettlementFee() != null) {
                        calculateFee.setAdvanceSettlementFee(calculateFee.getAdvanceSettlementFee().add(calculateSum(response, FeeAmountDto::getTransFee4)));
                    } else {
                        calculateFee.setRedTransFee4(calculateSum(response, FeeAmountDto::getTransFee4));
                    }
                }

            }

            calculateFee.setSettleBaffleScene(response.getSettleBaffleScene());
            calculateFee.setRedDeductAmt(response.getRedDeductAmt());
            if (response.getRealAmtDetail() != null) {
                if (req.getRepayType() == 1) {
                    calculateFee.setCalTotalAmt(response.getRealAmt());
                } else {
                    if (SettleBaffleScene.TRIAL_PROCESS.getCode().equals(response.getSettleBaffleScene())) {
                        calculateFee.setCalTotalAmt(response.getTotalAmt().subtract(response.getDeductAmt()).subtract(response.getUnprofitDeductAmt()).subtract(response.getRedDeductAmt()));
                    } else {
                        calculateFee.setCalTotalAmt(response.getTotalAmt().subtract(response.getDeductAmt()).subtract(response.getUnprofitDeductAmt()));
                    }
                }

                //可抵扣范围
                deductionAmt(calculateFee.getCalTotalAmt(), calculateFee, loanPlanDto);

                calculateFee.setCalPrinAmt(response.getRealAmtDetail().getTransPrin());
                calculateFee.setCalIntAmt(response.getRealAmtDetail().getTransInt());
                calculateFee.setCalGuaranteeFee(response.getRealAmtDetail().getTransFee1().add(response.getRealAmtDetail().getTransFee2()));
                calculateFee.setTransFee1(response.getRealAmtDetail().getTransFee1());
                calculateFee.setTransFee2(response.getRealAmtDetail().getTransFee2());
                calculateFee.setCalLateFee(response.getRealAmtDetail().getTransOint().add(response.getRealAmtDetail().getTransFee3()).add(response.getRealAmtDetail().getTransFee6()));
                calculateFee.setTransOint(response.getRealAmtDetail().getTransOint());
                calculateFee.setTransFee3(response.getRealAmtDetail().getTransFee3());
                calculateFee.setTransFee6(response.getRealAmtDetail().getTransFee6());

                if (!SettleBaffleScene.TRIAL_PROCESS.getCode().equals(response.getSettleBaffleScene())) {
                    //非强制：应还金额 + 红线减免金额
                    calculateFee.setCalPrinAmt(calculateFee.getCalPrinAmt().add(calculateSum(response, FeeAmountDto::getTransPrin)));
                    calculateFee.setCalIntAmt(calculateFee.getCalIntAmt().add(calculateSum(response, FeeAmountDto::getTransInt)));
                    calculateFee.setCalGuaranteeFee(calculateFee.getCalGuaranteeFee().add(calculateSum(response, FeeAmountDto::getTransFee1)).add(calculateSum(response, FeeAmountDto::getTransFee2)));
                    calculateFee.setTransFee1(calculateFee.getTransFee1().add(calculateSum(response, FeeAmountDto::getTransFee1)));
                    calculateFee.setTransFee2(calculateFee.getTransFee2().add(calculateSum(response, FeeAmountDto::getTransFee2)));
                    calculateFee.setCalLateFee(calculateFee.getCalLateFee().add(calculateSum(response, FeeAmountDto::getTransOint)).add(calculateSum(response, FeeAmountDto::getTransFee3)).add(calculateSum(response, FeeAmountDto::getTransFee6)));
                    calculateFee.setTransOint(calculateFee.getTransOint().add(calculateSum(response, FeeAmountDto::getTransOint)));
                    calculateFee.setTransFee3(calculateFee.getTransFee3().add(calculateSum(response, FeeAmountDto::getTransFee3)));
                    calculateFee.setTransFee6(calculateFee.getTransFee6().add(calculateSum(response, FeeAmountDto::getTransFee6)));
                } else {
                    //强制，计算红线减免金额
                    calculateFee.setRedCalPrinAmt(calculateSum(response, FeeAmountDto::getTransPrin));
                    calculateFee.setRedCalIntAmt(calculateSum(response, FeeAmountDto::getTransInt));
                    calculateFee.setRedCalGuaranteeFee(calculateSum(response, FeeAmountDto::getTransFee1).add(calculateSum(response, FeeAmountDto::getTransFee2)));
                    calculateFee.setRedTransFee1(calculateSum(response, FeeAmountDto::getTransFee1));
                    calculateFee.setRedTransFee2(calculateSum(response, FeeAmountDto::getTransFee2));
                    calculateFee.setRedCalLateFee(calculateSum(response, FeeAmountDto::getTransOint).add(calculateSum(response, FeeAmountDto::getTransFee3)).add(calculateSum(response, FeeAmountDto::getTransFee6)));
                    calculateFee.setRedTransOint(calculateSum(response, FeeAmountDto::getTransOint));
                    calculateFee.setRedTransFee3(calculateSum(response, FeeAmountDto::getTransFee3));
                    calculateFee.setRedTransFee6(calculateSum(response, FeeAmountDto::getTransFee6));
                }

                if (vocConfig.getIsNewFeeControl()) {
                    List<String> whiteList = gson.fromJson(vocConfig.getUserIdentifyWhiteList(), typeListString);
                    if (!whiteList.contains(UserContextHolder.getUserContext().getUserIdentify())) {
                        deductionAmtNew(calculateFee, loanPlanDto, response, req);
                    }
                }
            }
            FeeAmountDtoResp canDeductAmtDetail = RepayConverter.INSTANCE.feeAmountDtoToFeeAmountDtoResp(response.getCanDeductAmtDetail());
            calculateFee.setCanDeductAmtDetail(canDeductAmtDetail);
            calculateFees.add(calculateFee);
        }
        repaymentProcessResp.setCalculateFees(calculateFees);
    }

    public static BigDecimal calculateSum(RepayLoanCalcResponse response, Function<FeeAmountDto, BigDecimal> mapper) {
        return response.getPlanList()
                .stream()
                .map(RepayLoanCalcResponse.CalcPlan::getRedDeductDetail)
                .filter(Objects::nonNull)
                .map(mapper)
                .filter(Objects::nonNull)
                .reduce(BigDecimal::add)
                .orElse(BigDecimal.ZERO);
    }

    private void getExemptionLimit(RepaymentProcessReq req, List<LoanPlanDto> loanPlanDtos, RepaymentProcessResp repaymentProcessResp) {
        List<RepayInitFee> repayInitFees = getRepayInitFees(repaymentProcessResp);

        //过滤出减免及对应还款类型的费控配置信息
        List<ControlData> controlDataList = UserContextHolder.getUserContext().getControlDataList().stream()
                .filter(r -> r.getControlScene() == 1 && r.getControlType().equals(req.getRepayType()))
                .collect(Collectors.toList());

        List<ExemptionResponse> exemptionList = new ArrayList<>();

        for (RepayInitFee repayInitFee : repayInitFees) {
            ExemptionResponse exemption = new ExemptionResponse();
            exemption.setLoanNo(repayInitFee.getLoanNo());

            LoanPlanDto loanPlanDto = loanPlanDtos.stream()
                    .filter(r -> r.getLoanNo().equals(repayInitFee.getLoanNo()))
                    .findFirst()
                    .orElseThrow(() -> new IgnoreException(TechplayErrDtlEnum.REPAY_ERROR, "未找到对应的还款计划"));

            PlanDetailDto initPlan = new PlanDetailDto();
            PlanDetailDto deductPlan = new PlanDetailDto();
            BigDecimal totalRedDeduct = BigDecimal.ZERO;

            if (req.getRepayType() == 1) {
                List<PlanDto> planDtos = getPlanDtos(loanPlanDto, req.getLoanNoTerms().get(repayInitFee.getLoanNo()));
                exemption.setTerms(planDtos.stream().map(PlanDto::getTerm).collect(Collectors.toList()));
                String billNos = planDtos.stream().map(PlanDto::getPlanNo).collect(Collectors.toList()).stream().map(Objects::toString).collect(Collectors.joining("/"));
                exemption.setBillNos(billNos);
                updatePlans(initPlan, deductPlan, planDtos);
            } else {
                initPlan = loanPlanDto.getLoanFeeDetail().getInitPlan();
                deductPlan = loanPlanDto.getLoanFeeDetail().getPlanDeduct().getActDeductPlan();
                //历史红线减免
                totalRedDeduct = loanPlanDto.getPlanList().stream()
                        .filter(Objects::nonNull)
                        .flatMap(planDto -> Optional.ofNullable(planDto.getPlanFeeDetail()).map(feeDetail -> feeDetail.getPlanDeduct().getDeductPlanList()).orElse(Collections.emptyList()).stream())
                        .filter(r -> "RP_RED_DEDUCT".equals(r.getTranType()))
                        .map(DeductDetailDto::getSumAmt)
                        .reduce(BigDecimal.ZERO, BigDecimal::add);
            }

            CalculateFee calculateFee = repaymentProcessResp.getCalculateFees().stream().filter(r -> repayInitFee.getLoanNo().equals(r.getLoanNo())).findFirst().orElse(null);
            if (calculateFee == null) {
                throw new IgnoreException(TechplayErrDtlEnum.REPAY_ERROR, "未找到对应的减免试算信息");
            }
            if (SettleBaffleScene.TRIAL_PROCESS.getCode().equals(calculateFee.getSettleBaffleScene())) {
                //强制减免，历史减免加上红线减免
                addReduction(deductPlan, calculateFee);
            }

            Map<String, String> result = null;
            FeeStrategyConfig config = null;

            if (vocConfig.getIsNewFeeControl()) {
                List<String> whiteList = gson.fromJson(vocConfig.getUserIdentifyWhiteList(), typeListString);
                if (!whiteList.contains(UserContextHolder.getUserContext().getUserIdentify())) {
                    OrderReductionStrategyInput input = OrderReductionStrategyInput
                            .builder()
                            .loanNo(loanPlanDto.getLoanNo())
                            .repaymentProcessReq(req)
                            .build();
                    StrategyExecutionResult<FeeStrategyConfig> strategyExecutionResult = orderStrategyServiceImpl.executeStrategy(FeeStrategyEnum.ORDER_REDUCTIONS, input);
                    result = strategyExecutionResult.getResult();
                    config = strategyExecutionResult.getConfig();
                }
            }

            if (OrderType.PROFIT.getCode().equals(loanPlanDto.getOrderType())) {
                controlDataList = controlDataList.stream().filter(r -> r.getControlChildType() == 8).collect(Collectors.toList());
            } else {
                controlDataList = controlDataList.stream().filter(r -> r.getControlChildType() != 8).collect(Collectors.toList());
            }

            initPlan.setFundSource(repayInitFee.getFundSource());
            initPlan.setFundSourceWhiteList(gson.fromJson(vocConfig.getFundSourceWhiteList(), typeListString));

            if (result == null) {
                throw new IgnoreException(TechplayErrDtlEnum.REPAY_ERROR, "还款减免新费控策略返回结果为空");
            }

            if (!vocConfig.getIsDeductFeeControl()) {
                processControlDataList(controlDataList, result, initPlan, deductPlan, calculateFee, totalRedDeduct, exemption, config);
            } else {
                calculateExemption(result, exemption, initPlan, deductPlan, calculateFee, totalRedDeduct, config);
            }

            exemptionList.add(exemption);
        }

        repaymentProcessResp.setExemptions(exemptionList);
    }

    private BigDecimal safeGet(BigDecimal value) {
        return Optional.ofNullable(value).orElse(BigDecimal.ZERO);
    }

    public void calculateExemption(Map<String, String> result, ExemptionResponse exemption,
                                   PlanDetailDto initPlan, PlanDetailDto deductPlan, CalculateFee calculateFee, BigDecimal totalRedDeduct, FeeStrategyConfig config) {
        for (Map.Entry<String, String> entry : result.entrySet()) {
            String key = entry.getKey();
            BigDecimal value;
            try {
                value = new BigDecimal(entry.getValue());
            } catch (NumberFormatException e) {
                log.warn(LogUtil.infoLog("Error parsing BigDecimal for key " + key + ", value: " + entry.getValue()));
                continue; // 跳过这个条目
            }

            // 1. 从工厂获取策略
            Optional<ExemptionApplicationStrategy> strategyOpt = ExemptionStrategyFactory.getStrategy(key);

            if (strategyOpt.isPresent()) {
                ExemptionApplicationStrategy strategy = strategyOpt.get();

                // 2. 创建上下文
                CalculationContext context = new CalculationContext(value, initPlan, deductPlan, calculateFee, totalRedDeduct, config);

                // 3. 执行策略，策略内部会完成计算和对 exemption 的设置
                strategy.apply(context, exemption); // 将 exemption 对象传递给策略
                log.info(LogUtil.newInfoLog("策略明细计算", "loanNo", calculateFee.getLoanNo(), JsonUtil.toJson(exemption)));

            }
        }

        exemption.setGuaranteeLower(BigDecimal.ZERO);
        exemption.setGuaranteeUpper(sumSafely(exemption.getFee1Upper(), exemption.getFee2Upper()));
        exemption.setGuaranteeMax(sumSafely(exemption.getFee1Max(), exemption.getFee2Max()));

        exemption.setLateLower(BigDecimal.ZERO);
        exemption.setLateUpper(sumSafely(exemption.getOintUpper(), exemption.getFee3Upper(), exemption.getFee6Upper()));
        exemption.setLateMax(sumSafely(exemption.getOintMax(), exemption.getFee3Max(), exemption.getFee6Max()));

        if (StringUtils.isNotBlank(result.get("Guarantee_Fee_Cap"))) {
            BigDecimal guaranteeFeeCap = new BigDecimal(result.get("Guarantee_Fee_Cap"));
            if (exemption.getGuaranteeUpper().compareTo(guaranteeFeeCap) > 0) {
                exemption.setGuaranteeUpper(guaranteeFeeCap);
            }
            if (exemption.getGuaranteeMax().compareTo(guaranteeFeeCap) > 0) {
                exemption.setGuaranteeMax(guaranteeFeeCap);
            }
        }

        log.info(LogUtil.newInfoLog("策略明细计算加总", "loanNo", calculateFee.getLoanNo(), JsonUtil.toJson(exemption)));
    }

    // 新增辅助方法：安全地累加多个 BigDecimal 值
    private BigDecimal sumSafely(BigDecimal... values) {
        BigDecimal sum = BigDecimal.ZERO;
        for (BigDecimal val : values) {
            sum = sum.add(safeGet(val));
        }
        return sum;
    }

    @Override
    public List<Map<String, Object>> getChannels() {
        if (!vocConfig.getIsNewFeeControl()) {
            return null;
        } else {
            List<String> whiteList = gson.fromJson(vocConfig.getUserIdentifyWhiteList(), typeListString);
            if (whiteList.contains(UserContextHolder.getUserContext().getUserIdentify())) {
                return null;
            }
        }

        List<FeeStrategyConfig> feeStrategyConfigs = UserContextHolder.getUserContext().getFeeStrategyConfigs();
        if (CollectionUtils.isEmpty(feeStrategyConfigs)) {
            return null;
        }

        FeeStrategyConfig config = feeStrategyConfigs.stream()
                .filter(r -> FeeStrategyEnum.ORDER_REDUCTIONS.getComment().equals(r.getSceneName()))
                .findFirst()
                .orElse(null);

        if (config == null || config.getNeedComplaintChannel() != 1) {
            return null;
        }

        return gson.fromJson(vocConfig.getComplaintChannels(), typeListMap);
    }


    @NotNull
    private static List<RepayInitFee> getRepayInitFees(RepaymentProcessResp repaymentProcessResp) {
        if (CollectionUtils.isEmpty(repaymentProcessResp.getRepayInitFees())) {
            throw new IgnoreException(TechplayErrDtlEnum.FEE_SHOW_ERROR, "无有效初始费项借据");
        }

        List<RepayInitFee> repayInitFees = repaymentProcessResp.getRepayInitFees().stream()
                .filter(r -> !"FP".equals(r.getStatus()) && !r.getIsEffectPlan())
                .collect(Collectors.toList());

        if (CollectionUtils.isEmpty(repayInitFees)) {
            throw new IgnoreException(TechplayErrDtlEnum.FEE_SHOW_ERROR, "借据都已结清或者存在生效中方案");
        }
        return repayInitFees;
    }

    private void processControlDataList(List<ControlData> controlDataList, Map<String, String> feeStrategyResult, PlanDetailDto initPlan, PlanDetailDto deductPlan, CalculateFee calculateFee, BigDecimal totalRedDeduct, ExemptionResponse exemption, FeeStrategyConfig config) {
        if (CollectionUtils.isNotEmpty(controlDataList)) {
            for (ControlData controlData : controlDataList) {
                try {
                    ControlEnum type = ControlEnum.getByControlChildType(controlData.getControlChildType());

                    //获取策略引擎返回百分比
                    String feeValue = getFeeValue(feeStrategyResult, controlData);

                    //赋值百分比
                    ControlItemValue<?, ?, ?> values = getControlItemValue(feeStrategyResult, controlData, feeValue, type);

                    //计算减免金额
                    ControlRes<?, ?> calResult = calculateResult(controlData, type, values, initPlan, deductPlan, calculateFee, totalRedDeduct);

                    //赋值减免范围
                    setExemptionValues(exemption, controlData.getControlChildType(), calResult, feeStrategyResult);

                    //计算最大可减免金额
                    if (config != null && config.getCanBreakOut() == 1) {
                        ControlItemValue<BigDecimal, BigDecimal, BigDecimal> maxValues = new ControlItemValue<>();
                        maxValues.setO(new BigDecimal("100"));
                        maxValues.setT(new BigDecimal("0"));
                        maxValues.setH(new BigDecimal("2000000"));
                        ControlRes<?, ?> maxResult = calculateResult(controlData, type, maxValues, initPlan, deductPlan, calculateFee, totalRedDeduct);
                        log.info(LogUtil.infoLog("maxResult", maxResult));
                        setMaxExemptionValues(exemption, controlData.getControlChildType(), maxResult);
                    }
                } catch (Exception e) {
                    // 处理异常
                    log.error(LogUtil.clientErrorLog("RepayServiceImp", "calculateAmount", null, null, "减免计算错误"), e);
                    throw new TechplayException(TechplayErrDtlEnum.EXEMPTION_ERROR);
                }
            }
        }
    }

    private ControlItemValue<?, ?, ?> getControlItemValue(Map<String, String> feeStrategyResult, ControlData controlData, String feeValue, ControlEnum type) throws IOException {
        ControlItemValue<?, ?, ?> values;
        if (feeStrategyResult == null || StringUtils.isEmpty(feeValue)) {
            values = factory.create(controlData.getControlValue(), type);
        } else {
            ControlItemValue<BigDecimal, BigDecimal, BigDecimal> newValues = new ControlItemValue<>();
            newValues.setO(new BigDecimal(feeValue));
            newValues.setT(new BigDecimal("0"));
            if (StringUtils.isBlank(feeStrategyResult.get("Guarantee_Fee_Cap"))) {
                newValues.setH(new BigDecimal("2000"));
            } else {
                newValues.setH(new BigDecimal(feeStrategyResult.get("Guarantee_Fee_Cap")));
            }
            values = newValues;
        }
        return values;
    }

    private static String getFeeValue(Map<String, String> feeStrategyResult, ControlData controlData) {
        String feeValue = "";
        if (feeStrategyResult != null) {
            switch (ControlEnum.getCommentByControlChildType(controlData.getControlChildType())) {
                case "担保费减免额度":
                    feeValue = feeStrategyResult.get("Out_Fee1Amt_Fee2Amt");
                    break;
                case "逾期费减免额度":
                    feeValue = feeStrategyResult.get("Overdue_Fee");
                    break;
                case "本金额度":
                    feeValue = feeStrategyResult.get("Out_Principal");
                    break;
                case "利息额度":
                    feeValue = feeStrategyResult.get("Out_intAmt");
                    break;
                case "提前结清费":
                    feeValue = feeStrategyResult.get("Out_Settle_Fee");
                default:
                    break;
            }
        }
        return feeValue;
    }

    private ControlRes<?, ?> calculateResult(ControlData controlData, ControlEnum type, ControlItemValue<?, ?, ?> values, PlanDetailDto initPlan, PlanDetailDto deductPlan, CalculateFee calculateFee, BigDecimal totalRedDeduct) {
        if (controlData.getControlChildType() == 5) {
            if (SettleBaffleScene.TRIAL_PROCESS.getCode().equals(calculateFee.getSettleBaffleScene())) {
                totalRedDeduct = totalRedDeduct.add(calculateFee.getRedTransFee4());
                return type.calculateAmount(values, calculateFee.getAdvanceSettlementFee().add(calculateFee.getRedTransFee4()), totalRedDeduct);
            } else {
                return type.calculateAmount(values, calculateFee.getAdvanceSettlementFee(), totalRedDeduct);
            }
        } else if (initPlan != null && deductPlan != null) {
            return type.calculateAmount(values, initPlan, deductPlan, calculateFee);
        } else {
            return null;
        }
    }

    private void setExemptionValues(ExemptionResponse exemption, int controlChildType, ControlRes<?, ?> result, Map<String, String> feeStrategyResult) {
        switch (ControlEnum.getCommentByControlChildType(controlChildType)) {
            case "担保费减免额度":
                exemption.setGuaranteeLower((BigDecimal) result.getLeft());
                exemption.setGuaranteeUpper((BigDecimal) result.getRight());
                break;
            case "逾期费减免额度":
                exemption.setLateLower((BigDecimal) result.getLeft());
                exemption.setLateUpper((BigDecimal) result.getRight());
                break;
            case "本金额度":
                exemption.setPrinLower((BigDecimal) result.getLeft());
                exemption.setPrinUpper((BigDecimal) result.getRight());
                break;
            case "营收订单本金减免额度":
                exemption.setPrinLower((BigDecimal) result.getLeft());
                exemption.setPrinUpper((BigDecimal) result.getRight());
                break;
            case "利息额度":
                exemption.setIntLower((BigDecimal) result.getLeft());
                exemption.setIntUpper((BigDecimal) result.getRight());
                break;
            case "提前结清费":
                exemption.setAdvSettLower((BigDecimal) result.getLeft());
                exemption.setAdvSettUpper((BigDecimal) result.getRight());
                break;
            default:
                break;
        }
    }

    private void setMaxExemptionValues(ExemptionResponse exemption, int controlChildType, ControlRes<?, ?> maxResult) {
        switch (ControlEnum.getCommentByControlChildType(controlChildType)) {
            case "担保费减免额度":
                exemption.setGuaranteeMax((BigDecimal) maxResult.getRight());
                break;
            case "逾期费减免额度":
                exemption.setLateMax((BigDecimal) maxResult.getRight());
                break;
            case "本金额度":
                exemption.setPrinMax((BigDecimal) maxResult.getRight());
                break;
            case "营收订单本金减免额度":
                exemption.setPrinMax((BigDecimal) maxResult.getRight());
                break;
            case "利息额度":
                exemption.setIntMax((BigDecimal) maxResult.getRight());
                break;
            case "提前结清费":
                exemption.setAdvSettMax((BigDecimal) maxResult.getRight());
                break;
            default:
                break;
        }
    }

    private void updatePlans(PlanDetailDto initPlan, PlanDetailDto deductPlan, List<PlanDto> planDtos) {
        for (PlanDto planDto : planDtos) {
            //初始值
            addAmount(initPlan, planDto.getPlanFeeDetail().getInitPlan());
            //历史减免值
            addAmount(deductPlan, planDto.getPlanFeeDetail().getPlanDeduct().getActDeductPlan());
        }
    }

    private void addAmount(PlanDetailDto target, PlanDetailDto source) {
        target.setPrinAmt((target.getPrinAmt() == null ? BigDecimal.ZERO : target.getPrinAmt()).add(source.getPrinAmt()));
        target.setIntAmt((target.getIntAmt() == null ? BigDecimal.ZERO : target.getIntAmt()).add(source.getIntAmt()));
        target.setLateFee((target.getLateFee() == null ? BigDecimal.ZERO : target.getLateFee()).add(source.getLateFee()));
        target.setGuaranteeFee((target.getGuaranteeFee() == null ? BigDecimal.ZERO : target.getGuaranteeFee()).add(source.getGuaranteeFee()));
        target.setFee1Amt((target.getFee1Amt() == null ? BigDecimal.ZERO : target.getFee1Amt()).add(source.getFee1Amt()));
        target.setFee2Amt((target.getFee2Amt() == null ? BigDecimal.ZERO : target.getFee2Amt()).add(source.getFee2Amt()));
        target.setFee3Amt((target.getFee3Amt() == null ? BigDecimal.ZERO : target.getFee3Amt()).add(source.getFee3Amt()));
        target.setFee4Amt((target.getFee4Amt() == null ? BigDecimal.ZERO : target.getFee4Amt()).add(source.getFee4Amt()));
        target.setFee6Amt((target.getFee6Amt() == null ? BigDecimal.ZERO : target.getFee6Amt()).add(source.getFee6Amt()));
        target.setOintAmt((target.getOintAmt() == null ? BigDecimal.ZERO : target.getOintAmt()).add(source.getOintAmt()));
    }

    private void addReduction(PlanDetailDto deductPlan, CalculateFee calculateFee) {
        deductPlan.setPrinAmt(deductPlan.getPrinAmt().add(calculateFee.getRedCalPrinAmt()));
        deductPlan.setLateFee(deductPlan.getLateFee().add(calculateFee.getRedCalLateFee()));
        deductPlan.setIntAmt(deductPlan.getIntAmt().add(calculateFee.getRedCalIntAmt()));
        deductPlan.setGuaranteeFee(deductPlan.getGuaranteeFee().add(calculateFee.getRedCalGuaranteeFee()));
    }

    /**
     * 1. 初次建方案并生成；
     * 2. 初次保存方案；
     * 3. 对保存的方案进行修改；
     * <p>
     * 4. 待审批方案保存；
     * 5. 待审批方案发送；
     * 6. 待审批方案审批通过；
     * 7. 待审批方案审批拒绝。
     *
     * @param request
     * @return
     */
    @Override
    @Transactional
    public RepayPlanRequest saveRepayPlan(RepayPlanRequest request) {

        isAllowSend(request);

        //保存方案
        //若是修改，则逻辑删除该方案明细
        RepaymentPlan repaymentPlan = getRepaymentPlan(request);

        if (request.getIsApproved() != null && !request.getIsApproved()) {
            return request;
        }

        //保存方案明细
        List<RepaymentPlanDetail> repaymentPlanDetails = getRepaymentPlanDetails(request, repaymentPlan);
        redisUtils.set(RedisKeyConstants.REPAY_BANK_LIST + repaymentPlan.getId(), JsonUtil.toJson(request.getBanks()), LocalDateTimeUtils.between(LocalDateTime.now(), request.getEndTime()).getSeconds());
        redisUtils.set(RedisKeyConstants.HAS_DAILY + repaymentPlan.getId(), JsonUtil.toJson(request.getHasDailyType()), LocalDateTimeUtils.between(LocalDateTime.now(), request.getEndTime()).getSeconds());

        if (isSend(request)) {

            //生成发送方案明细
            List<PlanDetailInfo> planDetailInfos = getPlanDetailInfos(repaymentPlanDetails, repaymentPlan);
            //发送方案明细，支持APP自助还款
            List<FeeSubjectEnum> reductionFeeList = new ArrayList<>();
            Integer month = getDeductionLoansFee(request.getPlanType(), reductionFeeList);
            Date time = null;
            if (month == 0) {
                reductionFeeList = null;
            } else {
                LocalDateTime date = LocalDateTime.now().plusMonths(-month);
                time = LocalDateTimeUtils.parseDateByLocalDateTime(date);
            }

            if (vocConfig.getIsNewFeeControl()) {
                List<String> whiteList = gson.fromJson(vocConfig.getUserIdentifyWhiteList(), typeListString);
                if (!whiteList.contains(UserContextHolder.getUserContext().getUserIdentify())) {
                    StrategyExecutionResult<FeeStrategyConfig> strategyExecutionResult = fromDeductionStrategyServiceImpl.executeStrategy(FeeStrategyEnum.DEDUCTIONS_FROM, EmptyStrategyInput.builder().build());
                    Map<String, String> result = strategyExecutionResult.getResult();
                    month = Integer.parseInt(result.get("Out_FromDeduction_month"));
                    if (reductionFeeList == null) {
                        reductionFeeList = new ArrayList<>();
                    } else {
                        reductionFeeList.clear();
                    }

                    // 使用映射表将键与枚举对应，减少重复代码
                    Map<String, FeeSubjectEnum> deductionMapping = new HashMap<>();
                    deductionMapping.put("Out_FromDeduction_Principal", FeeSubjectEnum.PRIN_AMT);
                    deductionMapping.put("Out_FromDeduction_intAmt", FeeSubjectEnum.INT_AMT);
                    deductionMapping.put("Out_FromDeduction_Fee1Amt", FeeSubjectEnum.FEE1);
                    deductionMapping.put("Out_FromDeduction_Fee2Amt", FeeSubjectEnum.FEE2);
                    deductionMapping.put("Out_FromDeduction_Fee3Amt", FeeSubjectEnum.FEE3);
                    deductionMapping.put("Out_FromDeduction_Settle_Fee", FeeSubjectEnum.FEE4);
                    deductionMapping.put("Out_FromDeduction_OintAmt", FeeSubjectEnum.OINT_AMT);
                    deductionMapping.put("Out_FromDeduction_Fee6Amt", FeeSubjectEnum.FEE6);

                    // 遍历映射表，统一处理逻辑
                    for (Map.Entry<String, FeeSubjectEnum> entry : deductionMapping.entrySet()) {
                        BigDecimal value = new BigDecimal(result.get(entry.getKey()));
                        if (value.compareTo(BigDecimal.ZERO) > 0) {
                            reductionFeeList.add(entry.getValue());
                        }
                    }

                    LocalDateTime date = LocalDateTime.now().plusMonths(-month);
                    time = LocalDateTimeUtils.parseDateByLocalDateTime(date);
                }
            }

            CreateRepaymentPlanRequest createRepaymentPlanRequest = new CreateRepaymentPlanRequest();
            createRepaymentPlanRequest.setRequestNo(getRequestNo(ReqNoConstants.VOCCRP, repaymentPlan.getId()));
            repayFacadeClient.createRepaymentPlan(createRepaymentPlanRequest, planDetailInfos, employeeService.getUserNameForIdentify(UserContextHolder.getUserIdentify()), request.getBeReducedLoanNos(), reductionFeeList, time);
            //方案状态更新为生效中
            repaymentPlan.setPlanStatus(1);
            repaymentPlanMapper.updateById(repaymentPlan);
            request.setPlanId(repaymentPlan.getId());
        }

        return request;
    }

    @Override
    @Transactional
    public Boolean repay(RepayPlanRequest request) {
        if (!isSend(request) || request.getRepayMethod() == 1) {
            return true;
        }

        isAllowSend(request);
        RepaymentPlan repaymentPlan = repaymentPlanMapper.queryEffectRepayPlan(request.getPlanId());
        if (repaymentPlan == null) {
            throw new IgnoreException(TechplayErrDtlEnum.REPAY_ERROR, "无此生效中方案");
        }

        List<RepaymentPlanDetail> repaymentPlanDetails = repaymentPlanDetailMapper.queryPlanDetails(request.getPlanId());
        if (CollectionUtils.isEmpty(repaymentPlanDetails)) {
            throw new IgnoreException(TechplayErrDtlEnum.REPAY_ERROR, "此方案无明细");
        }

        triggerRepayment(request, repaymentPlan);
        return true;
    }

    @Override
    public List<FeeRatioProcessResp> rateCalculation(List<FeeRatioProcessReq> req) {
        List<FeeRatioProcessResp> feeRatioProcessResps = new ArrayList<>();
        for (FeeRatioProcessReq feeRatioProcessReq : req) {
            BigDecimal targetFeeRatio = null;
            if (feeRatioProcessReq.getTargetFeeRatio() != null) {
                targetFeeRatio = feeRatioProcessReq.getTargetFeeRatio().multiply(new BigDecimal("0.01"));
            }

            RepayLoanCalcResponse response = repayFacadeClient.repayCalculateFeeRatio(feeRatioProcessReq.getLoanNo(),
                    feeRatioProcessReq.getRepayType() == 1 ? RepayLoanCalcRequest.CalcSettleTypeEnum.NO_SETTLE : RepayLoanCalcRequest.CalcSettleTypeEnum.SETTLE,
                    feeRatioProcessReq.getTerms(), targetFeeRatio, feeRatioProcessReq.getTargetDeduct());

            if (response != null && response.getDeductCalculateResponse() != null) {
                FeeRatioProcessResp resp = new FeeRatioProcessResp();
                resp.setSuggestedRefundAmt(response.getDeductCalculateResponse().getSuggestedRefundAmt());
                resp.setRefundAmtInProcess(response.getDeductCalculateResponse().getRefundAmtInProcess());
                resp.setFeeRatioAfterDeduct(response.getDeductCalculateResponse().getFeeRatioAfterDeduct() != null ? response.getDeductCalculateResponse().getFeeRatioAfterDeduct().multiply(new BigDecimal("100")) : null);

                FeeAmountDtoResp feeAmountDtoResp = RepayConverter.INSTANCE.feeAmountDtoToFeeAmountDtoResp(response.getDeductCalculateResponse().getSuggestedDeductFeeAmtDetail());
                feeAmountDtoResp.setGuaranteeFee(feeAmountDtoResp.getTransFee1().add(feeAmountDtoResp.getTransFee2()));
                feeAmountDtoResp.setLateFee(feeAmountDtoResp.getTransOint().add(feeAmountDtoResp.getTransFee3()).add(feeAmountDtoResp.getTransFee6()));

                resp.setSuggestedDeductFeeAmtDetail(feeAmountDtoResp);
                feeRatioProcessResps.add(resp);
            }
        }

        return feeRatioProcessResps;
    }


    @Override
    public List<LoanCalculateDto> loanCalculate(LoanCalculateRequest request) {
        LoanPlanRequest loanPlanRequest = new LoanPlanRequest();
        loanPlanRequest.setLoanNos(request.getLoanInfos().stream().map(LoanInfo::getLoanNo).collect(Collectors.toList()));
        List<LoanPlanResponse> loanPlanResponses = lcsFeignService.planDetail(loanPlanRequest);
        if (CollectionUtils.isNotEmpty(loanPlanResponses.stream().filter(r -> "FP".equals(r.getStatus())).collect(Collectors.toList()))) {
            throw new IgnoreException(TechplayErrDtlEnum.REPAY_ERROR, "存在已结清订单");
        }

        List<LoanCalculateDto> list = new ArrayList<>();
        for (LoanInfo loanInfo : request.getLoanInfos()) {
            LoanCalculateDto loanCalculateDto = new LoanCalculateDto();
            loanCalculateDto.setOrderNo(loanInfo.getOrderNo());
            loanCalculateDto.setLoanNo(loanInfo.getLoanNo());
            loanCalculateDto.setRpyFlag(loanInfo.getRpyFlag());
            loanCalculateDto.setOrderType(loanInfo.getOrderType());
            loanCalculateDto.setPlanType(loanInfo.getPlanType());

            List<String> terms = getPlanSource(loanInfo, loanCalculateDto);

            RepayCalculateDto calculateDto = commonService.getRepayCalculateDto(loanCalculateDto.getPlanType(), terms, loanCalculateDto.getLoanNo(), null);
            loanCalculateDto.setRealAmt(calculateDto.getRealAmt());
            if (loanCalculateDto.getTotalReduct() != null) {
                loanCalculateDto.setOldRealAmt(loanCalculateDto.getRealAmt().add(loanCalculateDto.getTotalReduct()));
            }
            if (PlanSourceEnum.HUTTA.getCode().equals(loanCalculateDto.getPlanSource())) {
                loanCalculateDto.setTotalReduct(calculateDto.getDeductAmt());
            }
            if (loanInfo.getPlanType() == 2) {
                loanCalculateDto.setTerms(getTerms(loanInfo.getLoanNo()));
            } else {
                loanCalculateDto.setTerms(terms);
            }

            //资方回购限制剔除
            buyBackCheck(loanInfo, loanCalculateDto);
            list.add(loanCalculateDto);
        }

        return list;
    }

    private List<String> getPlanSource(LoanInfo loanInfo, LoanCalculateDto loanCalculateDto) {
        RepaymentPlan effectPlan = repaymentPlanDetailMapper.queryEffectPlan(loanInfo.getLoanNo(), null);
        List<String> terms = loanInfo.getTerms();
        if (effectPlan != null) {
            //客服有生效中方案
            List<RepaymentPlanDetail> repaymentPlanDetails = repaymentPlanDetailMapper.queryPlanDetails(effectPlan.getId());
            RepaymentPlanDetail repaymentPlanDetail = repaymentPlanDetails.stream().filter(r -> r.getLoanNo().equals(loanInfo.getLoanNo())).collect(Collectors.toList()).get(0);
            loanCalculateDto.setPlanStatus(effectPlan.getPlanStatus());
            loanCalculateDto.setPlanType(effectPlan.getPlanType());
            loanCalculateDto.setPlanSource(PlanSourceEnum.VOCMNG.getCode());
            loanCalculateDto.setId(effectPlan.getId());

            loanCalculateDto.setDeduction(repaymentPlanDetail.getDeduction());
            loanCalculateDto.setTotalReduct(repaymentPlanDetail.getGuarantReduct().add(repaymentPlanDetail.getPrinReduct()).add(repaymentPlanDetail.getIntReduct()).add(repaymentPlanDetail.getLateReduct()).add(repaymentPlanDetail.getRedReduct()));
            if (repaymentPlanDetail.getTerm() != null) {
                terms = Arrays.stream(repaymentPlanDetail.getTerm().split(","))
                        .collect(Collectors.toList());
            }
        } else {
            //贷后有生效中方案
            QueryRepaymentPlanResponse planResponse = repayFacadeClient.queryRepaymentPlan(loanInfo.getLoanNo(), null, DeductPlanChannelEnum.HUTTA.getCode(), 1);
            if (planResponse != null && CollectionUtils.isNotEmpty(planResponse.getPlanDeductList())) {
                for (QueryRepaymentPlanResponse.PlanDeduct planDeduct : planResponse.getPlanDeductList()) {
                    Integer planStatus = getPlanStatus(planDeduct);
                    if (planStatus == 1) {
                        loanCalculateDto.setPlanSource(PlanSourceEnum.HUTTA.getCode());
                        loanCalculateDto.setPlanStatus(planStatus);
                        loanCalculateDto.setPlanDetailId(planDeduct.getPlanDetailId());
                        loanCalculateDto.setPlanType("NO_SETTLE".equals(planDeduct.getSettleType()) ? 1 : 2);
                        terms = planDeduct.getTerms();
                    }
                }
            }
        }
        return terms;
    }

    private List<String> getTerms(String loanNo) {
        List<String> terms = null;
        GetBillListRequest getBillListRequest = new GetBillListRequest();
        getBillListRequest.setLoanNos(Collections.singletonList(loanNo));
        List<LoanPlanDto> loanPlans = loanService.queryBillList(getBillListRequest);
        if (CollectionUtils.isNotEmpty(loanPlans) && loanPlans.size() == 1) {
            terms = loanPlans.get(0).getPlanList().stream().filter(r -> !"2".equals(r.getRpyFlag())).map(PlanDto::getTerm).map(Object::toString).collect(Collectors.toList());
            terms = terms.stream().sorted().collect(Collectors.toList());
        }
        return terms;
    }

    @Override
    public List<WriteOffResponse> writeOff(WriteOffRequest writeOffRequest) {
        List<WriteOffResponse> result = new ArrayList<>();
        Iterator<FlowDetail> flowDetailIterator = writeOffRequest.getFlowDetails().iterator();
        for (WriteOffLoanDetail writeOffLoanDetail : writeOffRequest.getWriteOffDetails()) {
            WriteOffResponse response = new WriteOffResponse();
            try {
                response.setLoanNo(writeOffLoanDetail.getLoanNo());
                response.setTerms(writeOffLoanDetail.getTerms());
                response.setIsSuccess(true);

                List<FlowDetail> flowDetails = getFlowDetails(writeOffLoanDetail, flowDetailIterator);

                Long id = 0L;
                for (FlowDetail flowDetail : flowDetails) {
                    FlowLoanMapping fl = new FlowLoanMapping();
                    fl.setLoanNo(writeOffLoanDetail.getLoanNo());
                    fl.setFlowNo(flowDetail.getFlowNo());
                    fl.setType(1);
                    flowLoanMappingMapper.insert(fl);
                    id = fl.getId();
                }
                repayFacadeClient.liveRepayApply(generateLiveRepay(writeOffLoanDetail, id, flowDetails));
            } catch (Exception e) {
                log.warn("liveRepayApply warning");
                response.setIsSuccess(false);
                response.setMessage(e.getMessage());
            }
            result.add(response);

            if (CollectionUtils.isNotEmpty(writeOffRequest.getFlowDetails())) {
                flowDetailIterator = writeOffRequest.getFlowDetails().iterator();
            }
        }
        return result;
    }

    /**
     * 对借据分配流水及销账金额
     *
     * @param writeOffLoanDetail
     * @param flowDetailIterator
     * @return
     */
    @NotNull
    private static List<FlowDetail> getFlowDetails(WriteOffLoanDetail writeOffLoanDetail, Iterator<FlowDetail> flowDetailIterator) {
        List<FlowDetail> flowDetails = new ArrayList<>();
        BigDecimal sum = BigDecimal.ZERO;
        while (flowDetailIterator.hasNext() && sum.compareTo(writeOffLoanDetail.getAmount()) < 0) {
            FlowDetail flowDetail = flowDetailIterator.next();
            sum = sum.add(flowDetail.getAmount());

            FlowDetail newFlowDetail = new FlowDetail();
            if (sum.compareTo(writeOffLoanDetail.getAmount()) > 0) {
                newFlowDetail.setAmount(flowDetail.getAmount().subtract(sum.subtract(writeOffLoanDetail.getAmount())));
                newFlowDetail.setFlowNo(flowDetail.getFlowNo());
                newFlowDetail.setChannelCode(flowDetail.getChannelCode());
                flowDetails.add(newFlowDetail);
                flowDetail.setAmount(flowDetail.getAmount().subtract(newFlowDetail.getAmount()));
            } else {
                newFlowDetail.setAmount(flowDetail.getAmount());
                newFlowDetail.setFlowNo(flowDetail.getFlowNo());
                newFlowDetail.setChannelCode(flowDetail.getChannelCode());
                flowDetails.add(newFlowDetail);
                flowDetailIterator.remove();
            }
        }
        return flowDetails;
    }

    @Override
    public List<WriteOffRecord> writeOffRecord(WriteOffRecordRequest request) {
        List<WriteOffRecord> writeOffRecords = new ArrayList<>();
        List<String> loans;
        if (CollectionUtils.isEmpty(request.getLoanNos())) {
            loans = flowLoanMappingMapper.queryLoanByFlow(request.getFlowNos());
            if (CollectionUtils.isEmpty(loans)) {
                return writeOffRecords;
            }
            request.setLoanNos(loans);
        }

        List<LiveRecordResponse> list = repayFacadeClient.liveRecord(request.getLoanNos(), request.getFlowNos());
        for (LiveRecordResponse response : list) {
            WriteOffRecord writeOffRecord = RepayConverter.INSTANCE.liveRecordToWriteOffRecords(response);
            if (CollectionUtils.isNotEmpty(response.getFlowDetail())) {
                List<FlowDetailDto> flowDetailDtos = response.getFlowDetail().stream().filter(r -> r.getFlowNo().equals(request.getFlowNos().get(0))).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(flowDetailDtos) && flowDetailDtos.get(0) != null) {
                    writeOffRecord.setFlowTransAmt(flowDetailDtos.get(0).getAmount());
                }
            }
            writeOffRecords.add(writeOffRecord);
        }

        return writeOffRecords;
    }

    @Override
    public Boolean writeOffReversal(WriteOffReversalRequest request) {
        if (newRepayCancel) {
            repayFacadeClient.cancel(request.getRepaymentNo(), request.getCancelResult(), employeeService.getUserNameForIdentify(UserContextHolder.getUserIdentify()));
        } else {
            repayFacadeClient.liveRepayCancel(request.getRepaymentNo(), request.getCancelResult(), employeeService.getUserNameForIdentify(UserContextHolder.getUserIdentify()));
        }
        return true;
    }

    public List<RepayCancelVO> repaymentCancel(WriteOffReversalRequest request) {
        return repayFacadeClient.cancel(request.getRepaymentNo(), request.getCancelResult(), employeeService.getUserNameForIdentify(UserContextHolder.getUserIdentify()));
    }

    private LiveRepayApplyRequest generateLiveRepay(WriteOffLoanDetail request, Long id, List<FlowDetail> flowDetails) {
        LiveRepayApplyRequest repayApplyRequest = new LiveRepayApplyRequest();
        repayApplyRequest.setPayType("live");
        if (id == 0) {
            repayApplyRequest.setRequestNo(ReqNoConstants.VOCLRA + SerialNumberGeneratorUtil.generate());
        } else {
            repayApplyRequest.setRequestNo(getRequestNo(ReqNoConstants.VOCLRA, id));
        }
        repayApplyRequest.setLoanNo(request.getLoanNo());
        BigDecimal flowAmount = flowDetails.stream().map(FlowDetail::getAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
        if (request.getAmount().compareTo(flowAmount) != 0) {
            repayApplyRequest.setAmount(flowAmount);
        } else {
            repayApplyRequest.setAmount(request.getAmount());
        }
        repayApplyRequest.setSettleType(request.getPlanType() == 1 ? RepayLoanCalcRequest.CalcSettleTypeEnum.NO_SETTLE : RepayLoanCalcRequest.CalcSettleTypeEnum.SETTLE);
        repayApplyRequest.setCreatedBy(employeeService.getUserNameForIdentify(UserContextHolder.getUserIdentify()));
        if (request.getPlanType() == 1) {
            List<PlanNoAndTermsDto> planInfo = new ArrayList<>();
            for (String term : request.getTerms()) {
                PlanNoAndTermsDto planNoAndTermsDto = new PlanNoAndTermsDto();
//                planNoAndTermsDto.setPlanNo(request.getBillNo());
                planNoAndTermsDto.setTerm(term);
                planInfo.add(planNoAndTermsDto);
            }
            repayApplyRequest.setPlanInfo(planInfo);
        }
        List<FlowDetailDto> flowDetailDtos = new ArrayList<>();
        for (FlowDetail flowDetail : flowDetails) {
            FlowDetailDto flowDetailDto = new FlowDetailDto();
            flowDetailDto.setAmount(flowDetail.getAmount());
            flowDetailDto.setFlowNo(flowDetail.getFlowNo());
            flowDetailDto.setChannelCode(flowDetail.getChannelCode());
            flowDetailDtos.add(flowDetailDto);
        }
        repayApplyRequest.setFlowDetail(flowDetailDtos);
        return repayApplyRequest;
    }

    @NotNull
    private static List<PlanDetailInfo> getPlanDetailInfos(List<RepaymentPlanDetail> repaymentPlanDetails, RepaymentPlan repaymentPlan) {
        List<PlanDetailInfo> planDetailInfos = new ArrayList<>();
        for (RepaymentPlanDetail repaymentPlanDetail : repaymentPlanDetails) {
            PlanDetailInfo planDetailInfo = RepayConverter.INSTANCE.planDetailToPlanDetailInfo(repaymentPlanDetail);
            planDetailInfo.setDeductChannel(PlanSourceEnum.VOCMNG.getCode());
            planDetailInfo.setSettleType(repaymentPlan.getPlanType() == 1 ? RepayLoanCalcRequest.CalcSettleTypeEnum.NO_SETTLE : RepayLoanCalcRequest.CalcSettleTypeEnum.SETTLE);
            if (repaymentPlan.getPlanType() == 1) {
                String[] terms = planDetailInfo.getTerms().split(",");
                if (terms.length > 1) {
                    planDetailInfo.setSettleType(RepayLoanCalcRequest.CalcSettleTypeEnum.NO_SETTLE_GROUP);
                }
            }
            planDetailInfo.setDateEffective(LocalDateTimeUtils.parseDateByLocalDateTime(repaymentPlan.getUpdatedTime()));
            planDetailInfo.setDateExpire(LocalDateTimeUtils.parseDateByLocalDateTime(repaymentPlan.getEndTime()));
            planDetailInfo.setDeductRule(DeductRuleEnum.DEDUCT_AMT_LIMIT.getCode());
            planDetailInfo.setTotalAmt(null);
            planDetailInfos.add(planDetailInfo);
        }
        return planDetailInfos;
    }

    @NotNull
    private List<RepaymentPlanDetail> getRepaymentPlanDetails(RepayPlanRequest request, RepaymentPlan repaymentPlan) {
        List<RepaymentPlanDetail> repaymentPlanDetails = new ArrayList<>();
        for (RepayPlanDetailRequest repayPlanDetailRequest : request.getRepayPlanDetails()) {
            RepaymentPlan effectPlan = repaymentPlanDetailMapper.queryEffectPlan(repayPlanDetailRequest.getLoanNo(), null);
            if (effectPlan != null) {
                throw new IgnoreException(TechplayErrDtlEnum.FEE_SHOW_ERROR, "该借据存在生效中方案，方案编号：" + effectPlan.getId());
            }
            if (repayPlanDetailRequest.getRepaymentAmount().compareTo(BigDecimal.ZERO) == 0 && request.getRepayMethod() != 4) {
                throw new IgnoreException(TechplayErrDtlEnum.REPAY_PLAN_ERROR, "应还金额=0，需走线下还款方式，通过线下销账处理");
            }

            RepaymentPlanDetail repaymentPlanDetail = new RepaymentPlanDetail();
            repaymentPlanDetail.setPlanId(repaymentPlan.getId());
            repaymentPlanDetail.setLoanNo(repayPlanDetailRequest.getLoanNo());
            repaymentPlanDetail.setOrderType(repayPlanDetailRequest.getOrderType());
            repaymentPlanDetail.setBillNo(repayPlanDetailRequest.getBillNo());
            repaymentPlanDetail.setPrinReduct(repayPlanDetailRequest.getPrinReduct() == null ? BigDecimal.ZERO : repayPlanDetailRequest.getPrinReduct());
            repaymentPlanDetail.setIntReduct(repayPlanDetailRequest.getIntReduct() == null ? BigDecimal.ZERO : repayPlanDetailRequest.getIntReduct());
            repaymentPlanDetail.setGuarantReduct(repayPlanDetailRequest.getGuarantReduct() == null ? BigDecimal.ZERO : repayPlanDetailRequest.getGuarantReduct());
            repaymentPlanDetail.setLateReduct(repayPlanDetailRequest.getLateReduct() == null ? BigDecimal.ZERO : repayPlanDetailRequest.getLateReduct());
            repaymentPlanDetail.setRedReduct(repayPlanDetailRequest.getRedReduct() == null ? BigDecimal.ZERO : repayPlanDetailRequest.getRedReduct());
            repaymentPlanDetail.setDeduction(repayPlanDetailRequest.getDeduction() == null ? BigDecimal.ZERO : repayPlanDetailRequest.getDeduction());
            repaymentPlanDetail.setRepayMethod(request.getRepayMethod());
            repaymentPlanDetail.setOrderNo(repayPlanDetailRequest.getOrderNo());
            repaymentPlanDetail.setRealRatio(repayPlanDetailRequest.getRealRatio());
            repaymentPlanDetail.setIsDel(0);
            repaymentPlanDetail.setUseStatus(0);
            repaymentPlanDetail.setStatus(1);
            if (request.getPlanType() == 1) {
                repaymentPlanDetail.setTerm(String.join(",", repayPlanDetailRequest.getTerms()));
            }
            repaymentPlanDetails.add(repaymentPlanDetail);
        }
        repaymentPlanDetailMapper.saveBatchRepaymentPlanDetail(repaymentPlanDetails);
        return repaymentPlanDetails;
    }

    @NotNull
    private RepaymentPlan getRepaymentPlan(RepayPlanRequest request) {
        RepaymentPlan repaymentPlan = new RepaymentPlan();
        repaymentPlan.setPlanType(request.getPlanType());
        repaymentPlan.setCustNo(request.getCustNo());
        repaymentPlan.setUserNo(request.getUserNo());
        repaymentPlan.setPlanStatus(0);
        repaymentPlan.setEndTime(request.getEndTime());
        repaymentPlan.setIsDel(0);
        repaymentPlan.setComplaintChannelLv1(request.getComplaintChannelLv1());
        repaymentPlan.setComplaintChannelLv2(request.getComplaintChannelLv2());
        if (request.getBeReducedLoanNos() != null) {
            repaymentPlan.setBeReducedLoanNos(gson.toJson(request.getBeReducedLoanNos()));
        }

        if (StringUtils.isNotEmpty(request.getMobile())) {
            String encodeMobile = cisFacadeClientService.getEncodeMobileLocal(request.getMobile());
            repaymentPlan.setMobile(encodeMobile);
        }

        if (StringUtils.isNotEmpty(request.getBankCardId())) {
            repaymentPlan.setBankCardId(request.getBankCardId());
        }


        //待审批
        if (request.isNeedReview()) {
            //待审批方案发送
            if (request.getIsSend() == 1) {
                repaymentPlan.setReviewStatus(0);
                redisUtils.set(RedisKeyConstants.END_TIME + repaymentPlan.getId(), repaymentPlan.getEndTime().toString(), 24L, TimeUnit.HOURS);
                repaymentPlan.setEndTime(LocalDate.now().plusDays(1).atStartOfDay());
            }

            //待审批方案保存
            if (request.getIsSend() == 2) {
                repaymentPlan.setReviewStatus(null);
            }
        }

        //审批
        if (request.getIsApproved() != null && request.getIsSend() != 2) {
            if (request.getIsApproved()) {
                repaymentPlan.setReviewStatus(1);
                repaymentPlan.setReviewReason(request.getReason());
                if (redisUtils.get(RedisKeyConstants.END_TIME + repaymentPlan.getId()) != null) {
                    repaymentPlan.setEndTime(LocalDateTimeUtils.parseLocalDateTimeByDateStrT(redisUtils.get(RedisKeyConstants.END_TIME + repaymentPlan.getId())));
                }
            } else {
                repaymentPlan.setReviewStatus(2);
                repaymentPlan.setReviewReason(request.getReason());
            }
        }

        if (request.getPlanId() != null) {
            repaymentPlan.setId(request.getPlanId());
            repaymentPlan.setUpdatedTime(LocalDateTime.now());
            repaymentPlan.setUpdater(UserContextHolder.getUserContext().getUserIdentify());
            repaymentPlanMapper.updateById(repaymentPlan);

            if (request.getIsApproved() == null || request.getIsApproved()) {
                List<RepaymentPlanDetail> repaymentPlanDetails = repaymentPlanDetailMapper.queryPlanDetails(repaymentPlan.getId());
                repaymentPlanDetails.forEach(r -> r.setIsDel(1));
                repaymentPlanDetailMapper.updatePlanDetailDel(repaymentPlanDetails.stream().map(RepaymentPlanDetail::getId).collect(Collectors.toList()));
            }
        } else {
            repaymentPlan.setCreator(UserContextHolder.getUserContext().getUserIdentify());
            repaymentPlan.setUpdater(UserContextHolder.getUserContext().getUserIdentify());
            repaymentPlan.setCreatedTime(LocalDateTime.now());
            repaymentPlan.setUpdatedTime(LocalDateTime.now());
            repaymentPlanMapper.insert(repaymentPlan);
        }
        return repaymentPlan;
    }

    public void triggerRepayment(RepayPlanRequest request, RepaymentPlan repaymentPlan) {
        //聚合支付
        if (request.getRepayMethod() == 3) {
            ShortLinkCreateRequest shortLinkCreateRequest;
            BigDecimal repaymentAmount;
            List<RepayPlanDetailRequest> repayPlanDetailRequest = request.getRepayPlanDetails();
            repaymentAmount = repayPlanDetailRequest.stream()
                    .map(RepayPlanDetailRequest::getRepaymentAmount)
                    .filter(Objects::nonNull)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            shortLinkCreateRequest = getShortLinkByLoans(request, repayPlanDetailRequest, repaymentPlan);

            //app收银台 调用
            whitelistMark(request, null);

            sendAggregateSms(request.getMobile(), shortLinkCreateRequest, repaymentAmount, repaymentPlan, request.getIsSendSms(), true, null);
        }

        //线下还款
        if (request.getRepayMethod() == 4) {
            BigDecimal totalAmount = request.getRepayPlanDetails().stream().map(RepayPlanDetailRequest::getRepaymentAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
            offlineRepayment(request.getMobile(), request.getUserNo(), totalAmount, request.getIsSendSms());
        }

        //在线抵扣
        if (request.getRepayMethod() == 2) {
            sysDeduction(request, repaymentPlan.getId());
        }
    }

    private void sysDeduction(RepayPlanRequest request, Long id) {
        for (RepayPlanDetailRequest detailRequest : request.getRepayPlanDetails()) {
            RepayApplyRequest repayApplyRequest = new RepayApplyRequest();
            repayApplyRequest.setLoanNo(detailRequest.getLoanNo());
            repayApplyRequest.setAmount(detailRequest.getRepaymentAmount());
            repayApplyRequest.setSettleType(request.getPlanType() == 1 ? RepayLoanCalcRequest.CalcSettleTypeEnum.NO_SETTLE : RepayLoanCalcRequest.CalcSettleTypeEnum.SETTLE);
            repayApplyRequest.setBankCardId(request.getBankCardId());
            repayApplyRequest.setCreatedBy(employeeService.getUserNameForIdentify(UserContextHolder.getUserIdentify()));
            repayApplyRequest.setRequestNo(getRequestNo(ReqNoConstants.VOCSRP, id));
            List<PlanNoAndTermsDto> planInfo = new ArrayList<>();

            if (request.getPlanType() == 1) {
                for (String term : detailRequest.getTerms()) {
                    PlanNoAndTermsDto planNoAndTermsDto = new PlanNoAndTermsDto();
//                planNoAndTermsDto.setPlanNo(detailRequest.getBillNo());
                    planNoAndTermsDto.setTerm(term);
                    planInfo.add(planNoAndTermsDto);
                }
                repayApplyRequest.setPlanInfo(planInfo);
            }
            repayFacadeClient.repayApply(repayApplyRequest);
        }
    }

    private void offlineRepayment(String mobile, String userNo, BigDecimal totalAmount, Boolean isSendSms) {
        //生产短信内容
        Map<String, Object> data = new HashMap<>();
        ThreeElementsDTO threeElements = cisFacadeClient.queryThreeElementsByUserNo(Long.parseLong(userNo));
        data.put("#name", threeElements.getName());
        data.put("#amount", totalAmount);
        PublicAccountRequest publicAccountRequest = new PublicAccountRequest();
        publicAccountRequest.setUserNo(userNo);

        //获取对公账户信息
        PublicAccountInfo publicAccountInfo = nPayService.queryPublicAccountInfo(publicAccountRequest);
        if (publicAccountInfo != null) {
            data.put("#kf_bankAccountName", publicAccountInfo.getBankAccountName());
            data.put("#kf_bankCardNo", publicAccountInfo.getBankCardNo());
            data.put("#kf_bankName", publicAccountInfo.getBankName());
            data.put("#kf_openBankBranch", publicAccountInfo.getOpenBankBranch());
            data.put("#kf_openCityName", publicAccountInfo.getOpenCityName());
            data.put("#kf_openBankNo", publicAccountInfo.getOpenBankNo());
        }

        log.info(LogUtil.infoLog("offlineData", JsonUtil.toJson(data)));
        //发送短信
        if (isSendSms) {
            smsFeignService.smsSend(mobile, loginUserConfig.getSmsOfflineRepaymentTemplateId(), threeElements.getApp(), data);
        }
    }

    private void isAllowSend(RepayPlanRequest request) {
        if (request.getRepayMethod() == 2) {
            if (StringUtils.isEmpty(request.getBankCardId())) {
                throw new IgnoreException(TechplayErrDtlEnum.REPAY_PLAN_ERROR, "系统代扣必传bankCardId");
            }
        }

        if (request.getRepayMethod() == 3) {
            if (request.getRepayPlanDetails().size() > 1 && !isBatchLoanLink) {
                throw new IgnoreException(TechplayErrDtlEnum.REPAY_PLAN_ERROR, "聚合支付暂时只支持单订单");
            }

            RepayPlanDetailRequest repayPlanDetailRequest = request.getRepayPlanDetails().get(0);
            if (repayPlanDetailRequest == null) {
                throw new IgnoreException(TechplayErrDtlEnum.REPAY_PLAN_ERROR, "聚合支付明细为空");
            }

            if (StringUtils.isEmpty(request.getMobile())) {
                throw new IgnoreException(TechplayErrDtlEnum.REPAY_PLAN_ERROR, "聚合支付必传手机号");
            }
        }

        if (request.getRepayMethod() == 4) {
            if (StringUtils.isEmpty(request.getMobile()) && request.getIsSendSms()) {
                throw new IgnoreException(TechplayErrDtlEnum.REPAY_PLAN_ERROR, "线下还款必传手机号");
            }
        }

    }

    private Boolean isSend(RepayPlanRequest request) {
        if (request.getIsApproved() != null) {
            return request.getIsApproved();
        }

        if (!request.isNeedReview()) {
            return request.getIsSend() == 1;
        }

        return false;
    }

    private GroupPaymentApplyRequest getGroupPaymentApplyRequest(String userNo, Integer planType, String loanNo, String billNo, List<String> terms, BigDecimal totalAmount, BigDecimal realAmount) {
        GroupPaymentApplyRequest paymentApplyRequest = new GroupPaymentApplyRequest();
        paymentApplyRequest.setUserNo(userNo);
        paymentApplyRequest.setLoanNo(loanNo);
        paymentApplyRequest.setSettleType(planType == 1 ? RepayLoanCalcRequest.CalcSettleTypeEnum.NO_SETTLE : RepayLoanCalcRequest.CalcSettleTypeEnum.SETTLE);
        if (planType == 1) {
            List<PlanNoAndTermsDto> planNoAndTerms = new ArrayList<>();
            for (String term : terms) {
                PlanNoAndTermsDto planNoAndTerm = new PlanNoAndTermsDto();
//            planNoAndTerms.setPlanNo(billNo);
                planNoAndTerm.setTerm(term);
                planNoAndTerms.add(planNoAndTerm);
            }
            paymentApplyRequest.setPlanInfo(planNoAndTerms);
        }
        paymentApplyRequest.setCreatedBy(employeeService.getUserNameForIdentify(UserContextHolder.getUserIdentify()));
        paymentApplyRequest.setTotalAmount(totalAmount);
        paymentApplyRequest.setRealAmount(realAmount);
        return paymentApplyRequest;
    }

    private ShortLinkCreateRequest getShortLinkByLoans(RepayPlanRequest request, List<RepayPlanDetailRequest> repayPlanDetailRequests, RepaymentPlan repaymentPlan) {
        ShortLinkCreateRequest shortLinkCreateRequest = new ShortLinkCreateRequest();
        shortLinkCreateRequest.setRequestId(getRequestNo(ReqNoConstants.VOCSLC, repaymentPlan.getId()));
        shortLinkCreateRequest.setUserNo(request.getUserNo());
        shortLinkCreateRequest.setCustNo(request.getCustNo());

        List<BizMetadata> bizMetadatas = new ArrayList<>();
        for (RepayPlanDetailRequest repayPlanDetailRequest : repayPlanDetailRequests) {
            BizMetadata bizMetadata = new BizMetadata();
            bizMetadata.setLoanNo(repayPlanDetailRequest.getLoanNo());
            bizMetadata.setCalcSettleType(request.getPlanType() == 1 ? "NO_SETTLE" : "SETTLE");
            if (StringUtils.isNotEmpty(bizMetadata.getCalcSettleType()) && "NO_SETTLE".equals(bizMetadata.getCalcSettleType())) {
                bizMetadata.setTerms(repayPlanDetailRequest.getTerms().stream().map(Objects::toString).collect(Collectors.toList()));
            }
            ManageOrderDetailDTO manageOrderDetailDTO = lendQueryClientService.getOrderNoByLoanNo(repayPlanDetailRequest.getLoanNo());
            bizMetadata.setApp(manageOrderDetailDTO.getApp());
            bizMetadatas.add(bizMetadata);
        }
        shortLinkCreateRequest.setBizMetadata(JsonUtil.toJson(bizMetadatas));

        shortLinkCreateRequest.setCreatedBy(employeeService.getUserNameForIdentify(UserContextHolder.getUserIdentify()));
        shortLinkCreateRequest.setExpiredTime(LocalDateTimeUtils.parseDateByLocalDateTime(repaymentPlan.getEndTime()));

        return shortLinkCreateRequest;
    }

    private ShortLinkCreateRequest getShortLinkByLoans(RepayMethodRequest request, List<RepayMethodDetail> repayMethodDetails, RepaymentPlan repaymentPlan) {
        ShortLinkCreateRequest shortLinkCreateRequest = new ShortLinkCreateRequest();
        shortLinkCreateRequest.setRequestId(getRequestNo(ReqNoConstants.VOCSLC, repaymentPlan == null ? request.getPlanDetailId() : repaymentPlan.getId()));
        shortLinkCreateRequest.setUserNo(request.getUserNo());
        shortLinkCreateRequest.setCustNo(request.getCustNo());

        List<BizMetadata> bizMetadatas = new ArrayList<>();
        for (RepayMethodDetail repayMethodDetail : repayMethodDetails) {
            BizMetadata bizMetadata = new BizMetadata();
            bizMetadata.setLoanNo(repayMethodDetail.getLoanNo());
            bizMetadata.setCalcSettleType(request.getPlanType() == 1 ? "NO_SETTLE" : "SETTLE");
            if (StringUtils.isNotEmpty(bizMetadata.getCalcSettleType()) && "NO_SETTLE".equals(bizMetadata.getCalcSettleType())) {
                bizMetadata.setTerms(repayMethodDetail.getTerms().stream().map(Objects::toString).collect(Collectors.toList()));
            }
            ManageOrderDetailDTO manageOrderDetailDTO = lendQueryClientService.getOrderNoByLoanNo(repayMethodDetail.getLoanNo());
            bizMetadata.setApp(manageOrderDetailDTO.getApp());
            bizMetadatas.add(bizMetadata);
        }
        shortLinkCreateRequest.setBizMetadata(JsonUtil.toJson(bizMetadatas));

        shortLinkCreateRequest.setCreatedBy(employeeService.getUserNameForIdentify(UserContextHolder.getUserIdentify()));
        shortLinkCreateRequest.setExpiredTime(LocalDateTimeUtils.parseDateByLocalDateTime(repaymentPlan != null ? repaymentPlan.getEndTime() : LocalDateTime.parse(request.getEndTime(), formatter)));

        return shortLinkCreateRequest;
    }

    private ShortLinkVO aggregate(ShortLinkCreateRequest request) {
        return managementFacadeClient.linkCreate(request);
    }

    public void sendAggregateSms(String mobile, ShortLinkCreateRequest shortLinkCreateRequest, BigDecimal realAmount, RepaymentPlan repaymentPlan, Boolean isSendSms, Boolean isVocMng, String endTime) {
        //获取收银台短链
        ShortLinkVO shortLinkDTO = aggregate(shortLinkCreateRequest);
        if (StringUtils.isEmpty(shortLinkDTO.getShortLink()) || StringUtils.isEmpty(shortLinkDTO.getRenderKey())) {
            throw new TechplayException(TechplayErrDtlEnum.REPAY_ERROR, "收银台短链生成为空");
        }

        //生产短信内容
        Map<String, Object> data = new HashMap<>();
        ThreeElementsDTO threeElements = cisFacadeClient.queryThreeElementsByUserNo(Long.parseLong(shortLinkCreateRequest.getUserNo()));
        data.put("#name", threeElements.getName());
        data.put("#amount", realAmount);
        data.put("#url", shortLinkDTO.getShortLink());
        data.put("#time", endTime);

        //客服方案落手机号、落入renderKey
        if (isVocMng) {
            data.put("#time", repaymentPlan.getEndTime());
            //如果是客服的方案才修改数据
            repaymentPlan.setRenderKey(shortLinkDTO.getRenderKey());
            repaymentPlanMapper.updateById(repaymentPlan);
        }
        log.info(LogUtil.infoLog("shortLink", JsonUtil.toJson(data)));

        //发送短信
        if (isSendSms) {
            smsFeignService.smsSend(mobile, loginUserConfig.getSmsAggregatePaymentTemplateId(), threeElements.getApp(), data);
        }
    }

    public void whitelistMark(RepayPlanRequest request, RepayMethodRequest repayMethodRequest) {
        //app收银台
        WhitelistMarkRequest whitelistMarkRequest = new WhitelistMarkRequest();
        whitelistMarkRequest.setBizType("REPAYTRADE"); //REPAYTRADE 还款业务
        if (request != null) {
            whitelistMarkRequest.setWhiteType("USER");
            whitelistMarkRequest.setWhiteValue(request.getUserNo());
            whitelistMarkRequest.setUserNo(request.getUserNo());
            whitelistMarkRequest.setCustNo(request.getCustNo());
        } else if (repayMethodRequest != null) {
            whitelistMarkRequest.setWhiteType("USER");
            whitelistMarkRequest.setWhiteValue(repayMethodRequest.getUserNo());
            whitelistMarkRequest.setUserNo(repayMethodRequest.getUserNo());
            whitelistMarkRequest.setCustNo(repayMethodRequest.getCustNo());
        }
        whitelistMarkRequest.setFunctionScope("RENDER"); //RENDER 渲染
        managementFacadeClient.whitelistMark(whitelistMarkRequest);
    }


    @Override
    public PageResultResponse<RepaymentPlanDto> queryPlanList(RepayPlanListRequest request) {
        LambdaQueryWrapper<RepaymentPlan> wrapper = new LambdaQueryWrapper<>();
        wrapper = getPlanListWrapper(request, wrapper);

        List<RepaymentPlanDto> res = new ArrayList<>();
        if (StringUtils.isEmpty(request.getOrderNo())) {
            if (wrapper == null) {
                return new PageResultResponse<>();
            }

            IPage<RepaymentPlan> page = new Page<>(request.getCurrentPage(), request.getPageSize());
            IPage<RepaymentPlan> pageList = repaymentPlanMapper.selectPage(page, wrapper);
            if (Objects.nonNull(pageList) && CollectionUtils.isNotEmpty(pageList.getRecords())) {
                for (RepaymentPlan repaymentPlan : pageList.getRecords()) {
                    RepaymentPlanDto repaymentPlanDto = getRepaymentPlanDto(repaymentPlan);
                    repaymentPlanDto.setSolutionType("订单");
                    res.add(repaymentPlanDto);
                }
            }
            return new PageResultResponse<>(res, pageList.getCurrent(), pageList.getSize(), pageList.getTotal());

        } else {
            //订单号不为空的情况 先查询客服方案
            if (wrapper != null) {
                List<RepaymentPlan> repaymentPlansList = repaymentPlanMapper.selectList(wrapper);
                if (CollectionUtils.isNotEmpty(repaymentPlansList)) {
                    for (RepaymentPlan repaymentPlan : repaymentPlansList) {
                        RepaymentPlanDto repaymentPlanDto = getRepaymentPlanDto(repaymentPlan);
                        repaymentPlanDto.setSolutionType("订单");
                        res.add(repaymentPlanDto);
                    }
                }
            }

            if (!request.getIsReview()) {
                //查询贷后方案
                ManageOrderDetailDTO manageOrderDetailDTO = lendQueryClientService.getLoanNoByOrderNo(request.getOrderNo());
                //根据借据号获取方案
                if (StringUtils.isNotEmpty(manageOrderDetailDTO.getLoanNo())) {
                    ManageOrderListRequest manageOrderListRequest = new ManageOrderListRequest();
                    manageOrderListRequest.setLoanNos(Arrays.asList(manageOrderDetailDTO.getLoanNo()));
                    com.xinfei.lendtrade.facade.rr.dto.Page<ManageOrderDetailDTO> respDTO = lendQueryFacadeClient.getOrderList(manageOrderListRequest);
                    QueryRepaymentPlanResponse planResponse = repayFacadeClient.queryRepaymentPlan(manageOrderDetailDTO.getLoanNo(), null, DeductPlanChannelEnum.HUTTA.getCode(), null);
                    if (planResponse != null && CollectionUtils.isNotEmpty(planResponse.getPlanDeductList())) {
                        for (QueryRepaymentPlanResponse.PlanDeduct planDeduct : planResponse.getPlanDeductList()) {
                            RepaymentPlanDto repaymentPlanDto = new RepaymentPlanDto();
                            repaymentPlanDto.setPlanSource(PlanSourceEnum.HUTTA.getCode());
                            repaymentPlanDto.setLoanNos(Collections.singletonList(planDeduct.getLoanNo()));
                            repaymentPlanDto.setOrderNos(Collections.singletonList(request.getOrderNo()));
                            repaymentPlanDto.setCreatedTime(LocalDateTimeUtils.toLocalDateTime(planDeduct.getDateEffective()));
                            repaymentPlanDto.setEndTime(LocalDateTimeUtils.toLocalDateTime(planDeduct.getDateExpire()));
                            repaymentPlanDto.setPlanStatus(getPlanStatus(planDeduct));
                            repaymentPlanDto.setPlanDetailId(planDeduct.getPlanDetailId());
                            if (respDTO != null && CollectionUtils.isNotEmpty(respDTO.getPageList())) {
                                repaymentPlanDto.setUserNo(respDTO.getPageList().get(0).getUserNo().toString());
                                repaymentPlanDto.setCustNo(respDTO.getPageList().get(0).getCustNo());
                            }
                            repaymentPlanDto.setSolutionType("订单");
                            res.add(repaymentPlanDto);
                        }
                    }
                }
            }

            if (request.getPlanStatus() != null) {
                res = res.stream().filter(r -> r.getPlanStatus().equals(request.getPlanStatus())).collect(Collectors.toList());
            }

            if (request.getCreatedTimeStart() != null) {
                res = res.stream().filter(r -> r.getCreatedTime().isAfter(request.getCreatedTimeStart())).collect(Collectors.toList());
            }

            if (request.getCreatedTimeEnd() != null) {
                res = res.stream().filter(r -> r.getCreatedTime().isBefore(request.getCreatedTimeEnd())).collect(Collectors.toList());
            }

            int total = res.size();
            res = res.stream()
                    .skip((long) (request.getCurrentPage() - 1) * request.getPageSize())
                    .limit(request.getPageSize()).
                    collect(Collectors.toList());
            res = res.stream().sorted(Comparator.comparing(RepaymentPlanDto::getCreatedTime).reversed()).collect(Collectors.toList());
            return new PageResultResponse<>(res, request.getCurrentPage(), request.getPageSize(), total);
        }
    }

    private RepaymentPlanDto getRepaymentPlanDto(RepaymentPlan repaymentPlan) {
        RepaymentPlanDto repaymentPlanDto = RepayConverter.INSTANCE.repaymentPlanToRepaymentPlanDto(repaymentPlan);
        repaymentPlanDto.setCreator(employeeService.getUserNameForIdentify(repaymentPlanDto.getCreator()));
        repaymentPlanDto.setUpdater(employeeService.getUserNameForIdentify(repaymentPlanDto.getUpdater()));
        List<RepaymentPlanDetail> repaymentPlanDetails = repaymentPlanDetailMapper.queryPlanDetails(repaymentPlan.getId());
        List<RepaymentPlanDetailDto> repaymentPlanDetailDtos = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(repaymentPlanDetails)) {
            repaymentPlanDto.setLoanNos(repaymentPlanDetails.stream().map(RepaymentPlanDetail::getLoanNo).collect(Collectors.toList()));
            repaymentPlanDto.setOrderNos(repaymentPlanDetails.stream().map(RepaymentPlanDetail::getOrderNo).collect(Collectors.toList()));

            Map<String, String> loanOrderNos = repaymentPlanDetails.stream().collect(Collectors.toMap(RepaymentPlanDetail::getLoanNo, RepaymentPlanDetail::getOrderNo));
            repaymentPlanDto.setLoanOrderNos(loanOrderNos);

            BigDecimal totalGuarantReduct = repaymentPlanDetails.stream().map(RepaymentPlanDetail::getGuarantReduct).collect(Collectors.toList()).stream().reduce(BigDecimal.ZERO, BigDecimal::add);
            BigDecimal getIntReduct = repaymentPlanDetails.stream().map(RepaymentPlanDetail::getIntReduct).collect(Collectors.toList()).stream().reduce(BigDecimal.ZERO, BigDecimal::add);
            BigDecimal getPrinReduct = repaymentPlanDetails.stream().map(RepaymentPlanDetail::getPrinReduct).collect(Collectors.toList()).stream().reduce(BigDecimal.ZERO, BigDecimal::add);
            BigDecimal getLateReduct = repaymentPlanDetails.stream().map(RepaymentPlanDetail::getLateReduct).collect(Collectors.toList()).stream().reduce(BigDecimal.ZERO, BigDecimal::add);
            BigDecimal getRedReduct = repaymentPlanDetails.stream().map(RepaymentPlanDetail::getRedReduct).collect(Collectors.toList()).stream().reduce(BigDecimal.ZERO, BigDecimal::add);
            repaymentPlanDto.setExemptionAmt(totalGuarantReduct.add(getIntReduct).add(getPrinReduct).add(getLateReduct).add(getRedReduct));
            repaymentPlanDto.setDeductionAmt(repaymentPlanDetails.stream().map(RepaymentPlanDetail::getDeduction).collect(Collectors.toList()).stream().reduce(BigDecimal.ZERO, BigDecimal::add));
            for (RepaymentPlanDetail repaymentPlanDetail : repaymentPlanDetails) {
                RepaymentPlanDetailDto planDetailResp = RepayConverter.INSTANCE.planDetailToPlanDetailDto(repaymentPlanDetail);
                repaymentPlanDetailDtos.add(planDetailResp);
            }
        }

        if (commonService.disabledPlan(repaymentPlanDetails, repaymentPlan)) {
            repaymentPlanDto.setPlanStatus(PlanStatus.INVALID.getCode());
        }

        repaymentPlanDto.setPlanSource(PlanSourceEnum.VOCMNG.getCode());
        repaymentPlanDto.setRepaymentPlanDetails(repaymentPlanDetailDtos);
        if (repaymentPlanDto.getPlanType() == 1) {
            Map<String, Integer> loanNoTerms = new HashMap<>();
            for (RepaymentPlanDetailDto repaymentPlanDetail : repaymentPlanDetailDtos) {
                if (repaymentPlanDetail.getTerms() != null) {
                    loanNoTerms.put(repaymentPlanDetail.getLoanNo(), repaymentPlanDetail.getTerms().size());
                }
            }
            repaymentPlanDto.setLoanNoTerms(loanNoTerms);
        }

        repaymentPlanDto.setMobile(cisFacadeClientService.batchDecrypt(repaymentPlan.getMobile()));
        return repaymentPlanDto;
    }

    private LambdaQueryWrapper<RepaymentPlan> getPlanListWrapper(RepayPlanListRequest req, LambdaQueryWrapper<RepaymentPlan> wrapper) {
        if (StringUtils.isNotEmpty(req.getOrderNo())) {
            List<RepaymentPlanDetail> repaymentPlanDetails = repaymentPlanDetailMapper.queryPlanIdByOrderNo(req.getOrderNo());
            if (CollectionUtils.isNotEmpty(repaymentPlanDetails)) {
                List<Long> planIds = repaymentPlanDetails.stream().map(RepaymentPlanDetail::getPlanId).collect(Collectors.toList());
                wrapper.in(CollectionUtils.isNotEmpty(planIds), RepaymentPlan::getId, planIds);
            } else {
                return null;
            }
        }

        if (StringUtils.isNotBlank(req.getMobile())) {
            List<UserSearchDTO> needUserSearch = new ArrayList<>();
            PageResult<UserSearchDTO> pageResult;
            int n = 1;
            do {
                pageResult = cisFacadeClient.queryUserList(req.getMobile(), null, null, n, 30);
                if (CollectionUtils.isEmpty(pageResult.getList())) {
                    break;
                }
                needUserSearch.addAll(pageResult.getList());
                n++;
            } while (true);

            List<UserSearchDTO> custNoUsers = commonService.getUserByMobileAndCust(req.getMobile());
            if (CollectionUtils.isNotEmpty(custNoUsers)) {
                needUserSearch.addAll(custNoUsers);
            }
            if (needUserSearch.isEmpty()) {
                return null;
            }
            List<Long> userList = needUserSearch.stream()
                    .map(UserSearchDTO::getUserNo)
                    .distinct()
                    .collect(Collectors.toList());
            wrapper.in(CollectionUtils.isNotEmpty(userList), RepaymentPlan::getUserNo, userList);
        }


        wrapper.eq(Objects.nonNull(req.getPlanId()), RepaymentPlan::getId, req.getPlanId());
        wrapper.eq(StringUtils.isNotBlank(req.getCustNo()), RepaymentPlan::getCustNo, req.getCustNo());
        wrapper.eq(Objects.nonNull(req.getPlanStatus()), RepaymentPlan::getPlanStatus, req.getPlanStatus());

        if (req.getIsReview()) {
            wrapper.isNotNull(RepaymentPlan::getReviewStatus);
            wrapper.eq(Objects.nonNull(req.getReviewStatus()), RepaymentPlan::getReviewStatus, req.getReviewStatus());
        } else {
            wrapper.and(sub -> { // 使用 lambda 构建子条件
                sub.isNull(RepaymentPlan::getReviewStatus)
                        .or()
                        .eq(RepaymentPlan::getReviewStatus, 1);
            });
        }

        wrapper.ge(Objects.nonNull(req.getCreatedTimeStart()), RepaymentPlan::getCreatedTime, req.getCreatedTimeStart());
        wrapper.le(Objects.nonNull(req.getCreatedTimeEnd()), RepaymentPlan::getCreatedTime, req.getCreatedTimeEnd());
        wrapper.eq(StringUtils.isNotBlank(req.getUserNo()), RepaymentPlan::getUserNo, req.getUserNo());
        wrapper.eq(RepaymentPlan::getIsDel, 0);
        wrapper.orderByDesc(RepaymentPlan::getCreatedTime);
        return wrapper;
    }

    @Override
    public RepayPlanDetailDto queryPlanDetails(PlanDetailRequest request) {
        RepayPlanDetailDto repayPlanDetailDto = new RepayPlanDetailDto();

        //获取还款方案，生成方案对象
        RepaymentPlan repaymentPlan = repaymentPlanMapper.queryRepaymentPlanById(request.getPlanId());
        if (repaymentPlan == null) {
            throw new IgnoreException(TechplayErrDtlEnum.DB_EXCEPTION, "无此方案");
        }
        RepaymentPlanResp resp = getRepaymentPlanResp(repaymentPlan, request);
        repayPlanDetailDto.setRepaymentPlan(resp);

        //获取还款方案明细，生成方案明细对象
        List<RepaymentPlanDetail> repaymentPlanDetails = repaymentPlanDetailMapper.queryPlanDetails(request.getPlanId());
        List<RepaymentPlanDetailDto> repaymentPlanDetailDtos = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(repaymentPlanDetails)) {
            repayPlanDetailDto.getRepaymentPlan().setRepayMethod(repaymentPlanDetails.get(0).getRepayMethod());
            for (RepaymentPlanDetail repaymentPlanDetail : repaymentPlanDetails) {
                RepaymentPlanDetailDto planDetailResp = RepayConverter.INSTANCE.planDetailToPlanDetailDto(repaymentPlanDetail);
                repaymentPlanDetailDtos.add(planDetailResp);
            }
        }

        //根据方案明细对象计算总减免
        for (RepaymentPlanDetailDto repaymentPlanDetailDto : repaymentPlanDetailDtos) {
            repaymentPlanDetailDto.setTotalReduct(repaymentPlanDetailDto.getGuarantReduct().add(repaymentPlanDetailDto.getPrinReduct()).add(repaymentPlanDetailDto.getIntReduct()).add(repaymentPlanDetailDto.getLateReduct()).add(repaymentPlanDetailDto.getRedReduct()));
        }

        //生效中方案处理
        if (repaymentPlan.getPlanStatus() == 1 || Objects.equals(repaymentPlan.getReviewStatus(), 0)) {
            //实时查询借据状态，去除已结清借据
            List<String> loanNos = repaymentPlanDetailDtos.stream().map(RepaymentPlanDetailDto::getLoanNo).filter(StringUtils::isNotEmpty).collect(Collectors.toList());
            LoanPlanRequest loanPlanRequest = new LoanPlanRequest();
            loanPlanRequest.setLoanNos(loanNos);
            List<LoanPlanResponse> loanPlanResponses = lcsFeignService.planDetail(loanPlanRequest);
            if (CollectionUtils.isEmpty(loanPlanResponses)) {
                return repayPlanDetailDto;
            }
            Map<String, LoanPlanResponse> mapLoanPlans = loanPlanResponses.stream().collect(Collectors.toMap(LoanPlanResponse::getLoanNo, r -> r));
            for (RepaymentPlanDetailDto repaymentPlanDetailDto : repaymentPlanDetailDtos) {
                ManageOrderDetailRequest manageOrderDetailRequest = new ManageOrderDetailRequest();
                manageOrderDetailRequest.setOrderNo(repaymentPlanDetailDto.getOrderNo());
                ManageOrderDetailDTO manageOrderDetailDTO = lendQueryFacadeClient.getOrderDetail(manageOrderDetailRequest);
                //判断渠道是否是JD渠道
                if ("xyf01".equals(manageOrderDetailDTO.getApp()) && "xyf01_jdjq".equals(manageOrderDetailDTO.getInnerApp()) && "XYF01-API-JINGDONGJIEQIAN".equals(manageOrderDetailDTO.getUtmSource())) {
                    repaymentPlanDetailDto.setIsJDWithhold(true);
                    //判断最后一期是否逾期超过90天
                    Optional<PlanResponse> lastTermPlanOpt = mapLoanPlans.get(repaymentPlanDetailDto.getLoanNo()).getPlanList().stream()
                            .max(Comparator.comparingInt(PlanResponse::getTerm));
                    if (lastTermPlanOpt.isPresent()) {
                        PlanResponse lastTermPlan = lastTermPlanOpt.get();
                        if ("1".equals(lastTermPlan.getRpyFlag()) && lastTermPlan.getOverdueDays() > 90) {
                            repaymentPlanDetailDto.setIsJDWithhold(false);
                        }
                    }
                }

                //判断反诈渠道限制
                repaymentPlanDetailDto.setIsAntiFraud(false);
                LocalDateTime dateCashTime = LocalDateTimeUtils.toLocalDateTime(manageOrderDetailDTO.getDateCash());
                LocalDateTime dateToCompare = dateCashTime.plusDays(vocConfig.getAntiFraudDay()).withHour(23).withMinute(59).withSecond(59).withNano(0);
                LocalDateTime now = LocalDateTime.now();
                boolean isInRange = now.isAfter(dateCashTime) && now.isBefore(dateToCompare);
                List<String> whiteList = gson.fromJson(vocConfig.getAntiFraudApp(), typeListString);
                if (whiteList.contains(manageOrderDetailDTO.getInnerApp()) && isInRange) {
                    repaymentPlanDetailDto.setIsAntiFraud(true);
                }

                //资方回购限制
                repaymentPlanDetailDto.setIsBuyBack(false);
                List<String> sourcList = gson.fromJson(vocConfig.getBuyBackFundSource(), typeListString);
                if(sourcList.contains(manageOrderDetailDTO.getFundSource())){
                    GetBillListRequest getBillListRequest = new GetBillListRequest();
                    getBillListRequest.setLoanNos(Collections.singletonList(manageOrderDetailDTO.getLoanNo()));
                    List<LoanPlanDto> loanPlans = loanService.queryBillList(getBillListRequest);
                    repaymentPlanDetailDto.setIsBuyBack(loanPlans.get(0).getPlanList().stream()
                            .filter(plan -> !"2".equals(plan.getRpyFlag())) // 剔除 rpyFlag="2" 已结清
                            .anyMatch(plan -> "0".equals(plan.getCompensatedType()))); // 判断是否存在 compensatedType="0" 未回购
                }

                try {
                    repaymentPlanDetailDto.setTotalTerms(mapLoanPlans.get(repaymentPlanDetailDto.getLoanNo()).getTerm());
                    if (mapLoanPlans.get(repaymentPlanDetailDto.getLoanNo()) == null || "FP".equals(mapLoanPlans.get(repaymentPlanDetailDto.getLoanNo()).getStatus())) {
                        continue;
                    }
                    //试算借据应还金额、原来应还金额
                    RepayCalculateDto calculateDto = commonService.getRepayCalculateDto(repaymentPlan.getPlanType(), repaymentPlanDetailDto.getTerms(), repaymentPlanDetailDto.getLoanNo(), null);
                    if (Objects.equals(repaymentPlan.getReviewStatus(), 0) && repaymentPlanDetailDto.getDeduction().compareTo(BigDecimal.ZERO) > 0) {
                        calculateDto = commonService.getRepayCalculateDto(repaymentPlan.getPlanType(), repaymentPlanDetailDto.getTerms(), repaymentPlanDetailDto.getLoanNo(), repaymentPlanDetailDto.getDeduction());
                    }
                    repaymentPlanDetailDto.setRealAmt(calculateDto.getRealAmt());
                    repaymentPlanDetailDto.setOldRealAmt(repaymentPlanDetailDto.getRealAmt().add(repaymentPlanDetailDto.getTotalReduct()));

                    //审核中的方案，试算应还包含了减免和抵扣金额
                    if (Objects.equals(repaymentPlan.getReviewStatus(), 0)) {
                        repaymentPlanDetailDto.setRealAmt(calculateDto.getRealAmt().subtract(repaymentPlanDetailDto.getTotalReduct()));
                        repaymentPlanDetailDto.setOldRealAmt(calculateDto.getRealAmt());
                    }

                } catch (Exception e) {
                    log.warn("detail repayCalculate", e);
                }
            }
        }

        repayPlanDetailDto.setRepaymentPlanDetails(repaymentPlanDetailDtos);
        return repayPlanDetailDto;
    }

    @Override
    public HuttaPlanDetailDto queryHuttaPlanDetail(HuttaDetailRequest request) {
        HuttaPlanDetailDto huttaPlanDetailDto = new HuttaPlanDetailDto();
        QueryRepaymentPlanResponse response = repayFacadeClient.queryRepaymentPlan(request.getLoanNo(), request.getPlanDetailId(), null, null);
        if (response == null || CollectionUtils.isEmpty(response.getPlanDeductList()) || response.getPlanDeductList().get(0) == null) {
            return huttaPlanDetailDto;
        }

        QueryRepaymentPlanResponse.PlanDeduct planDeduct = response.getPlanDeductList().get(0);
        QueryRepaymentPlanResponse.PlanDeduct.PlanDeductDetail planDeductDetail = response.getPlanDeductList().get(0).getPlanDeductDetailList().get(0);
        huttaPlanDetailDto = RepayConverter.INSTANCE.planDeductToHuttaPlanDetailDto(planDeduct);
        huttaPlanDetailDto.setOrderNo(lendQueryClientService.getOrderNoByLoanNo(planDeduct.getLoanNo()).getLoanReqNo());
        huttaPlanDetailDto.setTotalTerms(planDeductDetail.getTerm());
        Integer planStatus = getPlanStatus(planDeduct);
        if (planStatus == -1) {
            return huttaPlanDetailDto;
        }
        huttaPlanDetailDto.setPlanStatus(planStatus);

        if ("NO_SETTLE".equals(planDeduct.getSettleType())) {
            huttaPlanDetailDto.setPlanType(1);
        } else {
            huttaPlanDetailDto.setPlanType(2);
        }

        if (planStatus == 1) {
            RepayCalculateDto repayCalculateDto = commonService.getRepayCalculateDto(huttaPlanDetailDto.getPlanType(), planDeduct.getTerms(), planDeduct.getLoanNo(), null);
            huttaPlanDetailDto.setRealAmt(repayCalculateDto.getRealAmt());
            huttaPlanDetailDto.setTotalReduct(repayCalculateDto.getDeductAmt());
        } else {
            if (CollectionUtils.isNotEmpty(planDeduct.getPlanDeductDetailList())) {
                BigDecimal totalPrinAmt = planDeduct.getPlanDeductDetailList().stream().map(QueryRepaymentPlanResponse.PlanDeduct.PlanDeductDetail::getPrinAmt).reduce(BigDecimal.ZERO, BigDecimal::add);
                BigDecimal totalIntAmt = planDeduct.getPlanDeductDetailList().stream().map(QueryRepaymentPlanResponse.PlanDeduct.PlanDeductDetail::getIntAmt).reduce(BigDecimal.ZERO, BigDecimal::add);
                BigDecimal totalOintAmt = planDeduct.getPlanDeductDetailList().stream().map(QueryRepaymentPlanResponse.PlanDeduct.PlanDeductDetail::getOintAmt).reduce(BigDecimal.ZERO, BigDecimal::add);
                BigDecimal totalFee1Amt = planDeduct.getPlanDeductDetailList().stream().map(QueryRepaymentPlanResponse.PlanDeduct.PlanDeductDetail::getFee1Amt).reduce(BigDecimal.ZERO, BigDecimal::add);
                BigDecimal totalFee2Amt = planDeduct.getPlanDeductDetailList().stream().map(QueryRepaymentPlanResponse.PlanDeduct.PlanDeductDetail::getFee2Amt).reduce(BigDecimal.ZERO, BigDecimal::add);
                BigDecimal totalFee3Amt = planDeduct.getPlanDeductDetailList().stream().map(QueryRepaymentPlanResponse.PlanDeduct.PlanDeductDetail::getFee3Amt).reduce(BigDecimal.ZERO, BigDecimal::add);
                BigDecimal totalFee4Amt = planDeduct.getPlanDeductDetailList().stream().map(QueryRepaymentPlanResponse.PlanDeduct.PlanDeductDetail::getFee4Amt).reduce(BigDecimal.ZERO, BigDecimal::add);
                BigDecimal totalFee5Amt = planDeduct.getPlanDeductDetailList().stream().map(QueryRepaymentPlanResponse.PlanDeduct.PlanDeductDetail::getFee5Amt).reduce(BigDecimal.ZERO, BigDecimal::add);
                BigDecimal totalFee6Amt = planDeduct.getPlanDeductDetailList().stream().map(QueryRepaymentPlanResponse.PlanDeduct.PlanDeductDetail::getFee6Amt).reduce(BigDecimal.ZERO, BigDecimal::add);
                BigDecimal totalAmt = totalPrinAmt.add(totalIntAmt).add(totalOintAmt).add(totalFee1Amt).add(totalFee2Amt).add(totalFee3Amt).add(totalFee4Amt).add(totalFee5Amt).add(totalFee6Amt);

                if (DeductRuleEnum.DEDUCT_AMT_LIMIT.getCode().equals(planDeduct.getDeductRule())) {
                    huttaPlanDetailDto.setTotalReduct(totalAmt);
                } else {
                    huttaPlanDetailDto.setRealAmt(totalAmt);
                }
            }
        }

        PageResult<UserSearchDTO> pageResult = cisFacadeClient.queryUserList(null, null, request.getUserNo(), 1, 10);
        if (!CollectionUtils.isEmpty(pageResult.getList())) {
            UserSearchDTO userSearch = pageResult.getList().get(0);
            huttaPlanDetailDto.setBanks(customerServiceImpl.getBankInfo(userSearch.getCustNo(), userSearch.getApp()));
            huttaPlanDetailDto.setMobile(userSearch.getMobile());
            huttaPlanDetailDto.setCustNo(userSearch.getCustNo());
            huttaPlanDetailDto.setUserNo(userSearch.getUserNo());
        }

        ManageOrderDetailRequest manageOrderDetailRequest = new ManageOrderDetailRequest();
        manageOrderDetailRequest.setOrderNo(huttaPlanDetailDto.getOrderNo());
        ManageOrderDetailDTO manageOrderDetailDTO = lendQueryFacadeClient.getOrderDetail(manageOrderDetailRequest);

        //判断渠道是否是JD渠道
        if ("xyf01".equals(manageOrderDetailDTO.getApp()) && "xyf01_jdjq".equals(manageOrderDetailDTO.getInnerApp()) && "XYF01-API-JINGDONGJIEQIAN".equals(manageOrderDetailDTO.getUtmSource())) {
            huttaPlanDetailDto.setIsJDWithhold(true);
            //判断最后一期是否逾期超过90天
            List<String> loanNos = Collections.singletonList(planDeduct.getLoanNo());
            LoanPlanRequest loanPlanRequest = new LoanPlanRequest();
            loanPlanRequest.setLoanNos(loanNos);
            List<LoanPlanResponse> loanPlanResponses = lcsFeignService.planDetail(loanPlanRequest);
            if (CollectionUtils.isNotEmpty(loanPlanResponses)) {
                Optional<PlanResponse> lastTermPlanOpt = loanPlanResponses.get(0).getPlanList().stream()
                        .max(Comparator.comparingInt(PlanResponse::getTerm));
                if (lastTermPlanOpt.isPresent()) {
                    PlanResponse lastTermPlan = lastTermPlanOpt.get();
                    if ("1".equals(lastTermPlan.getRpyFlag()) && lastTermPlan.getOverdueDays() > 90) {
                        huttaPlanDetailDto.setIsJDWithhold(false);
                    }
                }
            }

        }

        //判断反诈渠道限制
        huttaPlanDetailDto.setIsAntiFraud(false);
        LocalDateTime dateCashTime = LocalDateTimeUtils.toLocalDateTime(manageOrderDetailDTO.getDateCash());
        LocalDateTime dateToCompare = dateCashTime.plusDays(vocConfig.getAntiFraudDay()).withHour(23).withMinute(59).withSecond(59).withNano(0);
        LocalDateTime now = LocalDateTime.now();
        boolean isInRange = now.isAfter(dateCashTime) && now.isBefore(dateToCompare);
        List<String> whiteList = gson.fromJson(vocConfig.getAntiFraudApp(), typeListString);
        if (whiteList.contains(manageOrderDetailDTO.getInnerApp()) && isInRange) {
            huttaPlanDetailDto.setIsAntiFraud(true);
        }

        //资方回购限制
        huttaPlanDetailDto.setIsBuyBack(false);
        List<String> sourcList = gson.fromJson(vocConfig.getBuyBackFundSource(), typeListString);
        if(sourcList.contains(manageOrderDetailDTO.getFundSource())){
            GetBillListRequest getBillListRequest = new GetBillListRequest();
            getBillListRequest.setLoanNos(Collections.singletonList(manageOrderDetailDTO.getLoanNo()));
            List<LoanPlanDto> loanPlans = loanService.queryBillList(getBillListRequest);
            huttaPlanDetailDto.setIsBuyBack(loanPlans.get(0).getPlanList().stream()
                    .filter(plan -> !"2".equals(plan.getRpyFlag())) // 剔除 rpyFlag="2" 已结清
                    .anyMatch(plan -> "0".equals(plan.getCompensatedType()))); // 判断是否存在 compensatedType="0" 未回购
        }

        return huttaPlanDetailDto;
    }

    @NotNull
    private RepaymentPlanResp getRepaymentPlanResp(RepaymentPlan repaymentPlan, PlanDetailRequest request) {
        RepaymentPlanResp resp = new RepaymentPlanResp();
        resp.setId(repaymentPlan.getId());
        resp.setPlanType(repaymentPlan.getPlanType());
        resp.setPlanStatus(repaymentPlan.getPlanStatus());
        resp.setEndTime(repaymentPlan.getEndTime());
        resp.setReviewStatus(repaymentPlan.getReviewStatus());
        resp.setReviewReason(repaymentPlan.getReviewReason());
        resp.setComplaintChannelLv1(repaymentPlan.getComplaintChannelLv1());
        resp.setComplaintChannelLv2(repaymentPlan.getComplaintChannelLv2());
        if (repaymentPlan.getBeReducedLoanNos() != null) {
            resp.setBeReducedLoanNos(gson.fromJson(repaymentPlan.getBeReducedLoanNos(), List.class));
        }

        UserSearchDTO userSearch = null;
        PageResult<UserSearchDTO> pageResult = cisFacadeClient.queryUserList(null, null, request.getUserNo(), 1, 10);
        if (!CollectionUtils.isEmpty(pageResult.getList())) {
            userSearch = pageResult.getList().get(0);
        }

        String bankCardList = redisUtils.get(RedisKeyConstants.REPAY_BANK_LIST + repaymentPlan.getId());
        if (StringUtils.isNotEmpty(bankCardList)) {
            List<BankDto> bankDtos = JsonUtil.parseJson(bankCardList, new TypeReference<List<BankDto>>() {
            });
            resp.setBanks(bankDtos);
        } else if (userSearch != null) {
            resp.setBanks(customerServiceImpl.getBankInfo(request.getCustNo(), userSearch.getApp()));
        }

        if (StringUtils.isEmpty(repaymentPlan.getMobile()) && userSearch != null) {
            resp.setMobile(userSearch.getMobile());
        } else {
            String mobile = cisFacadeClientService.batchDecrypt(repaymentPlan.getMobile());
            resp.setMobile(mobile);
        }

        String hasDailyTypeStr = redisUtils.get(RedisKeyConstants.HAS_DAILY + repaymentPlan.getId());
        resp.setHasDailyType(false);
        if (StringUtils.isNotEmpty(hasDailyTypeStr)) {
            Boolean hasDailyType = JsonUtil.parseJson(hasDailyTypeStr, Boolean.class);
            resp.setHasDailyType(hasDailyType);
        }

        return resp;
    }

    @Override
    public List<PlanInvalidResponse> repayPlanInvalid(List<PlanInvalidRequest> requests) {
        List<PlanInvalidResponse> responses = new ArrayList<>();

        for (PlanInvalidRequest planInvalidRequest : requests) {
            PlanInvalidResponse response = new PlanInvalidResponse();
            response.setIsSuccess(true);
            try {
                if (planInvalidRequest.getPlanId() != null) {
                    vocInvalid(planInvalidRequest, response);
                } else if (StringUtils.isNotEmpty(planInvalidRequest.getPlanDetailId())) {
                    response.setPlanDetailId(planInvalidRequest.getPlanDetailId());
                    repayFacadeClient.repaymentPlanInvalid(Collections.singletonList(planInvalidRequest.getPlanDetailId()), null, "客服主动失效");
                }
            } catch (Exception e) {
                log.warn("repayPlanInvalid warn");
                response.setIsSuccess(false);
                response.setMessage(e.getMessage());
            }
            responses.add(response);
        }

        return responses;
    }

    private void vocInvalid(PlanInvalidRequest planInvalidRequest, PlanInvalidResponse response) {
        response.setPlanId(planInvalidRequest.getPlanId());
        RepaymentPlan repaymentPlan = repaymentPlanMapper.queryRepaymentPlan(planInvalidRequest.getPlanId());
        if (repaymentPlan == null) {
            throw new IgnoreException(TechplayErrDtlEnum.REPAY_PLAN_ERROR, "该方案编号不存在待生效/生效中方案");
        }

        List<RepaymentPlanDetail> repaymentPlanDetails = repaymentPlanDetailMapper.invalidPlanDetails(planInvalidRequest.getPlanId());
        List<String> planDetails = repaymentPlanDetails.stream().map(r -> r.getId().toString()).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(planDetails)) {
            throw new IgnoreException(TechplayErrDtlEnum.REPAY_PLAN_ERROR, "该方案编号中明细方案都已使用");
        }

        if (StringUtils.isNotEmpty(repaymentPlan.getRenderKey())) {
            try {
                ShortLinkCloseRequest request = new ShortLinkCloseRequest();
                request.setCustNo(repaymentPlan.getCustNo());
                request.setUserNo(repaymentPlan.getUserNo());
                request.setRequestId(getRequestNo(ReqNoConstants.VOCSLCLOSE, repaymentPlan.getId()));
                request.setRenderKey(repaymentPlan.getRenderKey());
                managementFacadeClient.linkClose(request);
            } catch (Exception e) {
                log.error("close shortLink failed", e);
            }
        }

//        List<String> loanNos = repaymentPlanDetails.stream().map(RepaymentPlanDetail::getLoanNo).collect(Collectors.toList());

        List<String> needPlanDetailId = new ArrayList<>();
        List<String> needLoanNos = new ArrayList<>();
        for (RepaymentPlanDetail repaymentPlanDetail : repaymentPlanDetails) {
            if (!repaymentPlanDetail.hasAllZeroReductions()) {
                needPlanDetailId.add(repaymentPlanDetail.getId().toString());
                needLoanNos.add(repaymentPlanDetail.getLoanNo());
            }
        }
        if (CollectionUtils.isNotEmpty(needPlanDetailId)) {
            repayFacadeClient.repaymentPlanInvalid(needPlanDetailId, needLoanNos, "客服主动失效");
        }
        repaymentPlan.setPlanStatus(2);
        repaymentPlanMapper.updateById(repaymentPlan);
        for (RepaymentPlanDetail repaymentPlanDetail : repaymentPlanDetails) {
            repaymentPlanDetail.setStatus(0);
            repaymentPlanDetailMapper.updateById(repaymentPlanDetail);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateRepaymentPlanStatus(List<Long> updateInfos, Integer status) {
        if (CollectionUtils.isNotEmpty(updateInfos)) {
            Map<String, Object> paramMap = new HashMap<>(2);
            paramMap.put("status", status);
            paramMap.put("list", updateInfos);
            repaymentPlanDetailMapper.batchUpdatePlanStatus(paramMap);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateDetail(QueryRepaymentPlanResultResponse.PlanDetailResult planDetailResult) {
        if (invalidData(planDetailResult)) {
            log.warn("updateDetail Invalid data，data:{}", JsonUtil.toJson(planDetailResult));
            return;
        }

        RepaymentPlanDetail rp = new RepaymentPlanDetail();
        rp.setId(Long.valueOf(planDetailResult.getPlanDetailId()));
        parseAndSetStatus(planDetailResult.getStatus()).ifPresent(rp::setStatus);
        parseAndSetStatus(planDetailResult.getUseStatus()).ifPresent(rp::setUseStatus);

        try {
            repaymentPlanDetailMapper.updateById(rp);
        } catch (Exception e) {
            log.error("Failed to update repayment plan detail，data:{},errmsg:{}", JsonUtil.toJson(planDetailResult), e.getMessage());
        }
    }


    /**
     * @param :
     * @return void
     * <AUTHOR>
     * @description 更新方案状态：1.当且仅当同一方案所有明细status = 1 AND use_status = 2 方案状态改为成功
     * 2.当且仅当同一方案所有明细status = 0 方案状态改为失败
     * 3.当同一方案里面明细为1，2情况的组合（1，2必须同时存在才更新）， 方案状态改为失败
     * 4.其他情况不更新
     * SELECT
     * rp.id AS plan_id,
     * CASE
     * WHEN subquery.used_rows = subquery.total_rows THEN 3
     * WHEN subquery.other_rows = subquery.total_rows THEN 2
     * ELSE rp.plan_status
     * END AS updated_plan_status
     * FROM
     * repayment_plan rp
     * JOIN (
     * SELECT
     * rpd.plan_id,
     * COUNT(*) AS total_rows,
     * SUM(CASE WHEN rpd.status = 1 AND rpd.use_status = 2 THEN 1 ELSE 0 END) AS used_rows,
     * SUM(CASE WHEN rpd.status = 0 OR (rpd.status = 1 AND rpd.use_status = 2) THEN 1 ELSE 0 END) AS other_rows
     * FROM
     * repayment_plan_detail rpd
     * JOIN
     * repayment_plan rp ON rpd.plan_id = rp.id
     * WHERE
     * rp.plan_status = 1 AND rp.is_del = 0
     * GROUP BY
     * rpd.plan_id
     * ) AS subquery ON rp.id = subquery.plan_id;
     * @date 2024/4/1 20:30
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateRepaymentAllStatus() {
        if (loginUserConfig.isUpdateBatch()) {
            log.info("isUpdateBatch come");
            repaymentPlanDetailMapper.updateRepaymentALL();
        }
    }

    /**
     * 对所有方案明细rpd.status = 1 AND rpd.use_status = 2的方案，置为成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateRepaymentSuccess() {
        List<Long> updateInfos = repaymentPlanDetailMapper.findPlanStatusUpdateInfo();
        if (CollectionUtils.isNotEmpty(updateInfos)) {
            updateRepaymentPlanStatus(updateInfos, 3);
        }
    }

    /**
     * 待生效---->失效
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updatePending() {
        repaymentPlanDetailMapper.updatePendingReview();
        repaymentPlanDetailMapper.updatePending();
    }

    @Override
    public List<RepaymentPlanDetail> queryInvalidData() {
        return new ArrayList<>(Stream.of(
                        //生效中的方案，方案有效期过期
                        repaymentPlanDetailMapper.getRepaymentInvalidDetailList(),
                        //生效中的方案，方案明细都为失效（status=0）
                        repaymentPlanDetailMapper.findPlanFailStatusUpdateInfo(),
                        repaymentPlanDetailMapper.findPlanIntermediate()
                )
                .flatMap(List::stream)
                .collect(Collectors.toMap(RepaymentPlanDetail::getId, e -> e, (existing, replacement) -> existing))
                .values());
    }

    @Override
    public List<DeductionLoansResponse> deductionLoans(DeductionLoansRequest request) {
        if (vocConfig.getIsNewFeeControl()) {
            List<String> whiteList = gson.fromJson(vocConfig.getUserIdentifyWhiteList(), typeListString);
            if (!whiteList.contains(UserContextHolder.getUserContext().getUserIdentify())) {
                return newDeductionLoans(request);
            }
        }

        List<DeductionLoansResponse> deductionResponses = new ArrayList<>();

        //获取可抵扣费项及月数
        List<FeeSubjectEnum> reductionFeeList = new ArrayList<>();
        Integer month = getDeductionLoansFee(request.getRepayType(), reductionFeeList);
        if (month == 0) {
            return deductionResponses;
        }

        //获取该用户下所有userNo
        PageResult<UserSearchDTO> custNoResult = cisFacadeClient.queryUserList(null, request.getCustNo(), null, 1, 30);
        if (CollectionUtils.isEmpty(custNoResult.getList())) {
            return deductionResponses;
        }
        List<String> userNos = custNoResult.getList().stream().map(UserSearchDTO::getUserNo).collect(Collectors.toList()).stream().map(Objects::toString).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(userNos)) {
            return deductionResponses;
        }

        //获取可抵扣订单信息
        LocalDateTime date = LocalDateTime.now().plusMonths(-month);
        CalculateReductionAmountResponse response = repayFacadeClient.calculateReductionAmount(userNos, reductionFeeList, LocalDateTimeUtils.parseDateByLocalDateTime(date));
        if (response == null || CollectionUtils.isEmpty(response.getResult())) {
            return deductionResponses;
        }
        for (ReductionInfoDto reductionInfo : response.getResult()) {
            DeductionLoansResponse deductionResponse = RepayConverter.INSTANCE.reductionInfoToDeductionResponse(reductionInfo);
            ManageOrderDetailDTO manageOrderDetailDTO = lendQueryClientService.getOrderNoByLoanNo(reductionInfo.getLoanNo());
            deductionResponse.setOrderNo(manageOrderDetailDTO.getLoanReqNo());
            deductionResponse.setDateCreated(manageOrderDetailDTO.getDateCreated());
            if (StringUtils.isNotEmpty(reductionInfo.getFundSource())) {
                deductionResponse.setOrderType("card_cash".equals(reductionInfo.getFundSource()) ? OrderType.PROFIT.getCode() : OrderType.MAIN.getCode());
            }
            deductionResponses.add(deductionResponse);
        }
        return deductionResponses;
    }

    public List<DeductionLoansResponse> newDeductionLoans(DeductionLoansRequest request) {
        StrategyExecutionResult<FeeStrategyConfig> strategyExecutionResult = fromDeductionStrategyServiceImpl.executeStrategy(FeeStrategyEnum.DEDUCTIONS_FROM, EmptyStrategyInput.builder().build());
        Map<String, String> result = strategyExecutionResult.getResult();

        List<ReductionInfoDto> reductionInfoDtos = getDeductionLoans(request, result);

        List<DeductionLoansResponse> deductionResponses = new ArrayList<>();
        for (ReductionInfoDto reductionInfo : reductionInfoDtos) {
            DeductionLoansResponse deductionResponse = RepayConverter.INSTANCE.reductionInfoToDeductionResponse(reductionInfo);
            ManageOrderDetailDTO manageOrderDetailDTO = lendQueryClientService.getOrderNoByLoanNo(reductionInfo.getLoanNo());
            deductionResponse.setOrderNo(manageOrderDetailDTO.getLoanReqNo());
            deductionResponse.setDateCreated(manageOrderDetailDTO.getDateCreated());
            if (StringUtils.isNotEmpty(reductionInfo.getFundSource())) {
                deductionResponse.setOrderType("card_cash".equals(reductionInfo.getFundSource()) ? OrderType.PROFIT.getCode() : OrderType.MAIN.getCode());
                //营收订单From默认全可抵扣，不参与费控计算
                if (deductionResponse.getOrderType().equals(OrderType.PROFIT.getCode())) {
                    deductionResponses.add(deductionResponse);
                    continue;
                }
            }

            BigDecimal canReductionAmt = new BigDecimal("0");
            FeeAmountDto feeAmountDto = reductionInfo.getCanReductionAmtDetail();
            if (feeAmountDto == null) {
                throw new TechplayException(TechplayErrDtlEnum.FEE_LIMIT_ERROR, "From订单可抵扣金额获取失败");
            }
            canReductionAmt = addReduction(canReductionAmt, feeAmountDto.getTransPrin(), result, "Out_FromDeduction_Principal");
            canReductionAmt = addReduction(canReductionAmt, feeAmountDto.getTransInt(), result, "Out_FromDeduction_intAmt");
            canReductionAmt = addReduction(canReductionAmt, feeAmountDto.getTransFee1(), result, "Out_FromDeduction_Fee1Amt");
            canReductionAmt = addReduction(canReductionAmt, feeAmountDto.getTransFee2(), result, "Out_FromDeduction_Fee2Amt");
            canReductionAmt = addReduction(canReductionAmt, feeAmountDto.getTransFee3(), result, "Out_FromDeduction_Fee3Amt");
            canReductionAmt = addReduction(canReductionAmt, feeAmountDto.getTransFee4(), result, "Out_FromDeduction_Settle_Fee");
            canReductionAmt = addReduction(canReductionAmt, feeAmountDto.getTransOint(), result, "Out_FromDeduction_OintAmt");
            canReductionAmt = addReduction(canReductionAmt, feeAmountDto.getTransFee6(), result, "Out_FromDeduction_Fee6Amt");

            deductionResponse.setCanReductionAmt(canReductionAmt);
            deductionResponses.add(deductionResponse);
        }
        return deductionResponses;
    }

    private BigDecimal addReduction(BigDecimal canReductionAmt, BigDecimal amount, Map<String, String> result, String key) {
        String valueStr = result.get(key);
        if (valueStr == null) {
            throw new TechplayException(TechplayErrDtlEnum.FEE_LIMIT_ERROR, "抵扣From订单出参：Out_FromDeduction_Settle_Fee 值为空");
        }

        BigDecimal value;
        try {
            value = new BigDecimal(valueStr);
        } catch (NumberFormatException e) {
            throw new TechplayException(TechplayErrDtlEnum.FEE_LIMIT_ERROR, "抵扣From订单出参：Out_FromDeduction_Settle_Fee 值不是有效数字");
        }

        BigDecimal reductionAmount = amount.multiply(value).multiply(new BigDecimal("0.01"));
        return canReductionAmt.add(reductionAmount);
    }

    private List<ReductionInfoDto> getDeductionLoans(DeductionLoansRequest request, Map<String, String> result) {
        //获取可抵扣月数
        int month = Integer.parseInt(result.get("Out_FromDeduction_month"));
        LocalDateTime date = LocalDateTime.now().plusMonths(-month);

        //获取可抵扣费项
        List<FeeSubjectEnum> reductionFeeList = new ArrayList<>();
        reductionFeeList.add(FeeSubjectEnum.PRIN_AMT);
        reductionFeeList.add(FeeSubjectEnum.INT_AMT);
        reductionFeeList.add(FeeSubjectEnum.FEE1);
        reductionFeeList.add(FeeSubjectEnum.FEE2);
        reductionFeeList.add(FeeSubjectEnum.OINT_AMT);
        reductionFeeList.add(FeeSubjectEnum.FEE3);
        reductionFeeList.add(FeeSubjectEnum.FEE6);
        reductionFeeList.add(FeeSubjectEnum.FEE4);

        //获取该用户下所有userNo
        PageResult<UserSearchDTO> custNoResult = cisFacadeClient.queryUserList(null, request.getCustNo(), null, 1, 30);
        if (CollectionUtils.isEmpty(custNoResult.getList())) {
            return new ArrayList<>();
        }
        List<String> userNos = custNoResult.getList().stream().map(UserSearchDTO::getUserNo).collect(Collectors.toList()).stream().map(Objects::toString).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(userNos)) {
            return new ArrayList<>();
        }

        //获取可抵扣订单信息
        CalculateReductionAmountResponse response = repayFacadeClient.calculateReductionAmount(userNos, reductionFeeList, LocalDateTimeUtils.parseDateByLocalDateTime(date));
        if (response == null || CollectionUtils.isEmpty(response.getResult())) {
            return new ArrayList<>();
        }
        return response.getResult();
    }

    private Integer getDeductionLoansFee(Integer repayType, List<FeeSubjectEnum> reductionFeeList) {
        List<ControlData> controlDataList = UserContextHolder.getUserContext().getControlDataList();
        //过滤出抵扣费项及对应还款类型的费控配置信息
        controlDataList = controlDataList.stream().filter(r -> r.getControlScene() == 2 && repayType.equals(r.getControlType())).collect(Collectors.toList());

        Integer month = 0;
        try {
            for (ControlData controlData : controlDataList) {
                ControlEnum controlEnum = ControlEnum.getByControlChildType(controlData.getControlChildType());
                switch (controlEnum) {
                    case GUARANTEE_REDUCTION:
                        reductionFeeList.add(FeeSubjectEnum.FEE1);
                        reductionFeeList.add(FeeSubjectEnum.FEE2);
                        break;
                    case PENALTY_REDUCTION:
                        reductionFeeList.add(FeeSubjectEnum.OINT_AMT);
                        reductionFeeList.add(FeeSubjectEnum.FEE3);
                        reductionFeeList.add(FeeSubjectEnum.FEE6);
                        break;
                    case PREPAYMENT_FEE:
                        reductionFeeList.add(FeeSubjectEnum.FEE4);
                        break;
                    case DEDUCTION_POOL_RANGE:
                        ControlItemValue<?, ?, ?> values = factory.create(controlData.getControlValue(), controlEnum);
                        month = (Integer) values.getO();
                        break;
                    default:
                        break;
                }
            }
        } catch (Exception e) {
            log.error(LogUtil.clientErrorLog("RepayServiceImp", "deductionLoans", null, null, "可抵扣费项/时间获取错误"), e);
            throw new TechplayException(TechplayErrDtlEnum.DEDUCTION_ERROR, "可抵扣费项/时间获取错误");
        }
        return month;
    }

    public void deductionAmt(BigDecimal calTotalAmt, CalculateFee calculateFee, LoanPlanDto loanPlanDto) {
        //获取历史抵扣金额
        ReductionAmountResponse response = repayTradeClient.reductionAmount(loanPlanDto.getLoanNo());
        List<ControlData> controlDataList = UserContextHolder.getUserContext().getControlDataList();
        controlDataList = controlDataList.stream().filter(r -> r.getControlScene() == 2 && r.getControlChildType() == 6).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(controlDataList)) {
            return;
        }
        ControlData controlData = controlDataList.get(0);
        ControlEnum type = ControlEnum.getByControlChildType(controlData.getControlChildType());
        try {
            ControlItemValue<?, ?, ?> values = factory.create(controlData.getControlValue(), type);
            ControlRes<?, ?> result = type.calculateAmount(values, calTotalAmt, loanPlanDto.getLoanFeeDetail().getInitPlan().getSumAmt(), response.getReductionTotalAmount());
            calculateFee.setAmountLower((BigDecimal) result.getLeft());
            calculateFee.setAmountUpper((BigDecimal) result.getRight());
        } catch (Exception e) {
            log.error(LogUtil.clientErrorLog("RepayServiceImp", "deductionAmt", null, null, "可抵扣金额范围获取错误"), e);
            throw new TechplayException(TechplayErrDtlEnum.DEDUCTION_ERROR, "可抵扣金额范围获取错误");
        }
    }

    public void deductionAmtNew(CalculateFee calculateFee, LoanPlanDto loanPlanDto, RepayLoanCalcResponse calResponse, RepaymentProcessReq req) {
        ToDeductionStrategyInput input = ToDeductionStrategyInput
                .builder()
                .loanNo(loanPlanDto.getLoanNo())
                .repaymentProcessReq(req)
                .build();
        StrategyExecutionResult<FeeStrategyConfig> strategyExecutionResult = toDeductionStrategyServiceImpl.executeStrategy(FeeStrategyEnum.DEDUCTIONS_TO, input);
        Map<String, String> result = strategyExecutionResult.getResult();
        //总可被抵扣金额
        BigDecimal totalDeduction = new BigDecimal("0");
        BigDecimal valueBigDecimal;
        for (Map.Entry<String, String> entry : result.entrySet()) {
            String key = entry.getKey();
            String value = entry.getValue();
            switch (key) {
                case "Out_ToDeduction_Principal":
                    valueBigDecimal = new BigDecimal(value);
                    valueBigDecimal = valueBigDecimal.multiply(new BigDecimal("0.01"));
                    totalDeduction = totalDeduction.add(valueBigDecimal.multiply(loanPlanDto.getLoanFeeDetail().getInitPlan().getPrinAmt()).setScale(2, RoundingMode.HALF_UP));
                    break;
                case "Out_ToDeduction_intAmt":
                    valueBigDecimal = new BigDecimal(value);
                    valueBigDecimal = valueBigDecimal.multiply(new BigDecimal("0.01"));
                    totalDeduction = totalDeduction.add(valueBigDecimal.multiply(loanPlanDto.getLoanFeeDetail().getInitPlan().getIntAmt()).setScale(2, RoundingMode.HALF_UP));
                    break;
                case "Out_ToDeduction_Fee1Amt":
                    valueBigDecimal = new BigDecimal(value);
                    valueBigDecimal = valueBigDecimal.multiply(new BigDecimal("0.01"));
                    totalDeduction = totalDeduction.add(valueBigDecimal.multiply(loanPlanDto.getLoanFeeDetail().getInitPlan().getFee1Amt()).setScale(2, RoundingMode.HALF_UP));
                    break;
                case "Out_ToDeduction_Fee2Amt":
                    valueBigDecimal = new BigDecimal(value);
                    valueBigDecimal = valueBigDecimal.multiply(new BigDecimal("0.01"));
                    totalDeduction = totalDeduction.add(valueBigDecimal.multiply(loanPlanDto.getLoanFeeDetail().getInitPlan().getFee2Amt()).setScale(2, RoundingMode.HALF_UP));
                    break;
                case "Out_ToDeduction_Settle_Fee":
                    valueBigDecimal = new BigDecimal(value);
                    valueBigDecimal = valueBigDecimal.multiply(new BigDecimal("0.01"));
                    totalDeduction = totalDeduction.add(valueBigDecimal.multiply(loanPlanDto.getLoanFeeDetail().getInitPlan().getFee4Amt()).setScale(2, RoundingMode.HALF_UP));
                    break;
                case "Out_ToDeduction_Fee6Amt":
                    valueBigDecimal = new BigDecimal(value);
                    valueBigDecimal = valueBigDecimal.multiply(new BigDecimal("0.01"));
                    totalDeduction = totalDeduction.add(valueBigDecimal.multiply(loanPlanDto.getLoanFeeDetail().getInitPlan().getFee6Amt()).setScale(2, RoundingMode.HALF_UP));
                    break;
                case "Out_ToDeduction_OintAmt":
                    valueBigDecimal = new BigDecimal(value);
                    valueBigDecimal = valueBigDecimal.multiply(new BigDecimal("0.01"));
                    totalDeduction = totalDeduction.add(valueBigDecimal.multiply(loanPlanDto.getLoanFeeDetail().getInitPlan().getOintAmt()).setScale(2, RoundingMode.HALF_UP));
                    break;
                case "Out_ToDeduction_Fee3Amt":
                    valueBigDecimal = new BigDecimal(value);
                    valueBigDecimal = valueBigDecimal.multiply(new BigDecimal("0.01"));
                    totalDeduction = totalDeduction.add(valueBigDecimal.multiply(loanPlanDto.getLoanFeeDetail().getInitPlan().getFee3Amt()).setScale(2, RoundingMode.HALF_UP));
                    break;
                default:
                    break;
            }
        }
        log.info(LogUtil.infoLog("总可抵扣金额", loanPlanDto.getLoanNo(), totalDeduction));

        //获取该订单历史被抵扣金额
        ReductionAmountResponse response = repayTradeClient.reductionAmount(loanPlanDto.getLoanNo());
        log.info(LogUtil.infoLog("历史被抵扣金额", loanPlanDto.getLoanNo(), response.getReductionTotalAmount()));
        totalDeduction = totalDeduction.subtract(response.getReductionTotalAmount());
        if (totalDeduction.compareTo(new BigDecimal(0)) < 0) {
            totalDeduction = new BigDecimal(0);
        }
        log.info(LogUtil.infoLog("抵扣试算应还金额", loanPlanDto.getLoanNo(), calculateFee.getCalTotalAmt()));
        calculateFee.setAmountLower(new BigDecimal(0));
        calculateFee.setAmountUpper(totalDeduction.compareTo(calculateFee.getCalTotalAmt()) > 0 ? calculateFee.getCalTotalAmt() : totalDeduction);
        calculateFee.setAmountUpper(calculateFee.getAmountUpper().compareTo(calResponse.getCanDeductAmtDetail().sumAmt()) > 0 ? calResponse.getCanDeductAmtDetail().sumAmt() : calculateFee.getAmountUpper());
    }

    @Override
    public DeductionAmtResponse deductionAmt(DeductionAmtRequest req) {
        DeductionAmtResponse resp = new DeductionAmtResponse();
        List<ControlData> controlDataList = UserContextHolder.getUserContext().getControlDataList();
        controlDataList = controlDataList.stream().filter(r -> r.getControlScene() == 2 && r.getControlChildType() == 6).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(controlDataList)) {
            return resp;
        }
        ControlData controlData = controlDataList.get(0);
        ControlEnum type = ControlEnum.getByControlChildType(controlData.getControlChildType());
        try {
            ControlItemValue<?, ?, ?> values = factory.create(controlData.getControlValue(), type);
            ControlRes<?, ?> result = type.calculateAmount(values, req.getAmount());
            resp.setAmountLower((BigDecimal) result.getLeft());
            resp.setAmountUpper((BigDecimal) result.getRight());
        } catch (Exception e) {
            log.error(LogUtil.clientErrorLog("RepayServiceImp", "deductionAmt", null, null, "可抵扣金额范围获取错误"), e);
            throw new TechplayException(TechplayErrDtlEnum.DEDUCTION_ERROR, "可抵扣金额范围获取错误");
        }
        return resp;
    }

    @Override
    public PageResultResponse<BankFlow> bankFlow(BankFlowRequest request) {
        PageResultResponse<BankFlow> pageResultResponse = new PageResultResponse<>();
        pageResultResponse.setPageSize(request.getPageSize());
        pageResultResponse.setCurrentPage(request.getCurrentPage());

        RefundRecordQueryReq refundRecordQueryReq = RepayConverter.INSTANCE.bankFlowRequestToRefundRecordQuery(request);
        refundRecordQueryReq.setSize(request.getPageSize());
        refundRecordQueryReq.setCurrent(request.getCurrentPage());
        refundRecordQueryReq.setStartAmount(refundRecordQueryReq.getStartAmount() == null ? null :
                new BigDecimal(refundRecordQueryReq.getStartAmount()).multiply(new BigDecimal("100")).longValue());
        refundRecordQueryReq.setEndAmount(refundRecordQueryReq.getEndAmount() == null ? null :
                new BigDecimal(refundRecordQueryReq.getEndAmount()).multiply(new BigDecimal("100")).longValue());


        RefundRecordResult result = cstStartService.getBankFlow(refundRecordQueryReq);
        if (result != null && CollectionUtils.isNotEmpty(result.getDataList())) {
            pageResultResponse.setPageSize(result.getPageSize());
            pageResultResponse.setCurrentPage(result.getPageNumber());
            pageResultResponse.setTotalPage(result.getTotal());
            pageResultResponse.setTotal(result.getTotal());
            List<BankFlow> bankFlows = new ArrayList<>();
            for (RefundRecordQueryRes res : result.getDataList()) {
                BankFlow bankFlow = RepayConverter.INSTANCE.refundRecordToBankFlow(res);
                bankFlows.add(bankFlow);
            }
            pageResultResponse.setList(bankFlows);
        }

        return pageResultResponse;
    }

    private boolean invalidData(QueryRepaymentPlanResultResponse.PlanDetailResult planDetailResult) {
        return StringUtils.isBlank(planDetailResult.getPlanDetailId()) ||
                (StringUtils.isBlank(planDetailResult.getStatus()) && StringUtils.isBlank(planDetailResult.getUseStatus())) ||
                (Objects.equals("1", planDetailResult.getStatus()) && Objects.equals("0", planDetailResult.getUseStatus()));
    }

    private Optional<Integer> parseAndSetStatus(String status) {
        return Optional.ofNullable(status).map(Integer::parseInt);
    }

    public Paging<RepayReductionDto> deductionList(RepayTradeDeductionRequest request) {
        Paging<RepayReductionDto> listPaging = new Paging<>();
        DetailListResponse detailListResponse = repayTradeClient.deductionDetailList(request.getLoanNo(), request.getStatus());
        if (CollectionUtils.isEmpty(detailListResponse.getReductionDetailList())) {
            return listPaging;
        }
        List<ReductionDetailDto> list = detailListResponse.getReductionDetailList();
        listPaging.setTotal(list.size());
        list = list.stream()
                .skip((long) (request.getCurrentPage() - 1) * request.getPageSize())
                .limit(request.getPageSize()).
                collect(Collectors.toList());
        listPaging.setCurrentPage(request.getCurrentPage());
        listPaging.setPageSize(request.getPageSize());
        listPaging.setList(getDeductionDetailList(list));
        return listPaging;
    }

    public Boolean deductionCancel(RepayTradeDeductionCancelRequest request) {
        return repayTradeClient.deductionDetailCancel(request.getRepaymentNo(), request.getUpdatedBy());
    }

    private List<RepayReductionDto> getDeductionDetailList(List<ReductionDetailDto> list) {
        List<RepayReductionDto> reductionDto = new ArrayList<>();
        for (ReductionDetailDto reductionDetailDto : list) {
            RepayReductionDto repayReductionDto = RepayTradeConverter.INSTANCE.ReductionDetailDtoToRepayReductionDto(reductionDetailDto);
            //担保费=抵扣贷后管理费+平台服务费
            BigDecimal guaranteeFee = reductionDetailDto.getReductionFee1().add(reductionDetailDto.getReductionFee2());
            //逾期费用=抵扣罚息+贷后逾期管理费+催费
            BigDecimal overdueFee = reductionDetailDto.getReductionOint().add(reductionDetailDto.getReductionFee3()).add(reductionDetailDto.getReductionFee6());
            repayReductionDto.setGuaranteeFee(guaranteeFee);
            repayReductionDto.setOverdueFee(overdueFee);
            reductionDto.add(repayReductionDto);
        }
        return reductionDto;
    }

    public Boolean updateRepayMethod(RepayMethodRequest request) {
        isRepaying(request);
        //方案来源是否为客服渠道
        Boolean isVocMng = PlanSourceEnum.VOCMNG.getCode().equals(request.getPlanSource());
        RepaymentPlan repaymentPlan = null;
        if (isVocMng) {
            repaymentPlan = repaymentPlanMapper.queryEffectRepayPlan(request.getPlanId());
            if (StringUtils.isNotEmpty(request.getMobile())) {
                String encodeMobile = cisFacadeClientService.getEncodeMobileLocal(request.getMobile());
                repaymentPlan.setMobile(encodeMobile);
                repaymentPlanMapper.updateById(repaymentPlan);
            }

            if (StringUtils.isNotEmpty(request.getBankCardId())) {
                repaymentPlan.setBankCardId(request.getBankCardId());
                repaymentPlanMapper.updateById(repaymentPlan);
            }

            Map<String, Object> paramMap = new HashMap<>(2);
            paramMap.put("repayMethod", request.getRepayMethod());
            paramMap.put("planId", request.getPlanId());
            repaymentPlanDetailMapper.updateRepayMethod(paramMap);
        }

        switch (request.getRepayMethod()) {
            case 1:
                //app自助
                break;
            case 2:
                //系统代扣
                handleSystemDeduction(request, isVocMng);
                break;
            case 3:
                //聚合支付
                ShortLinkCreateRequest shortLinkCreateRequest;
                BigDecimal repaymentAmount;
                List<RepayMethodDetail> repayMethodDetails = request.getRepayMethodDetails();
                repaymentAmount = repayMethodDetails.stream()
                        .map(RepayMethodDetail::getRepaymentAmount)
                        .filter(Objects::nonNull)
                        .reduce(BigDecimal.ZERO, BigDecimal::add);
                shortLinkCreateRequest = getShortLinkByLoans(request, repayMethodDetails, repaymentPlan);
                //app收银台 调用
                whitelistMark(null, request);
                sendAggregateSms(request.getMobile(), shortLinkCreateRequest, repaymentAmount, repaymentPlan, request.getIsSendSms(), isVocMng, request.getEndTime());
                break;
            case 4:
                //线下还款汇总求和
                BigDecimal totalAmount = request.getRepayMethodDetails().stream().map(RepayMethodDetail::getRepaymentAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
                offlineRepayment(request.getMobile(), request.getUserNo(), totalAmount, request.getIsSendSms());
                break;
            default:
                return false;
        }
        return true;
    }

    private void handleSystemDeduction(RepayMethodRequest request, Boolean isVocMng) {
        for (RepayMethodDetail repayMethodDetail : request.getRepayMethodDetails()) {
            RepayApplyRequest repayApplyRequest = new RepayApplyRequest();
            repayApplyRequest.setLoanNo(repayMethodDetail.getLoanNo());
            repayApplyRequest.setAmount(repayMethodDetail.getRepaymentAmount());
            repayApplyRequest.setSettleType(request.getPlanType() == 1 ? RepayLoanCalcRequest.CalcSettleTypeEnum.NO_SETTLE : RepayLoanCalcRequest.CalcSettleTypeEnum.SETTLE);
            repayApplyRequest.setBankCardId(request.getBankCardId());
            repayApplyRequest.setCreatedBy(employeeService.getUserNameForIdentify(UserContextHolder.getUserIdentify()));
            repayApplyRequest.setRequestNo(isVocMng ? getRequestNo(ReqNoConstants.VOCSRP, request.getPlanId()) : getRequestNo(ReqNoConstants.VOCSRP, request.getPlanDetailId()));
            if (request.getPlanType() == 1) {
                List<PlanNoAndTermsDto> planInfo = new ArrayList<>();
                for (String term : repayMethodDetail.getTerms()) {
                    PlanNoAndTermsDto planNoAndTermsDto = new PlanNoAndTermsDto();
                    planNoAndTermsDto.setTerm(term);
                    planInfo.add(planNoAndTermsDto);
                }
                repayApplyRequest.setPlanInfo(planInfo);
            }
            repayFacadeClient.repayApply(repayApplyRequest);
        }
    }

    private void isRepaying(RepayMethodRequest request) {
        if (request.getRepayMethod() == 3 && request.getRepayMethodDetails().size() > 1 && !isBatchLoanLink) {
            throw new IgnoreException(TechplayErrDtlEnum.REPAY_PLAN_ERROR, "聚合支付暂时只支持单订单");
        }

        List<String> loans = request.getRepayMethodDetails().stream().map(RepayMethodDetail::getLoanNo).collect(Collectors.toList());
        QueryLoansAreRepayingResponse queryLoansAreRepayingResponse = repayFacadeClient.queryLoansAreRepaying(loans);
        if (queryLoansAreRepayingResponse != null && CollectionUtils.isNotEmpty(queryLoansAreRepayingResponse.getLoanNosRepaying())) {
            if (queryLoansAreRepayingResponse.getLoanNosRepaying().stream().anyMatch(QueryLoansAreRepayingResponse.QueryLoansAreRepaying::getIsRepaying)) {
                throw new IgnoreException(TechplayErrDtlEnum.REPAY_ERROR, "当前方案存在还款中订单，无法修改还款方式!");
            }
        }
    }

    @Override
    public ReduceCalculateResponse reduceCalculate(ReduceCalculateReq request) {
        ReduceCalculateResponse response = new ReduceCalculateResponse();
        response.setRealAmt(request.getRealAmt());
        response.setLoanNo(request.getLoanNo());
        BigDecimal reduceTotalAmt = request.getOldRealAmt().subtract(request.getRealAmt());
        BigDecimal mixAmt = safeAdd(request.getOverdueFee())
                .add(safeAdd(request.getRealAdvSettFee()))
                .add(safeAdd(request.getGuaranteeFee()))
                .add(safeAdd(request.getTransInt()))
                .add(safeAdd(request.getPrinAmt()));

        if (reduceTotalAmt.compareTo(mixAmt) > 0) {
            response.setIsExceed(true);
            reduceTotalAmt = mixAmt;
        }

        BigDecimal[] amounts = {
                request.getOverdueFee(),
                request.getRealAdvSettFee(),
                request.getGuaranteeFee(),
                request.getTransInt(),
                request.getPrinAmt()
        };

        for (int i = 0; i < amounts.length; i++) {
            if (amounts[i] == null) {
                continue;
            }
            if (reduceTotalAmt.compareTo(amounts[i]) >= 0) {
                reduceTotalAmt = reduceTotalAmt.subtract(amounts[i]);
            } else {
                amounts[i] = reduceTotalAmt;
                reduceTotalAmt = BigDecimal.ZERO;
            }
        }
        response.setOverdueFee(amounts[0]);
        response.setRealAdvSettFee(amounts[1]);
        response.setGuaranteeFee(amounts[2]);
        response.setTransInt(amounts[3]);
        response.setPrinAmt(amounts[4]);
        return response;
    }


    @Override
    public List<AdvanceOrderListOuterDTO.AdvanceOrderInfoOuterDTO> queryAdvanceOrderList(AdvanceOrderListReq req) {
        List<AdvanceOrderListOuterDTO.AdvanceOrderInfoOuterDTO> queryAdvanceOrderList = outerApiFacadeClient.queryAdvanceOrderList(String.valueOf(req.getUserNo()));
        if (CollectionUtils.isNotEmpty(queryAdvanceOrderList)) {
            for (AdvanceOrderListOuterDTO.AdvanceOrderInfoOuterDTO orderInfoOuterDTO : queryAdvanceOrderList) {
                if (StringUtils.isNotBlank(orderInfoOuterDTO.getOrderNo())) {
                    ManageOrderListRequest request = new ManageOrderListRequest();
                    request.setOrderNos(Collections.singletonList(orderInfoOuterDTO.getRelateOrderNo()));
                    request.setPageSize(1);
                    com.xinfei.lendtrade.facade.rr.dto.Page<ManageOrderDetailDTO> manageOrderDetailDTOPage = lendQueryFacadeClient.getOrderList(request);
                    if (manageOrderDetailDTOPage != null && CollectionUtils.isNotEmpty(manageOrderDetailDTOPage.getPageList())) {
                        orderInfoOuterDTO.setRelateOrderNo(manageOrderDetailDTOPage.getPageList().get(0).getLoanReqNo());
                    }
                }
            }
        }
        queryAdvanceOrderList.sort((o1, o2) -> o2.getCreatedTime().compareTo(o1.getCreatedTime()));
        return queryAdvanceOrderList;
    }

    public List<ReduceCalculateResponse> reduceCalculateMany(List<ReduceCalculateReq> request) {
        List<ReduceCalculateResponse> list = new ArrayList<>();
        BigDecimal realAmtSum = request.stream()
                .map(ReduceCalculateReq::getRealAmt)
                .filter(realAmt -> realAmt != null)
                .reduce(BigDecimal.ZERO, BigDecimal::add).subtract(request.get(0).getRealAmtSum());
        for (ReduceCalculateReq req : request) {
            if (realAmtSum.compareTo(req.getRealAmt()) >= 0) {
                realAmtSum = realAmtSum.subtract(req.getRealAmt());
                req.setRealAmt(BigDecimal.ZERO);
            } else {
                req.setRealAmt(req.getRealAmt().subtract(realAmtSum));
                realAmtSum = BigDecimal.ZERO;
            }
            list.add(reduceCalculate(req));
        }
        return list;
    }

    public List<ReduceCalculateVerticalResponse> reduceCalculateVertical(List<ReduceCalculateVerticalReq> request) {
        List<ReduceCalculateVerticalResponse> list = new ArrayList<>();
        if (CollectionUtils.isEmpty(request)) {
            return list;
        }

        BigDecimal sumAmt = request.get(0).getSumAmt();
        for (ReduceCalculateVerticalReq req : request) {
            ReduceCalculateVerticalResponse response = new ReduceCalculateVerticalResponse();
            response.setLoanNo(req.getLoanNo());
            if (sumAmt.compareTo(req.getMixAmt()) > 0) {
                sumAmt = sumAmt.subtract(req.getMixAmt());
                response.setAmt(req.getMixAmt());
            } else {
                response.setAmt(sumAmt);
                sumAmt = BigDecimal.ZERO;
            }
            list.add(response);
        }
        return list;
    }

    private BigDecimal safeAdd(BigDecimal value) {
        return value == null ? BigDecimal.ZERO : value;
    }
    private void buyBackCheck(LoanInfo loanInfo, LoanCalculateDto loanCalculateDto) {
        List<String> sourcList = gson.fromJson(vocConfig.getBuyBackFundSource(), typeListString);
        if (sourcList.contains(loanInfo.getFundSource())) {
            loanCalculateDto.setIsBuyBack(false);
            GetBillListRequest getBillListRequest = new GetBillListRequest();
            getBillListRequest.setLoanNos(Collections.singletonList(loanInfo.getLoanNo()));
            List<LoanPlanDto> loanPlans = loanService.queryBillList(getBillListRequest);
            if (CollectionUtils.isNotEmpty(loanPlans) && CollectionUtils.isNotEmpty(loanCalculateDto.getTerms())
                    && CollectionUtils.isNotEmpty(loanPlans.get(0).getPlanList())) {
                List<PlanDto> filteredList = loanPlans.get(0).getPlanList().stream()
                        .filter(plan -> !"2".equals(plan.getRpyFlag()))             // 剔除 rpyFlag!="2"本期结清
                        .filter(plan -> "0".equals(plan.getCompensatedType()))      // 筛选 compensatedType="0" 未回购
                        .collect(Collectors.toList());

                // 获取 loanPlans.get(0).getPlanList() 里的所有 term
                Set<String> existingTerms = filteredList.stream()
                        .map(PlanDto::getTerm)
                        .map(String::valueOf)
                        .collect(Collectors.toSet());
                Integer totalTerm = loanCalculateDto.getTerms().size();
                //剔除 existingTerms 这个集合里出现过的元素
                loanCalculateDto.setTerms(loanCalculateDto.getTerms().stream()
                        .filter(term -> !existingTerms.contains(term))
                        .collect(Collectors.toList()));
                if (totalTerm != loanCalculateDto.getTerms().size()) {
                    loanCalculateDto.setIsBuyBack(true);
                }
            }
        }
    }
}