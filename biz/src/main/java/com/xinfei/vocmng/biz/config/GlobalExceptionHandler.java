/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2023 All Rights Reserved.
 */
package com.xinfei.vocmng.biz.config;

/**
 * <AUTHOR>
 * @version $ GlobalExceptionHandler, v 0.1 2023/12/24 19:45 wancheng.qu Exp $
 */

import com.xinfei.vocmng.biz.model.exception.IgnoreException;
import com.xinfei.vocmng.biz.model.exception.TechplayException;
import com.xinfei.vocmng.biz.model.resp.ApiResponse;
import com.xinfei.vocmng.itl.client.exeception.ClientException;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.FieldError;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseStatus;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@ControllerAdvice
public class GlobalExceptionHandler {

    @ExceptionHandler(TechplayException.class)
    @ResponseStatus(HttpStatus.INTERNAL_SERVER_ERROR)
    public ResponseEntity<ApiResponse<String>> handleTechplayException(TechplayException ex) {
        ApiResponse<String> responseDTO = new ApiResponse<>(ex.getResultCodeEnum(), ex.getMsg());
        return new ResponseEntity<>(responseDTO, HttpStatus.OK);
    }

    @ExceptionHandler(IgnoreException.class)
    @ResponseStatus(HttpStatus.INTERNAL_SERVER_ERROR)
    public ResponseEntity<ApiResponse<String>> handleIgnoreException(IgnoreException ex) {
        ApiResponse<String> responseDTO = new ApiResponse<>(ex.getResultCodeEnum(), ex.getMsg());
        return new ResponseEntity<>(responseDTO, HttpStatus.OK);
    }

    @ExceptionHandler(ClientException.class)
    @ResponseStatus(HttpStatus.INTERNAL_SERVER_ERROR)
    public ResponseEntity<ApiResponse<String>> handleClientException(ClientException ex) {
        ApiResponse<String> responseDTO = new ApiResponse<>(ex.getErrDtlEnum(), ex.getMessage());
        return new ResponseEntity<>(responseDTO, HttpStatus.OK);
    }

    @ExceptionHandler(MethodArgumentNotValidException.class)
    @ResponseStatus(HttpStatus.INTERNAL_SERVER_ERROR)
    public ResponseEntity<ApiResponse<String>> handleValidationExceptions(MethodArgumentNotValidException ex){
        List<String> errorMsgList = new ArrayList<>();
        ex.getBindingResult().getAllErrors().forEach((error)->{
            errorMsgList.add(error.getDefaultMessage());
        });

        ApiResponse<String> response = ApiResponse.paramIllegal(StringUtils.join(errorMsgList));
        return new ResponseEntity<>(response,HttpStatus.OK);
    }
}
