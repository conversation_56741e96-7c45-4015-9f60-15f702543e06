/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xinfei.vocmng.biz.model.req;

import lombok.Data;

import java.io.Serializable;

/**
 * 名单推送
 * <AUTHOR>
 * @version $ ListEntryReq, v 0.1 2024/5/14 10:18 wancheng.qu Exp $
 */
@Data
public class ListEntryReq implements Serializable {
    private String listType;       // 名单类型
    private String listDetail;     // 名单内容
    private String mainLabelKey="underground_industry";   // 主标签key
    private String subLabelKey="underground_industry01";    // 子标签key
    private String dateStart;        // 生效时间，不传或""为最小时间
    private String dateEnd;          // 失效时间，不传或""为最大时间
    private String operator="陆泽彧";       // 创建人
    private String department="产品部";     // 部门
    private String orgId="12";          // 组织ID
    private String remark;         // 备注

    public ListEntryReq() {
    }

    public ListEntryReq(String listType, String listDetail) {
        this.listDetail = listDetail;
        this.listType = listType;
    }
}