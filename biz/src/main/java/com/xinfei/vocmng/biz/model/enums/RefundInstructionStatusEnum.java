package com.xinfei.vocmng.biz.model.enums;

import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
@Getter
public enum RefundInstructionStatusEnum {
    PENDING("PENDING", "待发起", "退款中"),
    PROCESSING("PROCESSING", "退款中", "退款中"),
    SUCCESS("SUCCESS", "成功", "退款成功"),
    FAILURE("FAILURE", "失败", "退款失败"),
    CANCEL("CANCEL", "取消", "已撤销"),
    REJECT("REJECT", "拒绝", "退款失败");

    //退款明细全部成功才成功,失败>退款中>已撤销

    private final String code;

    private final String description;

    private final String orderDescription;

    RefundInstructionStatusEnum(String code, String description, String orderDescription) {
        this.code = code;
        this.description = description;
        this.orderDescription = orderDescription;
    }

    public static Boolean isFailed(String code) {
        if (StringUtils.isEmpty(code)) {
            return Boolean.FALSE;
        }

        if (FAILURE.getCode().equals(code) || REJECT.getCode().equals(code)) {
            return Boolean.TRUE;
        }

        return Boolean.FALSE;
    }


}
