/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2023 All Rights Reserved.
 */
package com.xinfei.vocmng.biz.util;

import com.xinfei.vocmng.biz.model.resp.UserInfo;
import org.apache.commons.lang3.StringUtils;

import java.util.Objects;

/**
 *
 * <AUTHOR>
 * @version $ UserContextHolder, v 0.1 2023/12/23 16:30 wancheng.qu Exp $
 */

public class UserContextHolder {

    private static final ThreadLocal<UserInfo> userContext = new ThreadLocal<>();

    public static void setUserContext(UserInfo userInfo) {
        userContext.set(userInfo);
    }

    public static UserInfo getUserContext() {
        return userContext.get();
    }

    public static String getUserIdentify(){
        if(Objects.nonNull(userContext.get()) && StringUtils.isNotBlank(userContext.get().getUserIdentify())){
            return userContext.get().getUserIdentify();
        }
        return null;
    }

    public static void clearUserContext() {
        userContext.remove();
    }
}
