/*
 * Xinfei.com Inc.
 * Copyright (c) 2015-2024. All Rights Reserved.
 */

package com.xinfei.vocmng.biz.api;

import com.xinfei.vocmng.biz.model.resp.ApiResponse;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;

import java.util.Map;

/**
 * <AUTHOR>
 * @version $ DictApi, v 0.1 2024/8/14 09:49 you.zhang Exp $
 */
@Api(tags = "字典API")
@RequestMapping("/dict")
public interface DictApi {
    @ApiOperation("查询策略编码接口")
    @GetMapping("/account/amount/adjustType")
    ApiResponse<Map<String, String>> queryAdjustType();
}
