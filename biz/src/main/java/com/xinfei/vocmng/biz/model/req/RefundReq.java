/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xinfei.vocmng.biz.model.req;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @version $ RefundReq, v 0.1 2024/3/25 15:31 wancheng.qu Exp $
 */

@Data
public class RefundReq implements Serializable {

    @ApiModelProperty("订单类型，PROFIT:营收订单（权益产品订单），MAIN:现金订单（信贷产品订单）")
    @NotBlank(message = "订单类型不能为空")
    private String orderType;

    @ApiModelProperty(value = "借据号")
    @NotBlank(message = "借据号不能为空")
    private String loanNo;

    @ApiModelProperty(value = "同借据下的还款单号列表")
    private List<String> repaymentNos;

    @ApiModelProperty(value = "账单号")
    private String billNo;

    @ApiModelProperty(value = "申请单id")
    private Long id;
}