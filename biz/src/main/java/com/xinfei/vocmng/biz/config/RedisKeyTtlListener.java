/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xinfei.vocmng.biz.config;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.fasterxml.jackson.core.type.TypeReference;
import com.xinfei.vocmng.biz.constants.LoginUserConstants;
import com.xinfei.vocmng.biz.mq.SseProducer;
import com.xinfei.vocmng.biz.util.RedisUtils;
import com.xinfei.vocmng.dal.mapper.DepartmentMapper;
import com.xinfei.vocmng.dal.mapper.EmployeeMapper;
import com.xinfei.vocmng.dal.po.Employee;
import com.xinfei.vocmng.itl.client.feign.impl.SmsFeignService;
import com.xinfei.vocmng.itl.client.feign.service.CisFacadeClientService;
import com.xinfei.xfframework.common.JsonUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.connection.Message;
import org.springframework.data.redis.listener.KeyExpirationEventMessageListener;
import org.springframework.data.redis.listener.RedisMessageListenerContainer;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @version $ RedisKeyTtlListener, v 0.1 2024/2/18 18:24 wancheng.qu Exp $
 */
@Component
@Slf4j
public class RedisKeyTtlListener extends KeyExpirationEventMessageListener {

    @Resource
    private EmployeeMapper employeeMapper;

    @Value("${dingTalk}")
    private String dingTalk;

    @Resource
    private DepartmentMapper departmentMapper;

    @Resource
    private SmsFeignService smsFeignService;

    @Resource
    private RedisUtils redisUtils;

    @Resource
    private SseProducer sseProducer;

    @Value("${waitTime}")
    private Long waitTime;
    @Resource
    private VocConfig vocConfig;
    @Resource
    private CisFacadeClientService cisFacadeClientService;


    public RedisKeyTtlListener(RedisMessageListenerContainer listenerContainer) {
        super(listenerContainer);
    }

    /**
     * 监听redis过期key处理
     *
     * @param message
     * @param pattern
     */
    @Override
    public void onMessage(Message message, byte[] pattern) {
        String keyName = new String(message.getBody());
        if (keyName.startsWith(LoginUserConstants.SSE_KEY)) {
            log.info("key is expired --> " + keyName);
            if (keyName.contains(LoginUserConstants.RETRY)) {
                String dkey = keyName.replace(LoginUserConstants.RETRY, "");
                Map<String, Object> value = getSseValue(dkey);
                if (MapUtils.isNotEmpty(value)) {
                    sseProducer.sendSseMsg(value);
                }
                return;
            }
            if (!"T".equals(dingTalk)) {
                return;
            }
            int end = keyName.indexOf("SUMMARYID");
            String userIdentify = keyName.substring(LoginUserConstants.SSE_KEY.length() + 5, end);
            String summaryId = keyName.substring(LoginUserConstants.SSE_KEY.length() + 5 + userIdentify.length() + 9);
            //钉钉告警 ：
            Employee employee =employeeMapper.getDepartmentIdByUserIdentify(userIdentify);
//            String director = departmentMapper.getDirectorById(employee.getDepartmentId());
//            List<String> mobiles = employeeMapper.getMobilesById(Long.parseLong(director));
            String content = "坐席 " + employee.getName() + " 未登陆客服系统/未在客服系统当前页，需要补充" + waitTime + "秒前创建的会话小结,小结id：" + summaryId;
            smsFeignService.sendRobotMsg("会话小结编辑提醒", content, null);
        }
    }

    private Map<String, Object> getSseValue(String key) {
        String value = null;
        try {
            value = redisUtils.get(key);
            return JsonUtil.parseJson(value, new TypeReference<Map<String, Object>>() {
            });
        } catch (Exception e) {
            log.warn("getSseValue error,key:{},value:{} ,error:{}", key, value, e.getMessage());
        }
        return null;
    }


}