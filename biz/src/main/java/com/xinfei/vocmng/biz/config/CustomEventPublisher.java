/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xinfei.vocmng.biz.config;

import com.xinfei.vocmng.biz.model.event.SseEvent;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Component;

/**
 *
 * <AUTHOR>
 * @version $ CustomEventPublisher, v 0.1 2024/2/7 15:41 wancheng.qu Exp $
 */

@Component
public class CustomEventPublisher {

    @Autowired
    private ApplicationEventPublisher applicationEventPublisher;

    public void publishEvent(String userIdentify,String customerPhone,String displayNumber,Long summaryId) {
        SseEvent event = new SseEvent(this, userIdentify,customerPhone,displayNumber,summaryId);
        applicationEventPublisher.publishEvent(event);
    }
}
