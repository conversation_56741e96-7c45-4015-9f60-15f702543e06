package com.xinfei.vocmng.biz.remote.impl;

import apollo.com.google.gson.reflect.TypeToken;
import com.google.common.collect.Lists;
import com.google.gson.Gson;
import com.xinfei.lendtrade.facade.rr.ManageOrderListRequest;
import com.xinfei.lendtrade.facade.rr.dto.ManageOrderDetailDTO;
import com.xinfei.lendtrade.facade.rr.dto.Page;
import com.xinfei.supervip.common.enums.ReduceApplyStatusEnum;
import com.xinfei.supervip.common.enums.RefundAccountTypeEnum;
import com.xinfei.supervip.common.enums.RefundChannelEnum;
import com.xinfei.supervip.common.enums.RefundTypeEnum;
import com.xinfei.supervip.common.enums.VipTypeEnum;
import com.xinfei.supervip.interfaces.model.admin.dto.OriginalRefundAccountAdminDTO;
import com.xinfei.supervip.interfaces.model.admin.dto.PageAdminDTO;
import com.xinfei.supervip.interfaces.model.admin.dto.RefundApplyAdminDTO;
import com.xinfei.supervip.interfaces.model.admin.dto.RefundApplyCancelResultAdminDTO;
import com.xinfei.supervip.interfaces.model.admin.dto.RefundApplyExecuteResultAdminDTO;
import com.xinfei.supervip.interfaces.model.admin.dto.RefundApplyListAdminDTO;
import com.xinfei.supervip.interfaces.model.admin.dto.RefundApplyResultAdminDTO;
import com.xinfei.supervip.interfaces.model.admin.dto.RefundListAdminDTO;
import com.xinfei.supervip.interfaces.model.admin.dto.VipOpsResultAdminDTO;
import com.xinfei.supervip.interfaces.model.admin.dto.VipOrderDetailAdminDTO;
import com.xinfei.supervip.interfaces.model.admin.dto.VipOrderPayLogAdminDTO;
import com.xinfei.supervip.interfaces.model.admin.dto.VipOrderReduceDetailAdminDTO;
import com.xinfei.supervip.interfaces.model.admin.dto.VipOrderReducePriceApplyAdminDTO;
import com.xinfei.supervip.interfaces.model.admin.request.CancelVipRefundApplyAdminRequest;
import com.xinfei.supervip.interfaces.model.admin.request.CreateVipRefundApplyAdminRequest;
import com.xinfei.supervip.interfaces.model.admin.request.ExecuteVipRefundApplyAdminRequest;
import com.xinfei.supervip.interfaces.model.admin.request.QueryOriginalRefundAccountAdminRequest;
import com.xinfei.supervip.interfaces.model.admin.request.QueryVipReduceListAdminRequest;
import com.xinfei.vipcore.facade.rr.dto.PayAccountDTO;
import com.xinfei.vipcore.facade.rr.dto.RefundApplyDto;
import com.xinfei.vipcore.facade.rr.dto.RefundApplyListDto;
import com.xinfei.vipcore.facade.rr.dto.RefundApplyResDto;
import com.xinfei.vipcore.facade.rr.dto.RefundListDto;
import com.xinfei.vipcore.facade.rr.dto.RefundStartDto;
import com.xinfei.vipcore.facade.rr.dto.RenewLogAdminDto;
import com.xinfei.vipcore.facade.rr.request.OrderPayAccountRequest;
import com.xinfei.vipcore.facade.rr.request.VipRefundApplyRequest;
import com.xinfei.vipcore.facade.rr.request.VipRefundApplyStartRequest;
import com.xinfei.vocmng.biz.config.VocConfig;
import com.xinfei.vocmng.biz.mapstruct.VipCardConverter;
import com.xinfei.vocmng.biz.mapstruct.VipOrderPayLogConverter;
import com.xinfei.vocmng.biz.model.annotation.DataPermission;
import com.xinfei.vocmng.biz.model.base.ControlItemValue;
import com.xinfei.vocmng.biz.model.base.ControlRes;
import com.xinfei.vocmng.biz.model.base.PageResultResponse;
import com.xinfei.vocmng.biz.model.enums.ControlEnum;
import com.xinfei.vocmng.biz.model.enums.FeeStrategyEnum;
import com.xinfei.vocmng.biz.model.enums.PlanSourceEnum;
import com.xinfei.vocmng.biz.model.enums.TechplayErrDtlEnum;
import com.xinfei.vocmng.biz.model.exception.IgnoreException;
import com.xinfei.vocmng.biz.model.exception.TechplayException;
import com.xinfei.vocmng.biz.model.resp.ApiResponse;
import com.xinfei.vocmng.biz.remote.MemberInterestRemoteService;
import com.xinfei.vocmng.biz.rr.RightCardPackInfoDto;
import com.xinfei.vocmng.biz.rr.dto.CardDeductionCalculationDto;
import com.xinfei.vocmng.biz.rr.dto.MemberCardDto;
import com.xinfei.vocmng.biz.rr.dto.MemberCardUseInfoDto;
import com.xinfei.vocmng.biz.rr.dto.RightCardDetailDto;
import com.xinfei.vocmng.biz.rr.dto.RightCardDiscountDto;
import com.xinfei.vocmng.biz.rr.dto.RightCardRefundDto;
import com.xinfei.vocmng.biz.rr.dto.RightCardRefundRecordDto;
import com.xinfei.vocmng.biz.rr.dto.RightUseDetailDto;
import com.xinfei.vocmng.biz.rr.dto.VipOrderPayLogDetailDTO;
import com.xinfei.vocmng.biz.rr.dto.VipOrderReduceDetailAdmin;
import com.xinfei.vocmng.biz.rr.dto.VipOrderReducePriceApplyAdmin;
import com.xinfei.vocmng.biz.rr.dto.VipRefundDetailDto;
import com.xinfei.vocmng.biz.rr.request.CancelVipReduceApplyRequest;
import com.xinfei.vocmng.biz.rr.request.CardDeductionCalculationRequest;
import com.xinfei.vocmng.biz.rr.request.CardDiscountListRequest;
import com.xinfei.vocmng.biz.rr.request.LoadRightCardRequest;
import com.xinfei.vocmng.biz.rr.request.QueryMemberCardListRequest;
import com.xinfei.vocmng.biz.rr.request.QueryMemberCardUsedListRequest;
import com.xinfei.vocmng.biz.rr.request.ReduceApplyRequest;
import com.xinfei.vocmng.biz.rr.request.RefundResultMsg;
import com.xinfei.vocmng.biz.rr.request.SVipRefundResultMsg;
import com.xinfei.vocmng.biz.rr.request.VipCardBlackReq;
import com.xinfei.vocmng.biz.rr.request.VipCardRefundApply;
import com.xinfei.vocmng.biz.rr.request.VipCardRefundApplyStart;
import com.xinfei.vocmng.biz.rr.request.VipCardRefundLog;
import com.xinfei.vocmng.biz.rr.request.VipCardRefundLogReq;
import com.xinfei.vocmng.biz.rr.request.VipRefundDetailReq;
import com.xinfei.vocmng.biz.rr.response.CardRefund;
import com.xinfei.vocmng.biz.service.CardUsageService;
import com.xinfei.vocmng.biz.service.CommonService;
import com.xinfei.vocmng.biz.service.ControlItemValueFactory;
import com.xinfei.vocmng.biz.service.EmployeeService;
import com.xinfei.vocmng.biz.service.impl.VipStrategyServiceImpl;
import com.xinfei.vocmng.biz.strategy.dto.StrategyExecutionResult;
import com.xinfei.vocmng.biz.strategy.dto.VipRefundStrategyInput;
import com.xinfei.vocmng.biz.util.LocalDateTimeUtils;
import com.xinfei.vocmng.biz.util.UserContextHolder;
import com.xinfei.vocmng.dal.dto.resp.ControlData;
import com.xinfei.vocmng.dal.dto.resp.FeeStrategyConfig;
import com.xinfei.vocmng.itl.MemberInterestFeignClient;
import com.xinfei.vocmng.itl.client.feign.CisFacadeClient;
import com.xinfei.vocmng.itl.client.feign.LendQueryFacadeClient;
import com.xinfei.vocmng.itl.client.feign.impl.LcsFeignService;
import com.xinfei.vocmng.itl.client.feign.impl.VipFacadeClientImpl;
import com.xinfei.vocmng.itl.client.feign.impl.WorkOrderFeignClientImpl;
import com.xinfei.vocmng.itl.client.feign.service.CisFacadeClientService;
import com.xinfei.vocmng.itl.model.enums.CardStatusEnum;
import com.xinfei.vocmng.itl.model.enums.CardTypeEnum;
import com.xinfei.vocmng.itl.model.enums.MemberTypeEnum;
import com.xinfei.vocmng.itl.model.enums.PayStatusEnum;
import com.xinfei.vocmng.itl.model.enums.PayTypeEnum;
import com.xinfei.vocmng.itl.model.enums.PayTypeRenewEnum;
import com.xinfei.vocmng.itl.model.enums.VipCardStatusEnum;
import com.xinfei.vocmng.itl.rr.MemberCard;
import com.xinfei.vocmng.itl.rr.MemberCardRequest;
import com.xinfei.vocmng.itl.rr.MemberInterestResponse;
import com.xinfei.vocmng.itl.rr.OldMemberCard;
import com.xinfei.vocmng.itl.rr.RightCardPackInfo;
import com.xinfei.vocmng.itl.rr.RightPackRequest;
import com.xinfei.vocmng.itl.rr.RightUseDetail;
import com.xinfei.vocmng.itl.rr.VipBlackRecords;
import com.xinfei.vocmng.itl.rr.VipBlackRequest;
import com.xinfei.vocmng.itl.rr.dto.CreateTaskDto;
import com.xinfei.vocmng.itl.util.LogUtil;
import com.xinfei.xfframework.common.JsonUtil;
import com.xyf.cis.query.facade.dto.standard.response.ThreeElementsDTO;
import com.xyf.cis.query.facade.dto.standard.response.UserNoDTO;
import com.xyf.cis.query.facade.dto.standard.response.UserSearchDTO;
import com.xyf.user.facade.common.model.PageResult;
import io.kyoto.pillar.lcs.loan.domain.request.LoanPlanRequest;
import io.kyoto.pillar.lcs.loan.domain.response.LoanPlanResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.io.IOException;
import java.lang.reflect.Type;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version $ MemberInterestRemoteServiceImpl, v 0.1 2023/12/25 11:02 qu.lu Exp $
 */
@Slf4j
@Service
public class MemberInterestRemoteServiceImpl implements MemberInterestRemoteService {
    @Autowired
    private MemberInterestFeignClient memberInterestFeignClient;

    @Autowired
    private VipFacadeClientImpl vipFacadeClient;

    @Resource
    private CisFacadeClientService cisFacadeClientService;

    @Autowired
    private EmployeeService employeeService;

    @Resource
    private CisFacadeClient cisFacadeClient;

    @Resource
    private ControlItemValueFactory factory;

    @Resource
    private WorkOrderFeignClientImpl workOrderFeignClient;

    @Autowired
    protected VocConfig vocConfig;

    @Resource
    private VipStrategyServiceImpl vipStrategyService;

    @Resource
    private LendQueryFacadeClient lendQueryFacadeClient;

    @Resource
    private CardUsageService cardUsageService;

    @Resource
    private CommonService commonService;

    @Resource
    private LcsFeignService lcsFeignService;

    private final Gson gson = new Gson();

    private final Type typeList = new TypeToken<List<String>>() {
    }.getType();

    @Value("${refund.taskType}")
    private Integer refundType;

    @Value("${refund.subTaskType}")
    private Integer subRefundType;

    @Override
    public ApiResponse<RightCardPackInfoDto> loadRightCardDetail(LoadRightCardRequest request) {
        String validateResult = validateRightCardRequest(request);
        if (StringUtils.isNotEmpty(validateResult)) {
            return ApiResponse.paramIllegal(validateResult);
        }

        try {
            RightPackRequest rightPackRequest = buildRightPackRequest(request);
            MemberInterestResponse<RightCardPackInfo> response = memberInterestFeignClient.loadRightCardInfo(rightPackRequest);
            log.info(LogUtil.clientLog("MemberInterestFeignClient", "loadRightCardInfo", rightPackRequest, response));
            if (!response.isSuccess()) {
                log.error("load right card detail fail, request={},response={}", request, response);
                return ApiResponse.fail("load right card detail failed.");
            }

            RightCardPackInfo packInfo = response.getData();
            if (packInfo == null) {
                return ApiResponse.success(null);
            }

            return ApiResponse.success(convertRightCardDetail(packInfo));
        } catch (Exception e) {
            log.error("load right card detail failed, request=" + request, e);
        }

        return ApiResponse.fail("load right card failed.");
    }

    @Override
    public Boolean renewStopAdmin(String userNo) {
        return vipFacadeClient.renewStopAdmin(userNo);
    }

    @Override
    public Boolean disableVipRenew(String userNo) {
        String creator = employeeService.getUserNameForIdentify(UserContextHolder.getUserIdentify());
        return vipFacadeClient.disableVipRenew(userNo, creator);
    }


    @Override
    public ApiResponse<List<MemberCardUseInfoDto>> queryMemberCardUsedInfo(QueryMemberCardUsedListRequest request) {
        return ApiResponse.success(cardUsageService.queryCardUsedInfo(request));
    }

    private RightCardPackInfoDto convertRightCardDetail(RightCardPackInfo packInfo) {
        RightCardPackInfoDto result = new RightCardPackInfoDto();
        RightCardDetailDto cardDetailDto = new RightCardDetailDto();
        BeanUtils.copyProperties(packInfo.getPackInfo(), cardDetailDto);

        result.setRightUseInfo(convertRightUseDetail(packInfo.getItemInfo()));
        result.setCardInfo(cardDetailDto);

        return result;
    }

    private List<RightUseDetailDto> convertRightUseDetail(List<RightUseDetail> detailList) {
        if (CollectionUtils.isEmpty(detailList)) {
            return Collections.emptyList();
        }

        List<RightUseDetailDto> result = Lists.newArrayListWithCapacity(detailList.size());
        RightUseDetailDto dto;
        for (RightUseDetail detail : detailList) {
            dto = new RightUseDetailDto();
            BeanUtils.copyProperties(detail, dto);
            result.add(dto);
        }

        return result;
    }

    private RightPackRequest buildRightPackRequest(LoadRightCardRequest request) {
        RightPackRequest packRequest = new RightPackRequest();
        BeanUtils.copyProperties(request, packRequest);

        return packRequest;
    }

    private String validateRightCardRequest(LoadRightCardRequest request) {
        if (request == null) {
            return "param is required.";
        }
        if (StringUtils.isEmpty(request.getOrderNumber())) {
            return "orderNumber is required.";
        }

        return null;
    }

    @Override
    public ApiResponse<List<MemberCardDto>> queryMemberCardList(QueryMemberCardListRequest request) {
        String validateResult = validateMemberCardRequest(request);
        if (StringUtils.isNotEmpty(validateResult)) {
            return ApiResponse.paramIllegal(validateResult);
        }

        return ApiResponse.success(queryAllMemberCardList(request));
    }

    @Override
    public Boolean stopWithhold(Long vipOrderId) {
        String creator = employeeService.getUserNameForIdentify(UserContextHolder.getUserIdentify());
        return vipFacadeClient.stopWithhold(vipOrderId, creator);
    }

    @Override
    public Boolean cancelVipDeduct(Long vipOrderId) {
        String creator = employeeService.getUserNameForIdentify(UserContextHolder.getUserIdentify());
        return vipFacadeClient.cancelVipDeduct(vipOrderId, creator);
    }


    /**
     * 查询会员卡列表信息，聚合新老会员卡信息
     *
     * @param request
     * @return
     */
    private List<MemberCardDto> queryAllMemberCardList(QueryMemberCardListRequest request) {
        List<Long> userNos = new ArrayList<>();
        if (StringUtils.isNotEmpty(request.getApp()) && StringUtils.isNotBlank(request.getMobile())) {
            UserNoDTO userNoDTO = cisFacadeClient.getUserNoByMobileAndApp(request.getMobile(), request.getApp());
            if (userNoDTO != null) {
                userNos.add(userNoDTO.getUserNo());
            }
        } else if (StringUtils.isNotBlank(request.getMobile())) {
            PageResult<UserSearchDTO> result = cisFacadeClient.queryUserList(request.getMobile(), null, null, 1, 30);
            if (org.apache.commons.collections.CollectionUtils.isNotEmpty(result.getList())) {
                userNos = result.getList().stream().map(UserSearchDTO::getUserNo).collect(Collectors.toList());
            }

            List<UserSearchDTO> custNoUsers = commonService.getUserByMobileAndCust(request.getMobile());
            List<Long> userNosByCustNo = custNoUsers.stream().map(UserSearchDTO::getUserNo).collect(Collectors.toList());

            if (org.apache.commons.collections.CollectionUtils.isNotEmpty(userNos)) {
                userNos.addAll(userNosByCustNo);
            } else {
                userNos = userNosByCustNo;
            }
            // 去重
            userNos = userNos.stream().distinct().collect(Collectors.toList());

        }

        if (StringUtils.isNotBlank(request.getUserNo())) {
            if (StringUtils.isNotBlank(request.getMobile()) && !userNos.contains(Long.parseLong(request.getUserNo()))) {
                return new ArrayList<>();
            }
            userNos.add(Long.parseLong(request.getUserNo()));
            userNos = userNos.stream().filter(r -> Long.parseLong(request.getUserNo()) == r).collect(Collectors.toList());
        }

        if (CollectionUtils.isEmpty(userNos) && StringUtils.isBlank(request.getOrderNo())) {
            return new ArrayList<>();
        }

        List<MemberCardDto> result = new ArrayList<>();

        //vipCore支持orderNo的查询 当orderNo为空时，查询所有会员卡接口 orderNo不为空则只查询vipCore的接口
        if (!CollectionUtils.isEmpty(userNos) && StringUtils.isBlank(request.getOrderNo())) {
            MemberCardRequest cardRequest = buildMemberCardRequest(request);
            cardRequest.setUserId(userNos);
            List<MemberCardDto> newCardList = queryNewMemberCardList(cardRequest);
            if (!CollectionUtils.isEmpty(newCardList)) {
                result.addAll(newCardList);
            }

            List<MemberCardDto> oldCardList = queryOldMemberCardList(cardRequest);
            if (!CollectionUtils.isEmpty(oldCardList)) {
                result.addAll(oldCardList);
            }
        }

        List<MemberCardDto> renewCardList = queryRenewCardList(userNos, request);
        result.addAll(renewCardList);

        if (vocConfig.getIsSuperVipCard()) {
            List<MemberCardDto> vipCardList = queryVipOrderList(userNos, request);
            result.addAll(vipCardList);
        }

        if (CollectionUtils.isEmpty(result)) {
            return new ArrayList<>();
        }
        List<MemberCardDto> memberCardDtos = new ArrayList<>();
        memberCardDtos.addAll(result.stream().filter(r -> StringUtils.isNotEmpty(r.getCreateTime())).sorted(Comparator.comparing(MemberCardDto::getCreateTime).reversed()).collect(Collectors.toList()));
        memberCardDtos.addAll(result.stream().filter(r -> StringUtils.isEmpty(r.getCreateTime())).collect(Collectors.toList()));

        for (MemberCardDto memberCardDto : memberCardDtos) {
            //提额卡返回的LendNo不为空时 获取订单状态
            if (StringUtils.isNotBlank(memberCardDto.getLendNo())) {
                ManageOrderListRequest manageOrderListRequest = new ManageOrderListRequest();
                manageOrderListRequest.setOrderNos(Collections.singletonList(memberCardDto.getLendNo()));
                Page<ManageOrderDetailDTO> respDTO = lendQueryFacadeClient.getOrderList(manageOrderListRequest);
                if (!respDTO.getPageList().isEmpty()) {
                    LoanPlanRequest loanPlanRequest = new LoanPlanRequest();
                    loanPlanRequest.setLoanNos(Collections.singletonList(respDTO.getPageList().get(0).getLoanNo()));
                    List<LoanPlanResponse> loanPlanResponses = lcsFeignService.planDetail(loanPlanRequest);
                    memberCardDto.setOrderStatus(respDTO.getPageList().get(0).getStatus());
                    memberCardDto.setLendNo(respDTO.getPageList().get(0).getLoanReqNo());
                    if (respDTO.getPageList().get(0).getDateCreated() != null) {
                        memberCardDto.setDateCreated(LocalDateTimeUtils.toLocalDateTime(respDTO.getPageList().get(0).getDateCreated()));
                    }
                    memberCardDto.setLoanAmount(respDTO.getPageList().get(0).getLoanAmt());
                    if (!loanPlanResponses.isEmpty() && loanPlanResponses.get(0) != null) {
                            memberCardDto.setLoanStatus(loanPlanResponses.get(0).getStatus());
                    }
                }
            }

            if (StringUtils.isNotEmpty(memberCardDto.getUserId()) && StringUtils.isEmpty(memberCardDto.getName())) {
                try {
                    ThreeElementsDTO threeElementsDTO = cisFacadeClient.queryThreeElementsByUserNo(Long.parseLong(memberCardDto.getUserId()));

                    if (StringUtils.isEmpty(memberCardDto.getMobile())) {
                        memberCardDto.setMobile(threeElementsDTO.getMobile());
                    }
                    memberCardDto.setName(threeElementsDTO.getName());
                } catch (Exception e) {
                    log.warn("queryThreeElementsByUserNo error");
                }
            }
        }

        if (org.apache.commons.collections.CollectionUtils.isNotEmpty(memberCardDtos) && StringUtils.isNotBlank(request.getApp())) {
            memberCardDtos = memberCardDtos.stream().filter(r -> r.getApp().equals(request.getApp())).collect(Collectors.toList());
        }
        return memberCardDtos;
    }

    private List<MemberCardDto> queryRenewCardList(List<Long> userNos, QueryMemberCardListRequest request) {
        Integer status = null;
        if (StringUtils.isNotBlank(request.getVipStatus())) {
            Optional<VipCardStatusEnum> vipCardStatusEnum = VipCardStatusEnum.fromName(request.getVipStatus());
            if (vipCardStatusEnum.isPresent() && vipCardStatusEnum.get().getRenewCardStatusCode().isPresent()) {
                VipCardStatusEnum vip = vipCardStatusEnum.get();
                status = vip.getRenewCardStatusCode().get();
            }
        }

        List<RenewLogAdminDto> renewCardListResponse = new ArrayList<>();
        if (StringUtils.isBlank(request.getVipStatus()) || status != null) {
            renewCardListResponse = vipFacadeClient.renewLogAdmin(userNos, request.getOrderNo(), status);
        }

        if (CollectionUtils.isEmpty(renewCardListResponse)) {
            return Collections.emptyList();
        }
        return convertRenew(renewCardListResponse);
    }

    private List<MemberCardDto> queryVipOrderList(List<Long> userNos, QueryMemberCardListRequest request) {
        String status = null;
        if (StringUtils.isNotBlank(request.getVipStatus())) {
            Optional<VipCardStatusEnum> vipCardStatusEnum = VipCardStatusEnum.fromName(request.getVipStatus());
            if (vipCardStatusEnum.isPresent() && vipCardStatusEnum.get().getVipCardStatusCode().isPresent()) {
                VipCardStatusEnum vip = vipCardStatusEnum.get();
                status = vip.getVipCardStatusCode().get();
            }
        }

        List<VipOrderDetailAdminDTO> vipOrderDetailDTOS = new ArrayList<>();
        if (StringUtils.isBlank(request.getVipStatus()) || status != null) {
            vipOrderDetailDTOS = vipFacadeClient.queryVipOrderList(userNos, request.getOrderNo(), status, null);
        }

        if (CollectionUtils.isEmpty(vipOrderDetailDTOS)) {
            return Collections.emptyList();
        }
        return convertVipOrder(vipOrderDetailDTOS);
    }

    private List<MemberCardDto> convertRenew(List<RenewLogAdminDto> cardList) {
        if (CollectionUtils.isEmpty(cardList)) {
            return Collections.emptyList();
        }

        List<MemberCardDto> result = Lists.newArrayListWithCapacity(cardList.size());
        MemberCardDto dto;
        for (RenewLogAdminDto card : cardList) {
            dto = VipCardConverter.INSTANCE.renewLogAdminDtoToMemberCardDto(card);
            dto.setType(CardTypeEnum.RENEW_CARD.getType());
            //当订单类型为飞享会员时，创建时间字段改为取created_time
            if ("飞享会员".equals(dto.getCardName())) {
                dto.setCreateTime(card.getCreateTime());
            }
            dto.setPayTypeDesc(PayTypeRenewEnum.getDescByCode(card.getPayType()));
            dto.setPayAmount(dto.getPayAmount().multiply(new BigDecimal("0.01")));
            result.add(dto);
        }

        return result;
    }

    private List<MemberCardDto> convertVipOrder(List<VipOrderDetailAdminDTO> cardList) {
        List<MemberCardDto> result = Lists.newArrayListWithCapacity(cardList.size());
        MemberCardDto dto;
        for (VipOrderDetailAdminDTO card : cardList) {
            dto = VipCardConverter.INSTANCE.vipOrderDtoToMemberCardDto(card);
            dto.setType(CardTypeEnum.VIP_CARD.getType());
            dto.setPayTypeDesc(PayTypeEnum.getDescByCode(dto.getPayType()));
            result.add(dto);
        }

        return result;
    }

    private List<MemberCardDto> queryNewMemberCardList(MemberCardRequest cardRequest) {
        MemberInterestResponse<List<MemberCard>> newCardListResponse = memberInterestFeignClient.queryNewVipRecords(cardRequest);
        log.info(LogUtil.clientLog("MemberInterestFeignClient", "queryNewMemberCardList", cardRequest, newCardListResponse));
        if (!newCardListResponse.isSuccess()) {
            log.error("query new member card list failed, request={},response={}", cardRequest, newCardListResponse);
            return Collections.emptyList();
        }

        return convert(newCardListResponse.getData());
    }

    private List<MemberCardDto> queryOldMemberCardList(MemberCardRequest cardRequest) {
        //TODO:查询老的会员卡接口分页参数是否必须
        MemberInterestResponse<List<OldMemberCard>> oldCardListResponse = memberInterestFeignClient.queryPayRecords(cardRequest);
        log.info(LogUtil.clientLog("MemberInterestFeignClient", "queryOldMemberCardList", cardRequest, oldCardListResponse));
        if (!oldCardListResponse.isSuccess()) {
            log.error("query old member card list failed, request={},response={}", cardRequest, oldCardListResponse);
            return Collections.emptyList();
        }

        return converOldMemberList(oldCardListResponse.getData());
    }

    private List<MemberCardDto> converOldMemberList(List<OldMemberCard> cardList) {
        if (CollectionUtils.isEmpty(cardList)) {
            return Collections.emptyList();
        }

        List<MemberCardDto> result = Lists.newArrayListWithCapacity(cardList.size());
        MemberCardDto dto;
        for (OldMemberCard card : cardList) {
            dto = VipCardConverter.INSTANCE.oldMemberCardToMemberCardDto(card);
            if (dto.getCardStatus() != null && dto.getCardStatus() != 2 &&
                    StringUtils.isNotEmpty(dto.getEndTime()) && LocalDate.now().isAfter(LocalDateTimeUtils.parseLocalDateByDateStr(dto.getEndTime()))) {
                dto.setCardStatus(3);
            }
            dto.setType(CardTypeEnum.OLD_CARD.getType());
            if ("飞享会员".equals(dto.getCardName())) {
                dto.setCreateTime(card.getCreateTime());
            }
            dto.setCardStatusDesc(CardStatusEnum.getDescByCode(dto.getCardStatus()));
            dto.setPayAmount(convert(card.getPayAmount()));
            dto.setPayTypeDesc(PayTypeEnum.getDescByCode(card.getPayType()));
            dto.setPayStatusDesc(PayStatusEnum.getDescByCode(card.getPayStatus()));
            result.add(dto);
        }

        return result;
    }

    private static BigDecimal convert(Integer amount) {
        if (amount == null) {
            return BigDecimal.ZERO;
        }
        BigDecimal originAmount = new BigDecimal(amount);
        return originAmount.divide(new BigDecimal(100));
    }

    private List<MemberCardDto> convert(List<MemberCard> cardList) {
        if (CollectionUtils.isEmpty(cardList)) {
            return Collections.emptyList();
        }

        List<MemberCardDto> result = Lists.newArrayListWithCapacity(cardList.size());
        MemberCardDto dto;
        for (MemberCard card : cardList) {
            dto = VipCardConverter.INSTANCE.memberCardToMemberCardDto(card);
            if (dto.getCardStatus() != null && dto.getCardStatus() != 2 &&
                    StringUtils.isNotEmpty(dto.getEndTime()) && LocalDate.now().isAfter(LocalDateTimeUtils.parseLocalDateByDateStr(dto.getEndTime()))) {
                dto.setCardStatus(3);
            }
            dto.setType(CardTypeEnum.NEW_CARD.getType());
            if ("飞享会员".equals(dto.getCardName())) {
                dto.setCreateTime(card.getCreateTime());
            }
            dto.setCardStatusDesc(CardStatusEnum.getDescByCode(dto.getCardStatus()));
            dto.setPayAmount(convert(card.getPayAmount()));
            if (StringUtils.isNotBlank(card.getLimitUpAmount())) {
                dto.setLimitUpAmount(convert(Integer.parseInt(card.getLimitUpAmount())));
            }
            if (dto.getPayStatus() == 10) {
                dto.setPayStatusDesc("支付成功");
            } else {
                dto.setPayStatusDesc(PayStatusEnum.getDescByCode(card.getPayStatus()));
            }
            dto.setPayTypeDesc(PayTypeEnum.getDescByCode(card.getPayType()));
            result.add(dto);
        }

        return result;
    }

    private MemberCardRequest buildMemberCardRequest(QueryMemberCardListRequest request) {
        MemberCardRequest result = new MemberCardRequest();
        BeanUtils.copyProperties(request, result);

        return result;
    }

    private String validateMemberCardRequest(QueryMemberCardListRequest request) {
        if (request == null) {
            return "param is required.";
        }
        if (StringUtils.isEmpty(request.getMobile()) && StringUtils.isEmpty(request.getUserNo()) && StringUtils.isEmpty(request.getOrderNo())) {
            return "支付订单号、手机号、userNo必填其一";
        }

        return null;
    }

    @Override
    public ApiResponse<List<RightCardRefundDto>> queryRightCardRefund(List<VipCardRefundLog> request) {
        List<RightCardRefundDto> refundList = new ArrayList<>();
        //查询外部会员卡相关信息
        for (VipCardRefundLog vipCardRefundLog : request) {

            RightCardRefundDto refundApply = new RightCardRefundDto();
            //可退金额
            Integer remainRefundAmount;
            if (vipCardRefundLog.getCardType() == 4 && vocConfig.getIsSuperVipCard()) {
                RefundApplyListAdminDTO refundApplyListDTO = vipFacadeClient.queryVipRefundApplyList(vipCardRefundLog.getVipCardId());
                if (refundApplyListDTO == null) {
                    continue;
                }
//                refundApply = VipCardConverter.INSTANCE.vipRefundApplyListToRefundDto(refundApplyListDTO);
                remainRefundAmount = refundApplyListDTO.getRemainRefundAmount();
            } else {
                RefundApplyListDto refundApplyListDto = vipFacadeClient.vipCardRefundApplyList(vipCardRefundLog.getVipCardId(), vipCardRefundLog.getCardType());
//                refundApply = VipCardConverter.INSTANCE.refundApplyListToRefundDto(refundApplyListDto);
                if (refundApplyListDto == null) {
                    continue;
                }
                remainRefundAmount = refundApplyListDto.getRemainRefundAmount();
            }


            if (remainRefundAmount != null) {
                refundApply.setRemainRefundAmount(new BigDecimal(remainRefundAmount).multiply(new BigDecimal("0.01")));
                List<ControlData> controlDataList = getVipCardControlData(vipCardRefundLog.getCardType());

                if (org.apache.commons.collections.CollectionUtils.isNotEmpty(controlDataList)) {
                    ControlData controlData = controlDataList.get(0);
                    ControlEnum type = ControlEnum.getByControlChildType(controlData.getControlChildType());

                    ControlItemValue<?, ?, ?> values;
                    Map<String, String> decisionResult = null;

                    if (vocConfig.getIsNewFeeControl()) {
                        List<String> whiteList = gson.fromJson(vocConfig.getUserIdentifyWhiteList(), typeList);
                        if (!whiteList.contains(UserContextHolder.getUserContext().getUserIdentify())) {
                            VipRefundStrategyInput input = VipRefundStrategyInput
                                    .builder()
                                    .vipCardRefundLog(vipCardRefundLog)
                                    .build();
                            StrategyExecutionResult<FeeStrategyConfig> strategyExecutionResult = vipStrategyService.executeStrategy(FeeStrategyEnum.VIP_CARD_REFUNDS, input);
                            decisionResult = strategyExecutionResult.getResult();
                        }
                    }

                    try {
                        if (decisionResult == null || StringUtils.isEmpty(decisionResult.get("Out_Membership_Refund_Amount"))) {
                            values = factory.create(controlData.getControlValue(), type);
                        } else {
                            ControlItemValue<BigDecimal, BigDecimal, BigDecimal> newValues = new ControlItemValue<>();
                            newValues.setO(new BigDecimal(decisionResult.get("Out_Membership_Refund_Amount")));
                            newValues.setT(new BigDecimal("0"));
                            values = newValues;
                        }
                    } catch (IOException e) {
                        throw new TechplayException(TechplayErrDtlEnum.REFUND_ERROR, "会员卡退费费项获取失败");
                    }

                    ControlRes<?, ?> result = type.calculateAmount(values, new BigDecimal(remainRefundAmount).multiply(new BigDecimal("0.01")));
                    BigDecimal min = (BigDecimal) result.getLeft();
                    BigDecimal max = (BigDecimal) result.getRight();
                    refundApply.setRefundAmountMin(min);
                    refundApply.setRefundAmountMax(max);
                    refundApply.setIsRefund(true);
                } else {
                    refundApply.setIsRefund(false);
                }
            }
            BeanUtils.copyProperties(vipCardRefundLog, refundApply);
            refundList.add(refundApply);
        }

        return ApiResponse.success(refundList);
    }

    @Override
    public RightCardDiscountDto queryRightCardDiscount(VipCardRefundLog request, VipOrderDetailAdminDTO vipOrderDetailDTO) {

        RightCardDiscountDto result = new RightCardDiscountDto();
        result.setCanDiscountLower(BigDecimal.ZERO);
        VipRefundStrategyInput input = VipRefundStrategyInput
                .builder()
                .vipCardRefundLog(request)
                .build();
        StrategyExecutionResult<FeeStrategyConfig> strategyExecutionResult = vipStrategyService.executeStrategy(FeeStrategyEnum.CARD_DISCOUNT, input);
        Map<String, String> decisionResult = strategyExecutionResult.getResult();

        if (decisionResult == null || StringUtils.isEmpty(decisionResult.get("Out_Membership_Reduction_Amount"))) {
            throw new IgnoreException(TechplayErrDtlEnum.EXEMPTION_ERROR, "会员卡减免决策未获取到合理出参");
        }

        BigDecimal value = new BigDecimal(decisionResult.get("Out_Membership_Reduction_Amount"));
        BigDecimal sale = new BigDecimal(vipOrderDetailDTO.getSalePrice()).multiply(new BigDecimal("0.01"));
        if (sale.compareTo(BigDecimal.ZERO) < 0) {
            throw new IgnoreException(TechplayErrDtlEnum.EXEMPTION_ERROR, "会员卡售价小于0！");
        }

        result.setCanDiscountUpper(sale.multiply(value).multiply(new BigDecimal("0.01")));

        if (vipOrderDetailDTO.getCostPrice() != null) {
            BigDecimal cost = new BigDecimal(vipOrderDetailDTO.getCostPrice()).multiply(new BigDecimal("0.01"));
            BigDecimal maxReduce = sale.subtract(result.getCanDiscountUpper());
            if (maxReduce.compareTo(BigDecimal.ZERO) < 0) {
                throw new IgnoreException(TechplayErrDtlEnum.EXEMPTION_ERROR, "成本价格高于售价！");
            }
            if (cost.compareTo(maxReduce) > 0) {
                result.setCanDiscountUpper(sale.subtract(cost));
            }
        }

        if (!StringUtils.isEmpty(decisionResult.get("Out_Membership_Reduction_Cap"))) {
            BigDecimal cap = new BigDecimal(decisionResult.get("Out_Membership_Reduction_Cap"));
            if (cap.compareTo(BigDecimal.ZERO) < 0) {
                throw new IgnoreException(TechplayErrDtlEnum.EXEMPTION_ERROR, "盖帽金额小于0！");
            }
            if (result.getCanDiscountUpper().compareTo(cap) > 0) {
                result.setCanDiscountUpper(cap);
            }
        }

        return result;
    }

    @NotNull
    private static List<ControlData> getVipCardControlData(Integer cardType) {
        //费控
        int controlChildType;
        if (cardType == 3) {
            controlChildType = 10;
        } else {
            controlChildType = 9;
        }

        return UserContextHolder.getUserContext().getControlDataList().stream().filter(r -> r.getControlScene() == 4 && Objects.equals(r.getControlChildType(), controlChildType)).collect(Collectors.toList());
    }

    @Override
    public ApiResponse<RefundApplyResDto> vipCardRefundApply(List<VipCardRefundApply> request) {
        RefundApplyResDto refundApplyDto = new RefundApplyResDto();
        refundApplyDto.setMessage("退款成功");
        refundApplyDto.setSuccess(true);
        StringBuilder message = new StringBuilder();
        for (VipCardRefundApply vipCardRefundApply : request) {

            String validateResult = validateVipCardRefundApply(vipCardRefundApply);

            if (StringUtils.isNotEmpty(validateResult)) {
                return ApiResponse.paramIllegal(validateResult);
            }
            List<ControlData> controlDataList = getVipCardControlData(vipCardRefundApply.getCardType());
            if (CollectionUtils.isEmpty(controlDataList)) {
                throw new IgnoreException(TechplayErrDtlEnum.REFUND_ERROR, "您无退费退卡权限");
            }

            if (vipCardRefundApply.getCardType() == 4 && vocConfig.getIsSuperVipCard()) {
                CreateVipRefundApplyAdminRequest vipRefundApplyRequest = new CreateVipRefundApplyAdminRequest();
                vipRefundApplyRequest.setVipOrderId(vipCardRefundApply.getVipCardId());
                vipRefundApplyRequest.setVipType(MemberTypeEnum.FEI_YUE.getCode());

                // 处理 refundType
                Integer refundType = vipCardRefundApply.getRefundType();
                if (refundType != null) {
                    vipRefundApplyRequest.setRefundType(
                            refundType == 1 ? RefundTypeEnum.REFUND_ONLY.getCode() : RefundTypeEnum.REFUND_CARD.getCode()
                    );
                }

                // 处理 refundChannel 前端统一传1 具体退款逻辑由会员中心控制
                Integer refundChannel = vipCardRefundApply.getRefundChannel();
                if (refundChannel != null) {
                    vipRefundApplyRequest.setRefundChannel(
                            refundChannel == 1 ? RefundChannelEnum.ORIGINAL_REFUND.getCode() : RefundChannelEnum.OFFLINE_REFUND.getCode()
                    );
                }

                vipRefundApplyRequest.setRefundAccountBank(vipCardRefundApply.getOfflineAccountBankName());

                // 处理 offlineAccountType
                Integer offlineAccountType = vipCardRefundApply.getOfflineAccountType();
                if (offlineAccountType != null) {
                    vipRefundApplyRequest.setRefundAccountType(
                            offlineAccountType == 1 ? RefundAccountTypeEnum.DEBIT_CARD.getCode() : RefundAccountTypeEnum.ALIPAY.getCode()
                    );
                }

                vipRefundApplyRequest.setRefundAccount(vipCardRefundApply.getOfflineAccount());
                vipRefundApplyRequest.setRefundReason(vipCardRefundApply.getReason());

                if (vipCardRefundApply.getAmount() != null) {
                    vipRefundApplyRequest.setRefundAmount(vipCardRefundApply.getAmount().multiply(new BigDecimal("100")).intValue());
                }
                vipRefundApplyRequest.setOperator(employeeService.getUserNameForIdentify(UserContextHolder.getUserIdentify()));
                vipRefundApplyRequest.setHoldHour(2);

                RefundApplyResultAdminDTO refundApplyResDto = vipFacadeClient.createVipRefundApply(vipRefundApplyRequest);
                if (refundApplyResDto == null || !refundApplyResDto.getSuccess()) {
                    refundApplyDto.setSuccess(false);
                    message.append(" vipCardId: ").append(vipCardRefundApply.getVipCardId()).append(" 失败原因： ").append(refundApplyResDto == null ? "response is null" : refundApplyResDto.getMessage());
                }
            } else {
                VipRefundApplyRequest refundApplyRequest = VipCardConverter.INSTANCE.refundRequestToRefundApply(vipCardRefundApply);
                if (vipCardRefundApply.getAmount() != null) {
                    refundApplyRequest.setAmount(vipCardRefundApply.getAmount().multiply(new BigDecimal("100")).intValue());
                }
                refundApplyRequest.setOperator(employeeService.getUserNameForIdentify(UserContextHolder.getUserIdentify()));
                refundApplyRequest.setHoldHour(2);

                RefundApplyResDto refundApplyResDto = vipFacadeClient.vipCardRefundApply(refundApplyRequest);
                if (refundApplyResDto == null || !refundApplyResDto.getSuccess()) {
                    refundApplyDto.setSuccess(false);
                    message.append(" vipCardId: ").append(vipCardRefundApply.getVipCardId()).append(" 失败原因： ").append(refundApplyResDto == null ? "response is null" : refundApplyResDto.getMessage());
                }
            }
        }

        if (!refundApplyDto.getSuccess()) {
            refundApplyDto.setMessage(message.toString());
            refundApplyDto.setSuccess(false);
        }

        return ApiResponse.success(refundApplyDto);
    }

    @Override
    public VipOpsResultAdminDTO reduceApply(ReduceApplyRequest request) {
        String creator = employeeService.getUserNameForIdentify(UserContextHolder.getUserIdentify());
        return vipFacadeClient.createVipReduceApply(request.getCardId(), creator, request.getReduceAmount().multiply(new BigDecimal("100")));
    }

    @Override
    public VipOpsResultAdminDTO cancelVipReduceApply(CancelVipReduceApplyRequest request) {
        String creator = employeeService.getUserNameForIdentify(UserContextHolder.getUserIdentify());
        return vipFacadeClient.cancelVipReduceApply(request.getVipReduceId(), creator);
    }

    @Override
    public RefundStartDto vipCardRefundStart(VipCardRefundApplyStart request) {
        if (request.getType() == 4 && vocConfig.getIsSuperVipCard()) {
            ExecuteVipRefundApplyAdminRequest refundApplyRequest = new ExecuteVipRefundApplyAdminRequest();
            refundApplyRequest.setApplyId(request.getApplyId());
            refundApplyRequest.setOperator(employeeService.getUserNameForIdentify(UserContextHolder.getUserIdentify()));
            RefundApplyExecuteResultAdminDTO resultDTO = vipFacadeClient.executeVipRefundApply(refundApplyRequest);
            RefundStartDto refundStartDto = new RefundStartDto();
            if (resultDTO != null) {
                refundStartDto.setSuccess(resultDTO.getSuccess());
                refundStartDto.setMessage(resultDTO.getMessage());
            }
            return refundStartDto;
        } else {
            VipRefundApplyStartRequest refundApplyRequest = new VipRefundApplyStartRequest();
            refundApplyRequest.setApplyId(request.getApplyId());
            return vipFacadeClient.vipCardRefundStart(refundApplyRequest);
        }
    }

    @Override
    public RefundStartDto vipCardRefundCancel(VipCardRefundApplyStart request) {
        if (request.getType() == 4 && vocConfig.getIsSuperVipCard()) {
            CancelVipRefundApplyAdminRequest refundApplyRequest = new CancelVipRefundApplyAdminRequest();
            refundApplyRequest.setApplyId(request.getApplyId());
            refundApplyRequest.setOperator(employeeService.getUserNameForIdentify(UserContextHolder.getUserIdentify()));
            RefundApplyCancelResultAdminDTO resultDTO = vipFacadeClient.cancelVipRefundApply(refundApplyRequest);
            RefundStartDto refundStartDto = new RefundStartDto();
            if (resultDTO != null) {
                refundStartDto.setSuccess(resultDTO.getSuccess());
                refundStartDto.setMessage(resultDTO.getMessage());
            }
            return refundStartDto;
        } else {
            VipRefundApplyStartRequest refundApplyRequest = new VipRefundApplyStartRequest();
            refundApplyRequest.setApplyId(request.getApplyId());
            return vipFacadeClient.vipCardRefundCancel(refundApplyRequest);
        }
    }

    @Override
    public Map<String, List<CardRefund>> queryRightCardRefundList(VipCardRefundLogReq request) {
        Map<String, List<CardRefund>> map = new HashMap<>();
        for (VipCardRefundLog req : request.getVipCardRefundLogList()) {
            RightCardRefundRecordDto rightCardRefundRecordDto;
            if (req.getCardType() == 4 && vocConfig.getIsSuperVipCard()) {
                //飞跃会员
                RefundListAdminDTO refundListDto = vipFacadeClient.queryVipRefundList(req.getVipCardId());
                rightCardRefundRecordDto = VipCardConverter.INSTANCE.refundRecordVipCoreListToRefundDto(refundListDto);

                RefundApplyListAdminDTO refundApplyListDto = vipFacadeClient.queryVipRefundApplyList(req.getVipCardId());
                if (rightCardRefundRecordDto != null) {
                    applyVipHandle(refundApplyListDto, rightCardRefundRecordDto);
                }
            } else {
                //飞享会员
                RefundListDto refundListDto = vipFacadeClient.vipCardRefundList(req.getVipCardId(), req.getCardType());
                rightCardRefundRecordDto = VipCardConverter.INSTANCE.refundRecordListToRefundDto(refundListDto);

                RefundApplyListDto refundApplyListDto = vipFacadeClient.vipCardRefundApplyList(req.getVipCardId(), req.getCardType());
                if (rightCardRefundRecordDto != null) {
                    applyHandle(refundApplyListDto, rightCardRefundRecordDto);
                }
            }
            if (org.apache.commons.collections.CollectionUtils.isNotEmpty(rightCardRefundRecordDto.getList())) {
                rightCardRefundRecordDto.getList().forEach(r -> {
                    if (r.getAmount() != null) {
                        r.setAmount(r.getAmount().multiply(new BigDecimal("0.01")));
                    }

                    if (StringUtils.isNotBlank(r.getOperator()) && r.getOperator().length() >= 16) {
                        r.setOperator(cisFacadeClientService.getDecryptByField("name", Collections.singletonList(r.getOperator())));
                    }

                });
            }

            List<CardRefund> cardRefunds;
            if (!CollectionUtils.isEmpty(request.getStatus()) && !CollectionUtils.isEmpty(rightCardRefundRecordDto.getList())) {
                cardRefunds = rightCardRefundRecordDto.getList().stream().filter(r -> request.getStatus().contains(r.getStatus())).collect(Collectors.toList());
            } else {
                cardRefunds = rightCardRefundRecordDto.getList();
            }
            map.put(req.getVipCardId().toString(), cardRefunds);
        }

        return map;
    }

    private void applyHandle(RefundApplyListDto refundApplyListDto, RightCardRefundRecordDto rightCardRefundRecordDto) {
        List<String> refundFlowNumbers = rightCardRefundRecordDto.getList().stream().map(CardRefund::getRefundFlowNumber).filter(s -> !s.isEmpty()).collect(Collectors.toList());
        for (RefundApplyDto refundApplyDto : refundApplyListDto.getList()) {
            if (refundFlowNumbers.contains(refundApplyDto.getRefundFlowNumber())) {
                continue;
            }
            CardRefund cardRefundDto = new CardRefund();
            if (rightCardRefundRecordDto.getList().size() > 0) {
                cardRefundDto.setCardName(rightCardRefundRecordDto.getList().get(0).getCardName());
            }
            cardRefundDto.setVipCardId(refundApplyDto.getVipCardId());
            cardRefundDto.setApplyId(refundApplyDto.getId());
            cardRefundDto.setOrderNumber(refundApplyDto.getVipOrderNumber());
            cardRefundDto.setVipCardType(refundApplyDto.getCardType());
            cardRefundDto.setRefundFlowNumber(refundApplyDto.getRefundFlowNumber());
            cardRefundDto.setAmount(BigDecimal.valueOf(refundApplyDto.getAmount()));
            cardRefundDto.setStatus(VipCardConverter.INSTANCE.mapStatusTo(refundApplyDto.getStatus()));
            cardRefundDto.setIsRefundCancel(refundApplyDto.getStatus() == 1);
            cardRefundDto.setOperator(refundApplyDto.getOperator());
            cardRefundDto.setReason(refundApplyDto.getReason());
            cardRefundDto.setCreateTime(refundApplyDto.getCreateTime());
            cardRefundDto.setRefundAccountType(refundApplyDto.getOfflineAccountType());
            cardRefundDto.setRefundAccount(refundApplyDto.getOfflineAccount());
            cardRefundDto.setIsRefundApply(true);
            cardRefundDto.setRefundApplyChannel(refundApplyDto.getRefundApplyChannel());
            rightCardRefundRecordDto.getList().add(cardRefundDto);
        }
    }

    private void applyVipHandle(RefundApplyListAdminDTO refundApplyListDto, RightCardRefundRecordDto rightCardRefundRecordDto) {
        List<String> refundFlowNumbers = rightCardRefundRecordDto.getList().stream().map(CardRefund::getApplyFlowNo).collect(Collectors.toList());
        //遍历申请记录，将没有发起退款的申请记录加入退款记录
        for (RefundApplyAdminDTO refundApplyDto : refundApplyListDto.getList()) {
            if (refundFlowNumbers.contains(refundApplyDto.getApplyFlowNo())) {
                continue;
            }
            CardRefund cardRefundDto = new CardRefund();
            cardRefundDto.setCardName(refundApplyDto.getVipCardName());
            cardRefundDto.setApplyId(refundApplyDto.getId());
            cardRefundDto.setOrderNumber(refundApplyDto.getVipOrderNo());
            cardRefundDto.setVipCardType(VipCardConverter.INSTANCE.vipCardTypeConvert(refundApplyDto.getVipType()));
            cardRefundDto.setRefundFlowNumber(refundApplyDto.getApplyFlowNo());
            cardRefundDto.setAmount(BigDecimal.valueOf(refundApplyDto.getRefundAmount()));
            cardRefundDto.setStatus(VipCardConverter.INSTANCE.mapStatusToNumber(refundApplyDto.getApplyStatus()));
            cardRefundDto.setIsRefundCancel(VipCardConverter.INSTANCE.isRefundCancel(refundApplyDto.getApplyStatus()));
            cardRefundDto.setOperator(refundApplyDto.getApplicant());
            cardRefundDto.setReason(refundApplyDto.getRefundReason());
            cardRefundDto.setCreateTime(refundApplyDto.getCreateTime());
            cardRefundDto.setRefundAccountType(VipCardConverter.INSTANCE.vipCardRefundAccountTypeConvert(refundApplyDto.getRefundAccountType()));
            cardRefundDto.setRefundAccount(refundApplyDto.getRefundAccount());
            cardRefundDto.setIsRefundApply(true);
            cardRefundDto.setRefundApplyChannel(VipCardConverter.INSTANCE.vipCardRefundApplyChannelConvert(refundApplyDto.getRefundChannel()));
            cardRefundDto.setRefundBankName(refundApplyDto.getRefundAccountBank());
            cardRefundDto.setVipCardId(refundApplyDto.getVipOrderId());
            rightCardRefundRecordDto.getList().add(cardRefundDto);
        }
    }

    @Override
    public Boolean queryVipBlack(VipCardBlackReq request) {
        try {
            VipBlackRequest vipBlackRequest = new VipBlackRequest();
            vipBlackRequest.setMobile(request.getMobile());
            MemberInterestResponse<VipBlackRecords> response = memberInterestFeignClient.queryVipBlack(vipBlackRequest);
            if (Objects.isNull(response) || Objects.isNull(response.getData()) || !response.isSuccess()) {
                log.warn("queryVipBlack list failed, request={},response={}", request, response);
                return false;
            }
            if (CollectionUtils.isEmpty(response.getData().getRecords())) {
                return false;
            }
            return true;
        } catch (Exception e) {
            log.warn("queryVipBlack  failed, request=" + request, e);
            return false;
        }
    }


    @Override
    public Boolean vipBlack(VipCardBlackReq request) {
        try {
            VipBlackRequest vipBlackRequest = new VipBlackRequest();
            vipBlackRequest.setMobile(request.getMobile());
            vipBlackRequest.setOperatorId(request.getOperatorId());
            vipBlackRequest.setOperatorName(request.getOperatorName());
            MemberInterestResponse<VipBlackRecords> response = memberInterestFeignClient.vipBlack(vipBlackRequest);
            if (Objects.isNull(response) || Objects.isNull(response.getData()) || !response.isSuccess()) {
                log.warn("vipBlack failed, request={},response={}", request, response);
                return false;
            }
            return response.getData().getSuccess();
        } catch (Exception e) {
            log.warn("vipBlack failed, request=" + request, e);
            return false;
        }
    }

    @Override
    public void refundVipFailed(RefundResultMsg request, SVipRefundResultMsg sVipRefundResultMsg) {
        log.info("refundVipFailedMsg={}", JsonUtil.toJson(request));
        CreateTaskDto createTaskDto = new CreateTaskDto();
        createTaskDto.setIsCustomize(true);
        createTaskDto.setCustomizeSceneId(refundType);
        createTaskDto.setCustomizeChannel(subRefundType);
        createTaskDto.setTaskTypeId(subRefundType);
        createTaskDto.setQuestionTypeId(0);
        createTaskDto.setEmergencyStatus(1);
        Long userNo = null;

        if (request != null) {
            //飞享会员退款失败消息处理
            createTaskDto.setVipOrderNumber(request.getOrderNumber());
            if (request.getUserNo() != null) {
                createTaskDto.setUserId(String.valueOf(request.getUserNo()));
                userNo = request.getUserNo();
            }
            createTaskDto.setRefundAmount(new BigDecimal(request.getAmount()).divide(new BigDecimal("100")));
            if (request.getRefundAccountType() != null) {
                String entryMethod = "";
                if (request.getRefundAccountType() == 1) {
                    entryMethod = "银行卡";
                } else if (request.getRefundAccountType() == 2) {
                    entryMethod = "支付宝";
                } else if (request.getRefundAccountType() == 3) {
                    entryMethod = "线下退";
                } else if (request.getRefundAccountType() == 4) {
                    entryMethod = "微信";
                }
                createTaskDto.setEntryMethod(entryMethod);
            }
            createTaskDto.setAccountCredited(request.getRefundAccount());
            createTaskDto.setComment(request.getReason());

        } else if (sVipRefundResultMsg != null) {
            //飞跃会员退款失败消息处理
            createTaskDto.setVipOrderNumber(sVipRefundResultMsg.getVipOrderNo());
            if (sVipRefundResultMsg.getUserNo() != null) {
                createTaskDto.setUserId(sVipRefundResultMsg.getUserNo());
                userNo = Long.parseLong(sVipRefundResultMsg.getUserNo());
            }
            createTaskDto.setRefundAmount(new BigDecimal(sVipRefundResultMsg.getAmount()).divide(new BigDecimal("100")));
            if (sVipRefundResultMsg.getRefundAccountType() != null) {
                String entryMethod = "";
                if ("debit_card".equals(sVipRefundResultMsg.getRefundAccountType())) {
                    entryMethod = "银行卡";
                } else if ("alipay".equals(sVipRefundResultMsg.getRefundAccountType())) {
                    entryMethod = "支付宝";
                } else if ("wechat".equals(sVipRefundResultMsg.getRefundAccountType())) {
                    entryMethod = "微信";
                }
                createTaskDto.setEntryMethod(entryMethod);
            }
            createTaskDto.setAccountCredited(sVipRefundResultMsg.getRefundAccount());
            createTaskDto.setComment(sVipRefundResultMsg.getRefundReason());
        }

        try {
            if (userNo != null) {
                ThreeElementsDTO threeElementsDTO = cisFacadeClient.queryThreeElementsByUserNo(userNo);
                createTaskDto.setIdCard(threeElementsDTO.getIdNo());
                createTaskDto.setCallNumber(threeElementsDTO.getMobile());
            }
        } catch (Exception e) {
            log.warn("MemberInterestRemoteServiceImpl queryThreeElementsByUserNo failed");
        }
        workOrderFeignClient.createTask(createTaskDto);
    }

    @Override
    public List<VipRefundDetailDto> vipRefundDetail(List<VipRefundDetailReq> request) {
        List<VipRefundDetailDto> refundDetailDtoList = new ArrayList<>();
        for (VipRefundDetailReq vipRefundDetailReq : request) {
            VipRefundDetailDto vipRefundDetailDto = new VipRefundDetailDto();
            try {

                if (vipRefundDetailReq.getCardType() == 4 && vocConfig.getIsSuperVipCard()) {
                    QueryOriginalRefundAccountAdminRequest orderPayAccountRequest = new QueryOriginalRefundAccountAdminRequest();
                    orderPayAccountRequest.setVipOrderId(Long.parseLong(vipRefundDetailReq.getVipCardId()));
                    orderPayAccountRequest.setVipType(VipTypeEnum.FEI_YUE.getCode());
                    OriginalRefundAccountAdminDTO payAccountDTO = vipFacadeClient.queryOriginalRefundAccount(orderPayAccountRequest);
                    if (payAccountDTO == null) {
                        continue;
                    }
                    /**
                     * 赋值原路原退时需要展示的退款账户信息
                     */
                    if (payAccountDTO.getRefundAccountType() != null) {
                        if (RefundAccountTypeEnum.DEBIT_CARD.getCode().equals(payAccountDTO.getRefundAccountType()) && payAccountDTO.getPayOrderBankDTO() != null) {
                            vipRefundDetailDto.setRefundAccountType(1);
                            vipRefundDetailDto.setRefundAccount(payAccountDTO.getPayOrderBankDTO().getTailBankNo());
                            vipRefundDetailDto.setBankName(payAccountDTO.getPayOrderBankDTO().getBankName());
                        } else if (RefundAccountTypeEnum.ALIPAY.getCode().equals(payAccountDTO.getRefundAccountType())) {
                            vipRefundDetailDto.setRefundAccountType(2);
                            vipRefundDetailDto.setRefundAccount(payAccountDTO.getRefundAccount());
                        } else if (RefundAccountTypeEnum.WECHAT.getCode().equals(payAccountDTO.getRefundAccountType())) {
                            vipRefundDetailDto.setRefundAccountType(4);
                        }
                    }

                    /**
                     * 赋值退款方式，若为2，则只能线下退
                     */
                    if (RefundChannelEnum.OFFLINE_REFUND.getCode().equals(payAccountDTO.getRefundChannel())) {
                        vipRefundDetailReq.setRefundChannel(2);
                    } else if (RefundChannelEnum.ORIGINAL_REFUND.getCode().equals(payAccountDTO.getRefundChannel())) {
                        vipRefundDetailReq.setRefundChannel(1);
                    }

                    BeanUtils.copyProperties(vipRefundDetailReq, vipRefundDetailDto);
                    refundDetailDtoList.add(vipRefundDetailDto);
                } else {
                    OrderPayAccountRequest orderPayAccountRequest = new OrderPayAccountRequest();
                    orderPayAccountRequest.setVipCardId(vipRefundDetailReq.getVipCardId());
                    orderPayAccountRequest.setCardType(vipRefundDetailReq.getCardType());
                    PayAccountDTO payAccountDTO = vipFacadeClient.orderPayAccount(orderPayAccountRequest);

                    /**
                     * 赋值原路原退时需要展示的退款账户信息
                     */
                    if (payAccountDTO.getRefundAccountType() != null) {
                        if (payAccountDTO.getRefundAccountType() == 1 && payAccountDTO.getPayOrderBankDTO() != null) {
                            vipRefundDetailDto.setRefundAccountType(payAccountDTO.getRefundAccountType());
                            vipRefundDetailDto.setRefundAccount(payAccountDTO.getPayOrderBankDTO().getTailBankNo());
                            vipRefundDetailDto.setBankName(payAccountDTO.getPayOrderBankDTO().getBankName());
                        } else if (payAccountDTO.getRefundAccountType() == 2) {
                            vipRefundDetailDto.setRefundAccountType(payAccountDTO.getRefundAccountType());
                            vipRefundDetailDto.setRefundAccount(payAccountDTO.getAliAccount());
                        } else if (payAccountDTO.getRefundAccountType() == 4) {
                            vipRefundDetailDto.setRefundAccountType(payAccountDTO.getRefundAccountType());
                        }
                    }

                    /**
                     * 赋值退款方式，若为2，则只能线下退
                     */
                    vipRefundDetailReq.setRefundChannel(payAccountDTO.getRefundChannel());

                    BeanUtils.copyProperties(vipRefundDetailReq, vipRefundDetailDto);
                    refundDetailDtoList.add(vipRefundDetailDto);
                }
            } catch (Exception e) {
                vipRefundDetailDto.setReason(e.getMessage());
                BeanUtils.copyProperties(vipRefundDetailReq, vipRefundDetailDto);
                refundDetailDtoList.add(vipRefundDetailDto);
            }
        }
        refundDetailDtoList.sort((dto1, dto2) ->
                Boolean.compare(dto2.getRefundChannel().equals(2), dto1.getRefundChannel().equals(2))
        );
        return refundDetailDtoList;
    }

    private String validateVipCardRefundApply(VipCardRefundApply vipCardRefundApply) {
        if (vipCardRefundApply == null) {
            return "param is required.";
        }

        if (vipCardRefundApply.getOfflineAccountType() != null) {
            if (StringUtils.isBlank(vipCardRefundApply.getOfflineAccount())) {
                return "线下退款账号:offlineAccount 不能为空";
            } else if (vipCardRefundApply.getOfflineAccountType() == 1 && StringUtils.isBlank(vipCardRefundApply.getOfflineAccountBankName())) {
                return "线下退款为银行卡:银行卡名称offlineAccountBankName不能为空";
            }
        }
        return null;
    }

    @Override
    @DataPermission
    public List<VipOrderPayLogDetailDTO> queryOrderPayLog(Long orderId) {
        if (!vocConfig.getIsSuperVipCard()) {
            return Collections.emptyList();
        }
        // 调用外部系统接口获取支付记录
        List<VipOrderPayLogAdminDTO> externalDTOList = vipFacadeClient.queryVipOrderPayLogList(orderId);
        if (CollectionUtils.isEmpty(externalDTOList)) {
            return Collections.emptyList();
        }
        return VipOrderPayLogConverter.INSTANCE.convertToDetailDTOList(externalDTOList);
    }

    @Override
    public VipOrderReduceDetailAdmin reduceDetail(Long vipReduceId) {
        VipOrderReduceDetailAdminDTO vipOrderReduceDetailDTO = vipFacadeClient.queryVipReduceDetail(vipReduceId);
        return VipCardConverter.INSTANCE.vipOrderReduceDetailDtoToVipOrderReduceDetailAdmin(vipOrderReduceDetailDTO);
    }

    @Override
    public PageResultResponse<VipOrderReducePriceApplyAdmin> queryVipReduceList(CardDiscountListRequest cardDiscountListRequest) {
        PageResultResponse<VipOrderReducePriceApplyAdmin> pageResultResponse = new PageResultResponse<>();
        pageResultResponse.setTotal(0);
        pageResultResponse.setPageSize(0);
        pageResultResponse.setCurrentPage(0);

        QueryVipReduceListAdminRequest request = new QueryVipReduceListAdminRequest();
        request.setPageSize(cardDiscountListRequest.getPageSize());
        request.setPageNo(cardDiscountListRequest.getCurrentPage());

        if (cardDiscountListRequest.getPlanStatus() != null) {
            if (cardDiscountListRequest.getPlanStatus() == 1) {
                request.setApplyStatusList(Collections.singletonList(ReduceApplyStatusEnum.ENABLE.getCode()));
            } else if (cardDiscountListRequest.getPlanStatus() == 2) {
                request.setApplyStatusList(Collections.singletonList(ReduceApplyStatusEnum.DISABLED.getCode()));
            } else if (cardDiscountListRequest.getPlanStatus() == 3) {
                request.setApplyStatusList(Collections.singletonList(ReduceApplyStatusEnum.USED.getCode()));
            } else {
                return pageResultResponse;
            }
        }

        request.setVipReduceId(cardDiscountListRequest.getPlanId());
        request.setVipOrderId(cardDiscountListRequest.getCardId());
        request.setVipOrderNo(cardDiscountListRequest.getOrderNo());
        request.setCreateStartTime(cardDiscountListRequest.getCreatedTimeStart());
        request.setCreateEndTime(cardDiscountListRequest.getCreatedTimeEnd());

        List<String> userListMobile = null;
        if (StringUtils.isNotBlank(cardDiscountListRequest.getMobile())) {
            List<UserSearchDTO> needUserSearch = new ArrayList<>();
            PageResult<UserSearchDTO> pageResult;
            int n = 1;
            do {
                pageResult = cisFacadeClient.queryUserList(cardDiscountListRequest.getMobile(), null, null, n, 30);
                if (org.apache.commons.collections.CollectionUtils.isEmpty(pageResult.getList())) {
                    break;
                }
                needUserSearch.addAll(pageResult.getList());
                n++;
            } while (true);

            List<UserSearchDTO> custNoUsers = commonService.getUserByMobileAndCust(cardDiscountListRequest.getMobile());
            if (org.apache.commons.collections.CollectionUtils.isNotEmpty(custNoUsers)) {
                needUserSearch.addAll(custNoUsers);
            }
            if (needUserSearch.isEmpty()) {
                return null;
            }
            userListMobile = needUserSearch.stream()
                    .map(user -> user.getUserNo().toString())
                    .distinct()
                    .collect(Collectors.toList());
        }

        List<String> userListCustNo = null;
        if (StringUtils.isNotBlank(cardDiscountListRequest.getCustNo())) {
            PageResult<UserSearchDTO> pageResult = cisFacadeClient.queryUserList(null, cardDiscountListRequest.getCustNo(), null, 1, 30);
            userListCustNo = pageResult.getList().stream()
                    .map(user -> user.getUserNo().toString())
                    .distinct()
                    .collect(Collectors.toList());
        }

        List<String> userListUserNo = null;
        if (StringUtils.isNotBlank(cardDiscountListRequest.getUserNo())) {
            userListUserNo = Collections.singletonList(cardDiscountListRequest.getUserNo());
        }

        List<List<String>> allUserLists = new ArrayList<>();
        if (userListMobile != null) {
            allUserLists.add(userListMobile);
        }
        if (userListCustNo != null) {
            allUserLists.add(userListCustNo);
        }
        if (userListUserNo != null) {
            allUserLists.add(userListUserNo);
        }

        if (allUserLists.isEmpty()) {
            //一个搜索条件都没有
        } else if (allUserLists.size() == 1) {
            //只有一个搜索条件
            request.setUserNoList(allUserLists.get(0));
            if (CollectionUtils.isEmpty(request.getUserNoList())) {
                return pageResultResponse;
            }
        } else {
            // 如果有2个或3个搜索条件，计算交集
            // 使用第一个列表初始化交集结果，并转换为 HashSet 以提高性能
            Set<String> intersectionResult = new HashSet<>(allUserLists.get(0));

            // 从第二个列表开始，依次取交集
            for (int i = 1; i < allUserLists.size(); i++) {
                // retainAll 方法会修改 set, 只保留也存在于另一个集合中的元素
                intersectionResult.retainAll(new HashSet<>(allUserLists.get(i)));
            }

            // 将最终的交集结果转换回 List 并返回
            request.setUserNoList(new ArrayList<>(intersectionResult));
            if (CollectionUtils.isEmpty(request.getUserNoList())) {
                return pageResultResponse;
            }
        }

        PageAdminDTO<VipOrderReducePriceApplyAdminDTO> pageAdminDTO = vipFacadeClient.queryVipReduceList(request);

        List<VipOrderReducePriceApplyAdmin> vipOrderReducePriceApplyAdmins = new ArrayList<>();

        if (pageAdminDTO != null && !CollectionUtils.isEmpty(pageAdminDTO.getList())) {
            pageAdminDTO.getList().forEach(dto -> {
                VipOrderReducePriceApplyAdmin vipOrderReducePriceApplyAdmin = VipCardConverter.INSTANCE.vipOrderReducePriceApplyDtoToVipOrderReducePriceApplyAdmin(dto);
                vipOrderReducePriceApplyAdmin.setPlanSource(PlanSourceEnum.VOCMNG.getCode());
                vipOrderReducePriceApplyAdmin.setSolutionType("会员卡");
                if (StringUtils.isNotBlank(vipOrderReducePriceApplyAdmin.getUserNo())) {
                    ThreeElementsDTO threeElementsDTO = cisFacadeClient.queryThreeElementsByUserNo(Long.parseLong(vipOrderReducePriceApplyAdmin.getUserNo()));
                    vipOrderReducePriceApplyAdmin.setMobile(threeElementsDTO.getMobile());
                }
                vipOrderReducePriceApplyAdmins.add(vipOrderReducePriceApplyAdmin);
            });

            pageResultResponse.setList(vipOrderReducePriceApplyAdmins);
            pageResultResponse.setTotal(pageAdminDTO.getTotal());
            pageResultResponse.setPageSize(pageAdminDTO.getPageSize());
            pageResultResponse.setCurrentPage(pageAdminDTO.getPageNo());
        }

        return pageResultResponse;
    }


    @Override
    public CardDeductionCalculationDto cardDeductionCalculation(CardDeductionCalculationRequest cardDeductionCalculationRequest) {
        CardDeductionCalculationDto cardDeductionCalculationDto = new CardDeductionCalculationDto();
        BigDecimal fee = cardDeductionCalculationRequest.getAmount().multiply(cardDeductionCalculationRequest.getRate())
                .divide(new BigDecimal("100"), 2, RoundingMode.HALF_UP);
        cardDeductionCalculationDto.setReductionAmount(fee);
        return cardDeductionCalculationDto;
    }
}
