/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xinfei.vocmng.biz.model.req;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @version $ OrderRefundApplyRecordsReq, v 0.1 2024-05-21 21:00 junjie.yan Exp $
 */
@Data
public class OrderRefundRecordsResp {

    @ApiModelProperty("退款指令号")
    private String refundInstructionNo;

    @ApiModelProperty("退款单号")
    private String refundOrderId;

    @ApiModelProperty("借据号")
    private String loanNo;

    @ApiModelProperty("退款金额")
    private BigDecimal refundAmt;

    @ApiModelProperty("退款方式-线下退款/原路退回")
    private String refundType;

    @ApiModelProperty("线下退款客户名")
    private String offlineRefundUserName;

    @ApiModelProperty("线下退款类型-zfb支付宝,bankcard银行卡")
    private String offlineRefundPayType;

    @ApiModelProperty("线下退款账号")
    private String offlineRefundAccount;

    @ApiModelProperty("线下退款银行")
    private String offlineRefundBank;

    @ApiModelProperty("退款客户名")
    private String refundUserName;

    @ApiModelProperty("退款账号")
    private String refundAccount;

    @ApiModelProperty("退款银行")
    private String refundBank;

    @ApiModelProperty("创建时间")
    private LocalDateTime createdTime;

    @ApiModelProperty("退款发起时间")
    private LocalDateTime initiateTime;

    @ApiModelProperty("退款完成时间")
    private LocalDateTime endTime;

    @ApiModelProperty("退款原因")
    private String refundReason;

    @ApiModelProperty("退款失败原因")
    private String failedReason;

    @ApiModelProperty("明细退款状态 PENDING:待发起,PROCESSING:退款中,SUCCESS:成功,FAILURE:失败,CANCEL:取消,REJECT:拒绝")
    private String refundStatus;

    @ApiModelProperty("用户号")
    private String custNo;

    @ApiModelProperty("第三方退款渠道")
    private String channelCode;

    @ApiModelProperty("第三方退款渠道流水号")
    private String channelOrderNo;

    @ApiModelProperty("创建人")
    private String createdBy;
}