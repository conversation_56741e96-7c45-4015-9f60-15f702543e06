/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2023 All Rights Reserved.
 */
package com.xinfei.vocmng.biz.util;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.LocalDateTimeUtil;
import org.apache.commons.lang3.StringUtils;

import java.time.*;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.util.Date;

/**
 * <AUTHOR>
 * @version $ LocalDateTimeUtil, v 0.1 2023/11/13 11:41 junjie.yan Exp $
 */

public class LocalDateTimeUtils {
    /**
     * 间隔（分钟）
     */
    public static final int INTERVAL = 3;

    public static final ZoneId ZONE_ID = ZoneId.of("Asia/Shanghai");

    /**
     * 中国时区（+8）
     */
    public static final ZoneOffset ZONE_OFFSET = ZoneOffset.of("+8");

    public static final DateTimeFormatter FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

    public static final DateTimeFormatter FORMATTERDATE = DateTimeFormatter.ofPattern("yyyy-MM-dd");

    public static final DateTimeFormatter FORMATTER_T = DateTimeFormatter.ISO_LOCAL_DATE_TIME;

    public static final String regexDateTime = "^\\d{4}-\\d{2}-\\d{2} \\d{2}:\\d{2}:\\d{2}$";

    public static final String regexDate = "^\\d{4}-\\d{2}-\\d{2}$";


    /**
     * Date对象转LocalDateTime对象
     *
     * @param date Date对象
     * @return LocalDateTime对象
     */
    public static LocalDateTime toLocalDateTime(Date date) {
        return date.toInstant()
                .atZone(ZONE_ID)
                .toLocalDateTime();
    }

    /**
     * Date对象转LocalDateTime对象
     *
     * @param datetimeStr eg: yyyy-mm-dd HH:mm:ss
     * @return LocalDateTime对象
     */
    public static LocalDateTime toLocalDateTime(String datetimeStr) {
        return LocalDateTimeUtil.parse(datetimeStr);
    }

    /**
     * 格式化日期时间为yyyy-MM-dd HH:mm:ss格式
     *
     * @param localDateTime LocalDateTime对象
     * @return 时间字符串
     */
    public static String format(LocalDateTime localDateTime, DateTimeFormatter dateTimeFormatter) {
        return LocalDateTimeUtil.format(localDateTime, dateTimeFormatter);
    }

    /**
     * 格式化日期时间为yyyy-MM-dd HH:mm:ss格式
     *
     * @param localDateTime LocalDateTime对象
     * @return 时间字符串
     */
    public static String format(LocalDateTime localDateTime, String format) {
        return LocalDateTimeUtil.format(localDateTime, format);
    }

    /**
     * 格式化日期时间为yyyy-MM-dd HH:mm:ss格式
     *
     * @param localDateTime LocalDateTime对象
     * @return 时间字符串
     */
    public static String format(LocalDateTime localDateTime) {
        return format(localDateTime, DatePattern.NORM_DATETIME_FORMATTER);
//        return LocalDateTimeUtil.formatNormal(localDateTime);
    }

    /**
     * 求两个时间相差值
     *
     * @param start 开始LocalDateTime对象
     * @param end   结束LocalDateTime
     * @return Duration 期间
     */
    public static Duration between(LocalDateTime start, LocalDateTime end) {
        return LocalDateTimeUtil.between(start, end);
    }


    /**
     * 获取时间戳
     *
     * @param now 时间
     * @return long 时间戳
     */
    public static long getTimeStamp(LocalDateTime now) {
        return now.toEpochSecond(ZONE_OFFSET);
    }

    /**
     * 获取时间戳（时间倒退@INTERVAL分钟）
     *
     * @param now 时间
     * @return long 时间戳
     */
    public static long getStartTimestamp(LocalDateTime now) {
        return now.minusMinutes(INTERVAL).toEpochSecond(ZONE_OFFSET);
    }

    /**
     * 获取当天的开始时间戳
     *
     * @param now 日期时间
     * @return long 时间戳
     */
    public static long getCurrentStartTimestamp(LocalDateTime now) {
        return LocalDateTime.of(now.getYear(), now.getMonth(), now.getDayOfMonth(), LocalDateTime.MIN.getHour(),
                LocalDateTime.MIN.getMinute(), LocalDateTime.MIN.getSecond()).toEpochSecond(ZONE_OFFSET);
    }

    /**
     * 获取当天结束的时间戳
     *
     * @param now 日期时间
     * @return long
     */
    public static long getCurrentEndTimestamp(LocalDateTime now) {
        return LocalDateTime.of(now.getYear(), now.getMonth(), now.getDayOfMonth(),
                        LocalDateTime.MAX.getHour(), LocalDateTime.MAX.getMinute(), LocalDateTime.MAX.getSecond())
                .toEpochSecond(ZONE_OFFSET);
    }

//    /**
//     * 获取当天的开始的LocalDateTime
//     *
//     * @param now 日期时间
//     * @return long 时间戳
//     */
//    public static LocalDateTime getCurrentStartLocalDateTime(LocalDateTime now) {
//        LocalDate localDate = now.toLocalDate();
//        return localDate.atTime(LocalTime.MIN.getHour(), LocalTime.MIN.getMinute(), LocalTime.MIN.getSecond());
//    }
//
//    /**
//     * 获取当天结束的LocalDateTime
//     *
//     * @param now 日期时间
//     * @return long
//     */
//    public static LocalDateTime getCurrentEndLocalDateTime(LocalDateTime now) {
//        LocalDate localDate = now.toLocalDate();
//        return localDate.atTime(LocalTime.MAX.getHour(), LocalTime.MAX.getMinute(), LocalTime.MAX.getSecond());
//    }
    // -------------------------------------------- 转换 --------------------------------------------

    /**
     * 字符串日期转换成java.util.Date类型
     *
     * @param dateStr 字符串日期 2018-06-01 10:12:05
     * @return java.util.Date
     */
    public static Date parseDateByDateStr(String dateStr) {
        return java.sql.Date.from(LocalDateTime.parse(dateStr, FORMATTER).toInstant(ZONE_OFFSET));
    }

    /**
     * java.util.Date类型 转成 LocalDateTime类型
     *
     * @param date 日期
     * @return LocalDateTime
     */
    public static LocalDateTime parseLocalDateTimeByDate(Date date) {
        // Java Date类的 toInstant()方法将该日期对象转换为Instant，然后返回Instant表示与日期对象在tim线上的同一点
        Instant instant = date.toInstant();
        return LocalDateTime.ofInstant(instant, ZONE_OFFSET);
    }

    public static LocalDate parseLocalDateByDate(Date date) {
        // Java Date类的 toInstant()方法将该日期对象转换为Instant，然后返回Instant表示与日期对象在tim线上的同一点
        Instant instant = date.toInstant();
        return instant.atZone(ZONE_OFFSET).toLocalDate();
    }

    public static LocalDateTime parseLocalDateTimeByDateStr(String dateStr) {
        return LocalDateTime.parse(dateStr, FORMATTER);
    }

    public static LocalDateTime parseLocalDateTimeByDateStrT(String dateStr) {
        return LocalDateTime.parse(dateStr, FORMATTER_T);
    }

    public static LocalDate parseLocalDateByDateStr(String dateStr) {
        return LocalDate.parse(dateStr, FORMATTERDATE);
    }

    /**
     * 判断字符串是否符合指定的时间格式 "yyyy-MM-dd"
     *
     * @param timeStr 时间字符串
     * @return 如果符合返回 true，否则返回 false
     */
    public static boolean isValidDateFormat(String timeStr) {
        return timeStr.matches(regexDate);
    }

    /**
     * 判断字符串是否符合指定的时间格式 "yyyy-MM-dd HH:mm:ss"
     *
     * @param timeStr 时间字符串
     * @return 如果符合返回 true，否则返回 false
     */
    public static boolean isValidDateTimeFormat(String timeStr) {
        return timeStr.matches(regexDateTime);
    }

    /**
     * LocalDateTime 转成 String
     *
     * @param localDateTime 日期时间
     * @return String 时间字符串
     */
    public static String parseDateStrByLocalDateTime(LocalDateTime localDateTime) {
        return localDateTime.format(FORMATTER);
    }

    /**
     * 将localDateTime 转成 java.util.Date
     *
     * @param localDateTime localDateTime
     * @return Date
     */
    public static Date parseDateByLocalDateTime(LocalDateTime localDateTime) {
        ZonedDateTime zdt = localDateTime.atZone(ZONE_OFFSET);
        return Date.from(zdt.toInstant());
    }
//    // ----------------------------------比较大小-----------------------------------------
//
//    /**
//     * 时间字符串比较
//     *
//     * @param dateStr1 时间字符串
//     * @param dateStr2 时间字符串
//     * @return int 0：相等 -1: dateStr1 < dateStr2 1：dateStr1 > dateStr2
//     */
//    public static int comparisonTimeByDateStr(String dateStr1, String dateStr2) {
//        LocalDateTime localDateTime1 = parseLocalDateTimeByDateStr(dateStr1);
//        LocalDateTime localDateTime2 = parseLocalDateTimeByDateStr(dateStr2);
//        return comparisonTimeByLocalDateTime(localDateTime1, localDateTime2);
//    }
//

    /**
     * Date比较大小
     *
     * @param date1 日期
     * @param date2 日期
     * @return int 0：相等 -1: date1 < date2 1：date1 > date2
     */
    public static int comparisonTimeByDate(Date date1, Date date2) {
        LocalDateTime localDateTime1 = parseLocalDateTimeByDate(date1);
        LocalDateTime localDateTime2 = parseLocalDateTimeByDate(date2);
        return comparisonTimeByLocalDateTime(localDateTime1, localDateTime2);
    }

    /**
     * LocalDateTime 比较大小
     *
     * @param localDateTime1 日期时间
     * @param localDateTime2 日期时间
     * @return int 0：相等 -1: localDateTime1 < localDateTime2 1：localDateTime1 > localDateTime2
     */
    public static int comparisonTimeByLocalDateTime(LocalDateTime localDateTime1, LocalDateTime localDateTime2) {
        return localDateTime1.isEqual(localDateTime2) ? 0 : localDateTime1.isBefore(localDateTime2) ? -1 : 1;
    }

//    // -------------------------------------判断日期属于哪天范畴-------------------------------------
//
//    /**
//     * @param localDateTime 日期时间
//     * @return int 1：今天范畴，2：今天以后，0：昨天范畴，-1：昨天以前
//     */
//    public static int isYesterday(LocalDateTime localDateTime) {
//        // 当前时间
//        LocalDateTime today = LocalDateTime.now();
//        // 今天开始
//        LocalDateTime todayStart = getCurrentStartLocalDateTime(today);
//        // 今天结束
//        LocalDateTime todayEnd = getCurrentEndLocalDateTime(today);
//        // 昨天
//        LocalDateTime yesterday = today.plusDays(-1);
//        // 昨天开始
//        LocalDateTime yesterdayStart = getCurrentStartLocalDateTime(yesterday);
//        // 昨天结束
//        LocalDateTime yesterdayEnd = getCurrentEndLocalDateTime(yesterday);
//        // 昨天以前
//        if (localDateTime.isBefore(yesterdayStart)) {
//            return -1;
//        }
//        // 昨天范畴
//        if (localDateTime.isAfter(yesterdayStart) && localDateTime.isBefore(yesterdayEnd)) {
//            return 0;
//        }
//        // 今天范畴
//        if (localDateTime.isAfter(todayStart) && localDateTime.isBefore(todayEnd)) {
//            return 1;
//        }
//        // 今天以后
//        return 2;
//    }
//
//    /**
//     * 求两个时间相差值
//     *
//     * @param localDateTime1 日期时间
//     * @param localDateTime2 日期时间
//     * @return Duration 期间
//     */
//    public static Duration getDateDurationByLocalDateTime(LocalDateTime localDateTime1, LocalDateTime localDateTime2) {
//        return Duration.between(localDateTime1, localDateTime2);
//    }
//
//    /**
//     * 求两个时间相差值
//     *
//     * @param dateStr1 时间字符串
//     * @param dateStr2 时间字符串
//     * @return Duration 期间
//     */
//    public static Duration getDateDurationByLocalDateStr(String dateStr1, String dateStr2) {
//        LocalDateTime localDateTime1 = parseLocalDateTimeByDateStr(dateStr1);
//        LocalDateTime localDateTime2 = parseLocalDateTimeByDateStr(dateStr2);
//        return Duration.between(localDateTime1, localDateTime2);
//    }
//
//    /**
//     * 求两个时间相差值
//     *
//     * @param date1 日期
//     * @param date2 日期
//     * @return Duration 期间
//     */
//    public static Duration getDateDurationByLocalDate(Date date1, Date date2) {
//        LocalDateTime localDateTime1 = parseLocalDateTimeByDate(date1);
//        LocalDateTime localDateTime2 = parseLocalDateTimeByDate(date2);
//        return Duration.between(localDateTime1, localDateTime2);
//    }

    public static void main(String[] args) {
        LocalDate now = LocalDate.now();
        String currentDate = LocalDateTimeUtil.format(now, DateTimeFormatter.ofPattern("yyyy 年 MM 月 d 日"));
        String applyDate = LocalDateTimeUtil.format(now, DateTimeFormatter.ofPattern("yyyy-MM-dd"));
        String registerDate = LocalDateTimeUtil.format(now, DateTimeFormatter.ofPattern("yyyy/M/d"));
        System.out.println("res===>"+currentDate+"======"+applyDate+"======"+registerDate);
        String input1 = "2024-08-25 00:00:00.000";
        String input2 = "2024-08-25";

        System.out.println(formatToChineseDate(input1)); // 输出：2024 年 08 月 25 日
        System.out.println(formatToChineseDate(input2)); // 输出：2024 年 08 月 25 日

        System.out.println(formatToSlashDate(input1)); // 输出：2024/8/25
        System.out.println(formatToSlashDate(input2)); // 输出：2024/8/25
    }
    /**
     * 解析输入的日期字符串为 LocalDate
     * @param inputTime 输入时间字符串
     * @return LocalDate 对象
     */
    private static LocalDate parseInputDate(String inputTime) {
        DateTimeFormatter[] formatters = {
                DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss.SSS"),
                DateTimeFormatter.ofPattern("yyyy-MM-dd")
        };

        for (DateTimeFormatter formatter : formatters) {
            try {
                return LocalDate.parse(inputTime, formatter);
            } catch (DateTimeParseException e) {
            }
        }

        throw new IllegalArgumentException("无法解析输入时间：" + inputTime);
    }

    /**
     * 将字符串时间转换为格式：yyyy 年 MM 月 d 日
     * @param inputTime 输入时间字符串，例如 2024-08-25 00:00:00.000 或 2024-08-25
     * @return 格式化后的日期字符串，例如 2023 年 10 月 29 日
     */
    public static String formatToChineseDate(String inputTime) {
        if(StringUtils.isBlank(inputTime)){
            return null;
        }
        LocalDate date = parseInputDate(inputTime);
        return date.format(DateTimeFormatter.ofPattern("yyyy 年 MM 月 d 日"));
    }

    /**
     * 将字符串时间转换为格式：yyyy/M/d
     * @param inputTime 输入时间字符串，例如 2024-08-25 00:00:00.000 或 2024-08-25
     * @return 格式化后的日期字符串，例如 2024/7/17
     */
    public static String formatToSlashDate(String inputTime) {
        if(StringUtils.isBlank(inputTime)){
            return null;
        }
        LocalDate date = parseInputDate(inputTime);
        return date.format(DateTimeFormatter.ofPattern("yyyy/M/d"));
    }

    public static String getCurrentDate() {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("M月d日");
        return LocalDate.now().format(formatter);
    }


}