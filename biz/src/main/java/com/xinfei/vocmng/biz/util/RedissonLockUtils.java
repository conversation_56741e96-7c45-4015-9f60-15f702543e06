/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xinfei.vocmng.biz.util;

import com.xinfei.vocmng.biz.service.DistributeLock;
import org.redisson.api.RMap;

import java.util.Map;
import java.util.concurrent.TimeUnit;

/**      单机模式
 * <AUTHOR>
 * @version $ RedissonLockUtils, v 0.1 2024/3/29 10:39 wancheng.qu Exp $
 */

public class RedissonLockUtils {

    private static DistributeLock locker;

    public static void setLocker(DistributeLock locker) {
        RedissonLockUtils.locker = locker;
    }

    public static void lock(String lockKey) {
        locker.lock(lockKey);
    }

    public static void unlock(String lockKey) {
        locker.unlock(lockKey);
    }

    public static void lock(String lockKey, int timeout) {
        locker.lock(lockKey, timeout);
    }

    public static void lock(String lockKey, int timeout, TimeUnit unit) {
        locker.lock(lockKey, timeout, unit);
    }

    public static boolean tryLock(String lockKey) {
        return locker.tryLock(lockKey);
    }

    public static boolean tryLock(String lockKey, long waitTime, long leaseTime,
                                  TimeUnit unit) throws InterruptedException {
        return locker.tryLock(lockKey, waitTime, leaseTime, unit);
    }

    public static boolean isLocked(String lockKey) {
        return locker.isLocked(lockKey);
    }


    public static boolean isHeldByCurrentThread(String lockKey) {
        return locker.isHeldByCurrentThread(lockKey);
    }

    public static <T> RMap<String, T> getCacheMapKeySet(String key){
        return locker.getCacheMapKeySet(key);
    }


}