/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xinfei.vocmng.biz.util;

import com.xinfei.vocmng.biz.model.base.CapitalTemplate;
import com.xinfei.vocmng.biz.model.base.CapitalTemplateField;
import com.xinfei.vocmng.biz.model.base.MailData;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.apache.poi.xwpf.usermodel.XWPFDocument;
import org.apache.poi.xwpf.usermodel.XWPFParagraph;
import org.apache.poi.xwpf.usermodel.XWPFRun;

import java.io.ByteArrayOutputStream;
import java.lang.reflect.Field;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

/**
 *
 * <AUTHOR>
 * @version $ MailDataUtil, v 0.1 2024/12/30 11:41 wancheng.qu Exp $
 */
@Slf4j
public class MailDataUtil {
    private static final Map<Class<?>, Map<String, Field>> FIELD_CACHE = new ConcurrentHashMap<>();

    private static Field getCachedField(Class<?> clazz, String fieldName) {
        return FIELD_CACHE.computeIfAbsent(clazz, c -> {
            Map<String, Field> fieldMap = new HashMap<>();
            Field[] fields = c.getDeclaredFields();
            for (Field field : fields) {
                field.setAccessible(true);
                fieldMap.put(field.getName(), field);
            }
            return fieldMap;
        }).get(fieldName);
    }

    public static MailData getData(CapitalTemplate template, List<CapitalTemplateField> fields) {
        CapitalTemplate.Word word = template.getWord();
        String type = template.getType();
        String text = template.getText();
        CapitalTemplate.Excel excel = template.getExcel();
        boolean sendText = template.isSendText();

        MailData mailData = new MailData();
        if(sendText){
            mailData.setText(text);
        }else {
            mailData.setText(getHtml(text,excel,fields));
        }
        mailData.setSendText(sendText);
        List<String> loans = fields.stream().map(CapitalTemplateField::getLoanNo).collect(Collectors.toList());
        if(Objects.equals("excel",type)){
            mailData.setAttachment(getExcel(excel,fields));
            mailData.setFileName(loans.stream().map(s->s+".xlsx").collect(Collectors.toList()));
        }else {
            mailData.setAttachment(getWord(word,fields));
            mailData.setFileName(loans.stream().map(s->s+".docx").collect(Collectors.toList()));
        }

        return mailData;
    }

    private static List<ByteArrayOutputStream> getWord(CapitalTemplate.Word word, List<CapitalTemplateField> fields) {
        List<ByteArrayOutputStream> result = new ArrayList<>();
        for (CapitalTemplateField field : fields) {
            String content = word.getContent();
            for (Map.Entry<String, Field> entry : FIELD_CACHE.computeIfAbsent(CapitalTemplateField.class, c -> {
                Map<String, Field> fieldMap = new HashMap<>();
                for (Field f : c.getDeclaredFields()) {
                    f.setAccessible(true);
                    fieldMap.put(f.getName(), f);
                }
                return fieldMap;
            }).entrySet()) {
                try {
                    Object value = entry.getValue().get(field);
                    content = content.replace("#{" + entry.getKey() + "}", value != null ? value.toString() : "");
                } catch (IllegalAccessException e) {
                    log.error("Failed to access field '{}' in field object: {}", entry.getKey(), field, e);
                }
            }

            ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
            try {
                XWPFDocument document = new XWPFDocument();
                String[] lines = content.split("\n");

                for (String line : lines) {
                    XWPFParagraph paragraph = document.createParagraph();
                    XWPFRun run = paragraph.createRun();
                    run.setText(line.trim());
                }

                document.write(outputStream);
            } catch (Exception e) {
                log.error("Failed to write Word document", e);
            }
            result.add(outputStream);
        }
        return result;
    }


    private static List<ByteArrayOutputStream> getExcel(CapitalTemplate.Excel excel, List<CapitalTemplateField> fields) {
        List<ByteArrayOutputStream> result = new ArrayList<>();
        List<CapitalTemplate.Entry> entryList = excel.getFields();
        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        try (Workbook workbook = new XSSFWorkbook()) {
            Sheet sheet = workbook.createSheet("Data");
            // 创建表头
            Row headerRow = sheet.createRow(0);
            List<String> keys = entryList.stream().map(CapitalTemplate.Entry::getName).collect(Collectors.toList());
            for (int i = 0; i < keys.size(); i++) {
                headerRow.createCell(i).setCellValue(keys.get(i));
            }
            // 填充数据行
            for (int i = 0; i < fields.size(); i++) {
                Row dataRow = sheet.createRow(i + 1);
                CapitalTemplateField field = fields.get(i);
                for (int j = 0; j < entryList.size(); j++) {
                    CapitalTemplate.Entry key = entryList.get(j);
                    Field fieldRef = getCachedField(CapitalTemplateField.class, key.getKey());
                    if (fieldRef != null) {
                        try {
                            Object value = fieldRef.get(field);
                            dataRow.createCell(j).setCellValue(value != null ? value.toString() : "");
                        } catch (IllegalAccessException e) {
                            log.error("Failed to access field '{}' in field object: {}", key, field, e);
                            dataRow.createCell(j).setCellValue("");
                        }
                    }
                }
            }
            workbook.write(outputStream);
        } catch (Exception e) {
            log.error("Failed to write Excel document", e);
        }
        result.add(outputStream);
        return result;
    }


    private static String getHtml(String text, CapitalTemplate.Excel excel, List<CapitalTemplateField> fields) {
        List<CapitalTemplate.Entry> entryList = excel.getFields();
        StringBuilder htmlContent = new StringBuilder();
        htmlContent.append("<html><body>");
        htmlContent.append("<p>").append(text).append("</p>");
        htmlContent.append("<p></p>");
        htmlContent.append("<table border='1' style='border-collapse:collapse; text-align:center;'>");
        htmlContent.append("<tr>");
        entryList.forEach(entry -> {
            String name = entry.getName();
            htmlContent.append("<th>").append(name).append("</th>");
        });
        htmlContent.append("</tr>");
        for (CapitalTemplateField row : fields) {
            appendRow(htmlContent, row,entryList);
        }
        htmlContent.append("</table>");
        htmlContent.append("</body></html>");

        return htmlContent.toString();
    }

    private static void appendRow(StringBuilder htmlContent, CapitalTemplateField row, List<CapitalTemplate.Entry> entryList) {
        htmlContent.append("<tr>");
        for (CapitalTemplate.Entry entry : entryList) {
            String key = entry.getKey();
            Field field = getCachedField(CapitalTemplateField.class, key);
            if (field != null) {
                try {
                    Object value = field.get(row);
                    String cellContent = value != null ? value.toString() : "";
                    htmlContent.append("<td>").append(cellContent).append("</td>");
                } catch (IllegalAccessException e) {
                    log.error("Failed to access field '{}' in row: {}", key, row, e);
                    htmlContent.append("<td></td>");
                }
            } else {
                log.warn("Field '{}' not found in CapitalTemplateField", key);
                htmlContent.append("<td></td>");
            }
        }
        htmlContent.append("</tr>");
    }

}