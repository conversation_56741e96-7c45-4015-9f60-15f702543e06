package com.xinfei.vocmng.biz.model.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 *
 * <AUTHOR>
 * @version $ UdeskSenderEnum, v 0.1 2025/3/7 10:50 shaohui.chen Exp $
 */
@AllArgsConstructor
@Getter
public enum UdeskSenderEnum {
    CUSTOMER("customer", SendTypeEnum.RECEIVE),  // 客户端发送 -> 我方接收
    AGENT("agent", SendTypeEnum.SEND),           // 坐席发送 -> 我方发送
    SYS("sys", SendTypeEnum.SEND);               // 系统发送 -> 我方发送

    private final String sender;         // Udesk侧字符串
    private final SendTypeEnum sendType; // 对应我方发送类型

    /**
     * 根据 Udesk 的 sender 字符串获取我方 code
     */
    public static int getMySideCode(String senderStr) {
        for (UdeskSenderEnum e : values()) {
            if (e.getSender().equalsIgnoreCase(senderStr)) {
                return e.getSendType().getCode();
            }
        }
        // 未匹配时的处理，可选择抛异常或返回默认值
        return -1;
    }
}
