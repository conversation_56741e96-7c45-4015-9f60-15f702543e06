/*
 * Xinfei.com Inc.
 * Copyright (c) 2015-2024. All Rights Reserved.
 */

package com.xinfei.vocmng.biz.model.resp;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @version $ AccountAmountAdjustmentLog, v 0.1 2024/8/12 17:11 you.zhang Exp $
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AmountAdjustmentRecordResp {

    @ApiModelProperty(value = "业务修改类型")
    private String changeType;

    @ApiModelProperty(value = "变动前金额")
    private BigDecimal beforeAmount;

    @ApiModelProperty(value = "变动后金额")
    private BigDecimal afterAmount;

    @ApiModelProperty(value = "变动差额afterAmount-beforeAmount")
    private BigDecimal changeAmt;

    @ApiModelProperty(value = "业务发生时间")
    private Date businessTime;

    @ApiModelProperty(value = "变动额度类型，ADJUST: 授信额度,TEMPORARY:临时额度")
    private String adjustType;

    @ApiModelProperty(value = "变动额度类型，ADJUST: 授信额度,TEMPORARY:临时额度")
    private String adjustTypeName;

}
