/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xinfei.vocmng.biz.model.req;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 *
 * <AUTHOR>
 * @version $ ReviewOrderRefundReq, v 0.1 2024-05-23 15:34 junjie.yan Exp $
 */
@Data
public class ReviewOrderRefundReq {

    @NotNull
    private Long id;

    @ApiModelProperty("2：审核通过，3：审核拒绝，4：已撤销")
    @NotNull
    private Integer status;

    @ApiModelProperty("原因")
    private String reason;

}