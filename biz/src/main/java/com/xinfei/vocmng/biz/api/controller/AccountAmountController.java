/*
 * Xinfei.com Inc.
 * Copyright (c) 2015-2024. All Rights Reserved.
 */

package com.xinfei.vocmng.biz.api.controller;

import com.xinfei.vocmng.biz.api.AccountAmountApi;
import com.xinfei.vocmng.biz.model.annotation.LoginRequired;
import com.xinfei.vocmng.biz.model.enums.TechplayErrDtlEnum;
import com.xinfei.vocmng.biz.model.exception.IgnoreException;
import com.xinfei.vocmng.biz.model.resp.AmountAdjustmentRecordResp;
import com.xinfei.vocmng.biz.model.resp.ApiResponse;
import com.xinfei.vocmng.biz.service.AccountAmountService;
import io.kyoto.pillar.ams.rest.dto.falcon.resp.AccountAmountLogResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @version $ AmountController, v 0.1 2024/8/12 11:57 you.zhang Exp $
 */
@Slf4j
@RestController
@LoginRequired
public class AccountAmountController implements AccountAmountApi {


    @Resource
    private AccountAmountService accountAmountService;

    @Override
    public ApiResponse<List<AmountAdjustmentRecordResp>> queryAdjustmentRecords(String app, String customerNo, String adjustType) {

        if (StringUtils.isBlank(app) || StringUtils.isEmpty(customerNo)) {
            throw new IgnoreException(TechplayErrDtlEnum.REQ_PARAM_NOT_VALID, "app和客户号信息必填");
        }

        return ApiResponse.success(accountAmountService.queryAdjustmentRecords(app, customerNo, adjustType));
    }
}
