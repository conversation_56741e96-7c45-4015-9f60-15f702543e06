/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xinfei.vocmng.biz.model.enums;

import lombok.Getter;

/**
 * <AUTHOR>
 * @version $ OfflineRefundMethodEnum, v 0.1 2024-05-21 14:38 junjie.yan Exp $
 */

@Getter
public enum OfflineRefundMethodEnum {
    ALIPAY(1, "ALIPAY", "支付宝"),
    BANK_CARD(2, "BANK_CARD", "银行卡");

    private final Integer code;
    private final String codeStr;
    private final String description;

    OfflineRefundMethodEnum(Integer code, String codeStr, String description) {
        this.code = code;
        this.codeStr = codeStr;
        this.description = description;
    }

    public static Integer getCodeByCodeStr(String codeStr) {
        if (codeStr == null) {
            return null;
        }

        for (OfflineRefundMethodEnum method : OfflineRefundMethodEnum.values()) {
            if (method.getCodeStr().equals(codeStr)) {
                return method.getCode();
            }
        }

        return null;
    }

    public static String getCodeStrByCode(Integer code) {
        if (code == null) {
            return null;
        }

        for (OfflineRefundMethodEnum method : OfflineRefundMethodEnum.values()) {
            if (method.getCode().equals(code)) {
                return method.getCodeStr();
            }
        }

        return null;
    }

}