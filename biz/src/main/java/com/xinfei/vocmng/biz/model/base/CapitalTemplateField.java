/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xinfei.vocmng.biz.model.base;

import lombok.Data;

import java.io.Serializable;

/**
 *
 * <AUTHOR>
 * @version $ CapitalTemplateField, v 0.1 2024/12/27 11:34 wancheng.qu Exp $
 */
@Data
public class CapitalTemplateField implements Serializable {

    private int number; //序号
    private String channel;//渠道码
    private String asset;  //资产平台编号
    private String tel;//手机号
    private String status="结清";
    private String name;
    private String idNo;
    private String amount; //贷款金额
    private String loanNo; //我放借据号
    private String loanNoZ; //资方借据号
    private String orderNo;//我方订单号
    private String orderNoZ;//资方订单号

    private String beginTime; //借款日期起始日 2024-08-25 00:00:00.000
    private String beginTimeY; //起始日  2023年 10 月 29 日
    private String beginTimeS; //起始日  2024/7/17

    private String endTime; //到期日
    private String endTimeY; //到期日
    private String endTimeS; //到期日

    private String dateSettle; //结清日     2022-08-31 00:00:00
    private String dateSettleY;
    private String dateSettleS;

    private String dateCash;//资金到账日期
    private String dateCashY;//资金到账日期
    private String dateCashS;//资金到账日期

    private String currentDate;//当前日期，2024 年 07 月 8 日
    private String applyDate; //申请日期2024-07-26
    private String registerDate; //登记日期 2024/7/17

}