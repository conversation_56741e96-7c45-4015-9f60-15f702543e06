/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2023 All Rights Reserved.
 */
package com.xinfei.vocmng.biz.model.enums;

/**
 * 错误码场景段
 *
 * <p>全站统一分配场景码，申请系统的时候找架构部统一分配
 *
 * <AUTHOR>
 * @version $ TechplayErrScenarioEnum, v 0.1 2023/8/28 18:52 Jinyan.Huang Exp $
 */
public enum TechplayErrScenarioEnum {

    MNG("0001", "Techplay管理类场景"),

    TRADE("0002", "Techplay交易类场景"),

    QUERY("0003", "Techplay查询类场景");

    // ~~~ 属性定义 ~~~

    /**
     * 枚举编码
     */
    private final String code;

    /**
     * 描述说明
     */
    private final String description;

    /**
     * 私有构造函数。
     *
     * @param code        枚举编码
     * @param description 描述说明
     */
    private TechplayErrScenarioEnum(String code, String description) {
        this.code = code;
        this.description = description;
    }

    /**
     * 通过枚举<code>code</code>获得枚举
     *
     * @param code 枚举编码
     * @return 错误明细枚举
     */
    public static TechplayErrScenarioEnum getByCode(String code) {
        for (TechplayErrScenarioEnum detailCode : values()) {
            if (detailCode.getCode().equals(code)) {

                return detailCode;
            }
        }
        return null;
    }

    // ~~~容器方法 ~~~

    /**
     * @return Returns the code.
     */
    public String getCode() {
        return code;
    }

    /**
     * @return Returns the description.
     */
    public String getDescription() {
        return description;
    }
}
