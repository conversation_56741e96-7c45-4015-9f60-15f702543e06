package com.xinfei.vocmng.biz.config;

import lombok.Getter;
import lombok.Setter;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

/**
 * 数据洞察OSS配置
 *
 * <AUTHOR>
 * @version $ DataInsightOssConfig, v 0.1 2025/7/16 10:00 shaohui.chen Exp $
 */
@RefreshScope
@Setter
@Getter
@Component
public class DataInsightOssConfig {

    @Value("${datainsight.oss.endpoint:oss-cn-beijing-internal.aliyuncs.com}")
    private String endpoint;

    @Value("${datainsight.oss.accessKeyId:LTAI5t8nXqfN6hFdHPiKN321}")
    private String accessKeyId;

    @Value("${datainsight.oss.secretAccessKey:******************************}")
    private String accessKeySecret;

    @Value("${datainsight.oss.bucketName:qa1-datainsight-oss}")
    private String bucketName;

}
