/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2023 All Rights Reserved.
 */
package com.xinfei.vocmng.biz.util;

/**
 * <AUTHOR>
 * @version $ JwtUtil, v 0.1 2023/12/21 20:08 wancheng.qu Exp $
 */

import com.xinfei.vocmng.biz.model.enums.TechplayErrDtlEnum;
import com.xinfei.vocmng.biz.model.exception.IgnoreException;
import com.xinfei.vocmng.biz.service.LoginService;
import io.jsonwebtoken.Claims;
import io.jsonwebtoken.Jwts;
import io.jsonwebtoken.SignatureAlgorithm;
import io.jsonwebtoken.security.Keys;
import org.apache.commons.lang3.StringUtils;

import java.security.SecureRandom;
import java.util.Base64;
import java.util.Date;


public class JwtUtil {

    private static final String SECRET_KEY = Base64.getEncoder().encodeToString(Keys.secretKeyFor(SignatureAlgorithm.HS512).getEncoded());
    private static final long EXPIRATION_TIME = 43200000; // 12 hours

    public static String generateToken(String userIdentify) {
        Date now = new Date();
        Date expirationDate = new Date(now.getTime() + EXPIRATION_TIME);

        return Jwts.builder()
                .setSubject(userIdentify)
                .setIssuedAt(now)
                .setExpiration(expirationDate)
                .signWith(SignatureAlgorithm.HS512, SECRET_KEY)
                .compact();
    }


    public static String getUserIdentifyFromToken(String token, LoginService loginService) {
        String identifyCache = loginService.getUserIdentifyCache(token);
        if(StringUtils.isNotBlank(identifyCache)){
            return identifyCache;
        }
        boolean isValid = validateToken(token);
        if (!isValid) {
            //Token 无效
            throw new IgnoreException(TechplayErrDtlEnum.NOLOGIN_ERROR);
        }
        Claims claims = Jwts.parser()
                .setSigningKey(SECRET_KEY)
                .parseClaimsJws(token)
                .getBody();

        return claims.getSubject();
    }

    public static boolean validateToken(String token) {
        try {
            Jwts.parser().setSigningKey(SECRET_KEY).parseClaimsJws(token);
            return true;
        } catch (Exception e) {
            return false;
        }
    }

    public static String generateSecretKey(int length) {
        SecureRandom secureRandom = new SecureRandom();
        byte[] keyBytes = new byte[length];
        secureRandom.nextBytes(keyBytes);
        return bytesToHex(keyBytes);
    }

    private static String bytesToHex(byte[] bytes) {
        StringBuilder hexStringBuilder = new StringBuilder(2 * bytes.length);
        for (byte b : bytes) {
            hexStringBuilder.append(String.format("%02x", b));
        }
        return hexStringBuilder.toString();
    }

    public static void main(String[] args) {
        String secretKey = generateSecretKey(32);
        System.out.println("Generated Secret Key: " + secretKey);
    }
}
