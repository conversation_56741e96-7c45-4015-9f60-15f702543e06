/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xinfei.vocmng.biz.model.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

/**
 * VIP支付状态枚举
 *
 * <AUTHOR>
 * @version $ VipPayStatusEnum, v 0.1 2025/05/19 15:40 shaohui.chen Exp $
 */
@Getter
@AllArgsConstructor
public enum VipPayStatusEnum {
    
    PAY_START("pay_start", "待支付"),
    PAYING("paying", "支付中"),
    PAY_FAIL("pay_fail", "支付失败"),
    PAY_SUCCESS("pay_success", "支付成功"),
    PAY_CANCEL("pay_cancel", "支付取消"),
    PAY_CLOSE("pay_close", "支付关闭");
    
    /** 支付状态编码 */
    private final String code;
    
    /** 支付状态描述 */
    private final String desc;

    /**
     * 根据支付状态编码获取支付状态描述
     *
     * @param code 支付状态编码
     * @return 支付状态描述
     */
    public static String getDescByCode(String code) {
        if (StringUtils.isBlank(code)) {
            return null;
        }
        
        for (VipPayStatusEnum payStatus : VipPayStatusEnum.values()) {
            if (payStatus.getCode().equalsIgnoreCase(code)) {
                return payStatus.getDesc();
            }
        }
        
        return null;
    }
}
