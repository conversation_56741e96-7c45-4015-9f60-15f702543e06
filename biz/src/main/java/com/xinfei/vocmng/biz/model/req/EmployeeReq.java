/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2023 All Rights Reserved.
 */
package com.xinfei.vocmng.biz.model.req;

import com.xinfei.vocmng.biz.model.base.PageRequestDto;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.Pattern;
import java.io.Serializable;

/**
 *
 * <AUTHOR>
 * @version $ EmployeeReq, v 0.1 2023/12/20 18:41 wancheng.qu Exp $
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class EmployeeReq extends PageRequestDto implements Serializable {

    @ApiModelProperty(value = "员工编号")
    private Long id;

    @ApiModelProperty(value = "userIdentify")
    private String userIdentify;

    @ApiModelProperty(value = "员工姓名,模糊搜索")
    private String name;


    @ApiModelProperty(value = "员工账号状态：0正常 1关闭")
    private Integer state;

    @ApiModelProperty(value = "组织id")
    private Long departmentId;

    @ApiModelProperty(value = "角色id")
    private Long roleId;

    @ApiModelProperty(value = "手机号码")
    @Pattern(regexp = "^[1][3,4,5,6,7,8,9][0-9]{9}$", message = "手机号格式有误")
    private String mobile;

    @ApiModelProperty(value = "是否删除：1已删除 0未删除")
    private Integer isDeleted;

    @ApiModelProperty(value = "(2:EHR 3：客服, 4：电销， 5: 催收)")
    private Integer sourceType;

    @ApiModelProperty(value = "是否实时质检辅助(0:否,1:是)")
    private Integer realTimeAssistance;

}