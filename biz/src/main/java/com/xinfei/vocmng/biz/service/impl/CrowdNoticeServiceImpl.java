package com.xinfei.vocmng.biz.service.impl;

import com.xinfei.vocmng.biz.constants.ApolloConstant;
import com.xinfei.vocmng.biz.model.enums.CrowdNoticeMessageTypeEnum;
import com.xinfei.vocmng.biz.rr.dto.CrowdUserDataDto;
import com.xinfei.vocmng.biz.rr.request.CrowdNoticeMsg;
import com.xinfei.vocmng.biz.service.CrowdFileParseService;
import com.xinfei.vocmng.biz.service.CrowdNoticeService;
import com.xinfei.vocmng.biz.service.CrowdRefundService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeoutException;

/**
 * 人群通知服务实现
 *
 * <AUTHOR>
 * @version $ CrowdNoticeServiceImpl, v 0.1 2025/7/16 10:00 shaohui.chen Exp $
 */
@Service
@Slf4j
public class CrowdNoticeServiceImpl implements CrowdNoticeService {

    @Autowired
    private CrowdFileParseService crowdFileParseService;

    @Autowired
    private CrowdRefundService crowdRefundService;

    @Override
    public void processCrowdNotice(CrowdNoticeMsg crowdNoticeMsg) {
        if (!ApolloConstant.AUTO_WORK_ORDER) {
            return;
        }
        if (Objects.isNull(crowdNoticeMsg)) {
            log.warn("人群通知消息为空，跳过处理");
            return;
        }
        if (Objects.isNull(crowdNoticeMsg.getCrowdId()) || !Objects.equals(crowdNoticeMsg.getCrowdId(), ApolloConstant.AUTO_WORK_CROWDID)) {
            log.info("非需要处理的人群id");
            return;
        }
        CrowdNoticeMessageTypeEnum messageType = CrowdNoticeMessageTypeEnum.getByCode(crowdNoticeMsg.getMessageType());
        log.info("处理人群通知消息，类型: {}, 人群ID: {}, 人群大小: {}",
                messageType.getName(), crowdNoticeMsg.getCrowdId(), crowdNoticeMsg.getCrowdSize());

        try {
            switch (messageType) {
                case ONLINE:
                case OFFLINE:
                case DELETE:
                    break;
                case REFRESH:
                    handleRefreshMessage(crowdNoticeMsg);
                    break;
                default:
                    log.warn("未知的消息类型: {}", crowdNoticeMsg.getMessageType());
            }
        } catch (Exception e) {
            log.error("处理人群通知消息失败，人群ID: {}, 消息类型: {}",
                    crowdNoticeMsg.getCrowdId(), messageType.getName(), e);
            throw e;
        }
    }


    /**
     * 处理刷新消息
     */
    private void handleRefreshMessage(CrowdNoticeMsg msg) {
        log.info("处理人群刷新消息，人群ID: {}, 新版本: {}, OSS路径: {}",
                msg.getCrowdId(), msg.getRunVersion(), msg.getOssPath());

        try {
            // 处理OSS文件
            if (StringUtils.isNotBlank(msg.getOssPath()) && CollectionUtils.isNotEmpty(msg.getOssFiles())) {
                for (String fileName : msg.getOssFiles()) {
                    log.info("开始处理OSS文件: {}", fileName);

                    // 解析文件内容
                    List<CrowdUserDataDto> userDataList = crowdFileParseService.parseFile(msg.getOssPath(), fileName);

                    if (CollectionUtils.isNotEmpty(userDataList)) {
                        log.info("文件解析完成，用户数量: {}", userDataList.size());

                        // 处理用户退款
                        crowdRefundService.processCrowdRefund(userDataList);

                        log.info("文件处理完成: {}", fileName);
                    } else {
                        log.warn("文件解析结果为空: {}", fileName);
                    }
                }
            } else {
                log.warn("OSS路径或文件列表为空，跳过文件处理");
            }

            log.info("人群刷新消息处理完成，人群ID: {}", msg.getCrowdId());

        } catch (Exception e) {
            log.error("处理人群刷新消息失败，人群ID: {}", msg.getCrowdId(), e);
            throw e;
        }
    }
}
