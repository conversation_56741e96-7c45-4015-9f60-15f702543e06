/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xinfei.vocmng.biz.util;

import com.xinfei.vocmng.biz.model.base.ControlRes;
import lombok.extern.slf4j.Slf4j;

import java.math.BigDecimal;
import java.math.RoundingMode;

/**
 * <AUTHOR>
 * @version $ ReductionStrategyUtil, v 0.1 2024-03-31 19:41 junjie.yan Exp $
 */
@Slf4j
public class ReductionStrategyUtil {

    public static ControlRes<BigDecimal, BigDecimal> calculateAmount(BigDecimal t, BigDecimal o, BigDecimal init, BigDecimal exempt, BigDecimal calculation) {

        ControlRes<BigDecimal, BigDecimal> cr = new ControlRes<>();
        if (t == null || o == null || init == null || exempt == null || calculation == null) {
            cr.setLeft(new BigDecimal(0));
            cr.setRight(new BigDecimal(0));
            return cr;
        }

        BigDecimal lower = init.multiply(t).multiply(new BigDecimal("0.01")).setScale(2, RoundingMode.HALF_UP);
        BigDecimal upper = init.multiply(o).multiply(new BigDecimal("0.01")).setScale(2, RoundingMode.HALF_UP);
        upper = upper.subtract(exempt);

        if (lower.compareTo(upper) > 0) {
            cr.setLeft(new BigDecimal(0));
            cr.setRight(new BigDecimal(0));
            return cr;
        }

        if (calculation.compareTo(lower) < 0) {
            cr.setLeft(new BigDecimal(0));
            cr.setRight(new BigDecimal(0));
            return cr;
        } else if (calculation.compareTo(upper) > 0) {
            cr.setLeft(lower);
            cr.setRight(upper);
            return cr;
        } else {
            cr.setLeft(lower);
            cr.setRight(calculation);
            return cr;
        }

    }

    public static ControlRes<BigDecimal, BigDecimal> calculateSettleAmount(BigDecimal t, BigDecimal o, BigDecimal exempt, BigDecimal calculation) {

        ControlRes<BigDecimal, BigDecimal> cr = new ControlRes<>();
        if (t == null || o == null || exempt == null || calculation == null) {
            cr.setLeft(new BigDecimal(0));
            cr.setRight(new BigDecimal(0));
            return cr;
        }

        //log.info("calculateAmount process红线挡板:试算金额:" + calculation + "历史减免:" + exempt + "上限百分比：" + o + "下限百分比：" + t);

        BigDecimal lower = calculation.multiply(t).multiply(new BigDecimal("0.01")).setScale(2, RoundingMode.HALF_UP);
        BigDecimal upper = calculation.multiply(o).multiply(new BigDecimal("0.01")).setScale(2, RoundingMode.HALF_UP);
        upper = upper.subtract(exempt);

        if (lower.compareTo(upper) > 0) {
            cr.setLeft(new BigDecimal(0));
            cr.setRight(new BigDecimal(0));
            return cr;
        }

        cr.setLeft(lower);
        cr.setRight(upper);

        return cr;

    }
}