/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xinfei.vocmng.biz.model.req;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 *
 * <AUTHOR>
 * @version $ CommunicateSummaryDetailReq, v 0.1 2024-02-04 14:54 junjie.yan Exp $
 */
@Data
public class CommunicateSummaryDetailReq {

    @ApiModelProperty(value = "redis key")
    private String mesgId;

    @ApiModelProperty(value = "会话小结id")
    private Long summaryId;

    @ApiModelProperty(value = "进线电话")
    private String telephone;

    @ApiModelProperty(value = "中继号")
    private String displayNumber;

}