/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xinfei.vocmng.biz.model.enums;

import lombok.Getter;

/**
 * <AUTHOR>
 * @version $ DeductRuleEnum, v 0.1 2024-05-21 14:38 junjie.yan Exp $
 */

@Getter
public enum DeductRuleEnum {
    DEDUCT_AMT_LIMIT("DEDUCT_AMT_LIMIT", "锁定减免金额"),
    PAY_AMT_LIMIT("PAY_AMT_LIMIT", "锁定应还金额");

    private final String code;
    private final String description;

    DeductRuleEnum(String code, String description) {
        this.code = code;
        this.description = description;
    }

}