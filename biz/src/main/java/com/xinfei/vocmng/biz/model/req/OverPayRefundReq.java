/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xinfei.vocmng.biz.model.req;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @version $ RefundTrialReq, v 0.1 2024-05-20 15:39 junjie.yan Exp $
 */
@Data
public class OverPayRefundReq {

    @ApiModelProperty("退款方式:OFFLINE_REFUND:线下退款")
    @NotBlank(message = "退款方式不能为空")
    private String refundType;

    @ApiModelProperty("银行流水转账渠道,zfb,bank")
    @NotBlank(message = "银行流水转账渠道不能为空")
    private String channelCode;

    @ApiModelProperty("转账流水")
    @NotBlank(message = "转账流水不能为空")
    private String transNo;

    @ApiModelProperty("总退款金额")
    @NotNull(message = "总退款金额不能为空")
    private BigDecimal refundAmount;

    @ApiModelProperty("还款时间，1：立即(2小时)")
    @NotNull(message = "还款时间不能为空")
    private Integer executeType;

    @ApiModelProperty("自定义天数")
    private Integer executeDay;

    @ApiModelProperty("线下退款类型，ALIPAY:支付宝,BANK_CARD:银行卡")
    @NotBlank(message = "线下退款类型不能为空")
    private String offlineRefundMethod;

    @ApiModelProperty("线下退款账号")
    @NotBlank(message = "线下退款账号不能为空")
    private String offlineRefundAccount;

    @ApiModelProperty("线下退款银行")
    private String offlineRefundBank;

    @ApiModelProperty("线下退款客户名")
    @NotBlank(message = "线下退款客户名不能为空")
    private String offlineRefundUserName;

    @ApiModelProperty("审核状态，0：无需审核")
    @NotNull(message = "审核状态不能为空")
    private Integer status;

    @ApiModelProperty("退款类型 3：溢缴款）")
    @NotNull(message = "退款类型不能为空")
    private Integer requestType;

    @ApiModelProperty("退款原因")
    @NotBlank(message = "退款原因不能为空")
    private String refundReason;
}