/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xinfei.vocmng.biz.job;

import com.xinfei.vocmng.biz.component.RepaymentTaskManage;
import com.xinfei.vocmng.biz.service.LoginService;
import com.xinfei.vocmng.itl.util.LogUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @version $ QuanlityTask, v 0.1 2024-10-18 16:18 junjie.yan Exp $
 */
@Configuration
@Slf4j
public class QualityStaffTask extends RepaymentTaskManage {

    @Resource
    private LoginService loginService;

    @Value(value = "${quality.staff.switch:false}")
    private Boolean isQuality;

    @Value(value = "${quality.staff.cron:0 0/10 * * * ?}")
    private String cron;

    @Override
    protected String getCron() {
        return cron;
    }

    @Override
    protected void processTask() {
        if (isQuality) {
            log.info(LogUtil.infoLog("syncQualityStaff", "开始全量同步客服员工信息至质检平台"));
            loginService.syncQualityStaff();
        }
    }
}