package com.xinfei.vocmng.biz.model.enums;

import lombok.Getter;

/**
 *
 * <AUTHOR>
 * @version $ CloseMethodEnum, v 0.1 2025/3/21 16:34 pengming.liu Exp $
 */
@Getter
public enum CloseMethodEnum {
    AGENT_CLOSE("agent_close", "客服关闭"),
    REDIRECT_CLOSE("redirect_close", "转接关闭"),
    SYS_CLOSE("sys_close", "系统关闭"),
    CUSTOMER_CLOSE("customer_close", "客户关闭");

    private final String code;
    // 获取 code 对应的中文描述
    @Getter
    private final String description;

    CloseMethodEnum(String code, String description) {
        this.code = code;
        this.description = description;
    }

    // 根据 code 获取对应的枚举实例
    public static String getDescriptionByCode(String code) {
        for (CloseMethodEnum method : CloseMethodEnum.values()) {
            if (method.code.equals(code)) {
                return method.getDescription();
            }
        }
        return "未知关闭方式";  // 如果没有找到匹配的 code，返回默认值
    }
}
