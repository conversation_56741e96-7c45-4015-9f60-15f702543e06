/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xinfei.vocmng.biz.model.req;

import com.xinfei.vocmng.biz.model.base.PageRequestDto;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * <AUTHOR>
 * @version $ OrderRefundReviewReq, v 0.1 2024-05-23 14:49 junjie.yan Exp $
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class OrderRefundReviewReq extends PageRequestDto {

    @ApiModelProperty("审核状态（0：无需审核，1：待审核，2：审核通过，3：审核拒绝）")
    private List<Integer> status;

    @ApiModelProperty("订单号")
    private String orderNo;

    @ApiModelProperty("退款记录id")
    private Long id;
}