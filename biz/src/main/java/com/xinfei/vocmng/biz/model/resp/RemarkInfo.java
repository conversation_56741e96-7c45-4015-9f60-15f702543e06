/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2023 All Rights Reserved.
 */
package com.xinfei.vocmng.biz.model.resp;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.xinfei.vocmng.biz.model.base.TimeFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 备注信息
 * 
 * <AUTHOR>
 * @version $ RemarkInfo, v 0.1 2024-01-01 00:00 system Exp $
 */
@Data
public class RemarkInfo implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "备注内容")
    private String remark;

    @ApiModelProperty(value = "创建时间")
    @JsonFormat(pattern = TimeFormat.DATE_TIME)
    private LocalDateTime createdTime;

    @ApiModelProperty(value = "创建人")
    private String createUser;

    public RemarkInfo() {
    }

    public RemarkInfo(String remark, LocalDateTime createdTime, String createUser) {
        this.remark = remark;
        this.createdTime = createdTime;
        this.createUser = createUser;
    }
}
