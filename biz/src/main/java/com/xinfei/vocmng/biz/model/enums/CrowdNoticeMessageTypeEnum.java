package com.xinfei.vocmng.biz.model.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 人群通知消息类型枚举
 *
 * <AUTHOR>
 * @version $ CrowdNoticeMessageTypeEnum, v 0.1 2025/7/16 10:00 shaohui.chen Exp $
 */
@AllArgsConstructor
@Getter
public enum CrowdNoticeMessageTypeEnum {

    /**
     * 上线
     */
    ONLINE(1, "上线"),

    /**
     * 下线
     */
    OFFLINE(2, "下线"),

    /**
     * 删除
     */
    DELETE(3, "删除"),

    /**
     * 刷新
     */
    REFRESH(4, "刷新");

    /**
     * 枚举值
     */
    private final Integer code;

    /**
     * 显示名称
     */
    private final String name;

    /**
     * 根据code获取枚举
     */
    public static CrowdNoticeMessageTypeEnum getByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (CrowdNoticeMessageTypeEnum value : values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return null;
    }

    /**
     * 判断是否为有效的消息类型
     */
    public static boolean isValid(Integer code) {
        return getByCode(code) != null;
    }
}
