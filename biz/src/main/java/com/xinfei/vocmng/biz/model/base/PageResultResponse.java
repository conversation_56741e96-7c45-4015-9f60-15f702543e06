package com.xinfei.vocmng.biz.model.base;

import lombok.Data;

import java.util.List;

/**
 * <p>
 * 分页返回结果
 * <p/>
 *
 */
@Data
public class PageResultResponse<T> {

    protected List<T> list;
    protected long currentPage;
    protected long pageSize;
    private long totalPage;
    private long total;

    public PageResultResponse() {
    }

    public PageResultResponse(List<T> records, long current, long size, long total) {
        this.list = records;
        this.currentPage = current;
        this.pageSize = size;
        this.total = total;
        if (total > 0 && size > 0) {
            this.totalPage = total % size == 0 ? total / size : total / size + 1;
        }
    }

}
