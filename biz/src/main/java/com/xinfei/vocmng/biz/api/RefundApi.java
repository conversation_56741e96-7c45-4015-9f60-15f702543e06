/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xinfei.vocmng.biz.api;

import com.xinfei.repaytrade.facade.rr.response.RefundCancelResponse;
import com.xinfei.repaytrade.facade.rr.response.RefundRightNowResponse;
import com.xinfei.vocmng.biz.model.annotation.DataPermission;
import com.xinfei.vocmng.biz.model.annotation.LoginRequired;
import com.xinfei.vocmng.biz.model.annotation.OperateLogAnnotation;
import com.xinfei.vocmng.biz.model.base.PageResultResponse;
import com.xinfei.vocmng.biz.model.enums.DataPermissionType;
import com.xinfei.vocmng.biz.model.enums.OperateType;
import com.xinfei.vocmng.biz.model.enums.TechplayErrDtlEnum;
import com.xinfei.vocmng.biz.model.exception.IgnoreException;
import com.xinfei.vocmng.biz.model.req.*;
import com.xinfei.vocmng.biz.model.resp.*;
import com.xinfei.vocmng.biz.rr.dto.RefundRequestDto;
import com.xinfei.vocmng.biz.rr.request.RefundFeeRatioProcessReq;
import com.xinfei.vocmng.biz.rr.response.CalRefundAmtManyResponse;
import com.xinfei.vocmng.biz.rr.response.RefundFeeRatioProcessResp;
import com.xinfei.vocmng.biz.service.impl.RefundServiceImp;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 * <AUTHOR>
 * @version $ RefundApi, v 0.1 2024/3/25 12:01 wancheng.qu Exp $
 */

@Api(tags = "退款流程相关")
@RestController
@LoginRequired
@Slf4j
@RequestMapping("/refund")
public class RefundApi {

    @Resource
    private RefundServiceImp refundServiceImp;

    @ApiOperation("可退金额")
    @PostMapping("/calRefundAmt")
    public ApiResponse<List<RefundResp>> queryRefundAmt(@Validated @RequestBody List<RefundReq> req) {
        return ApiResponse.success(refundServiceImp.calculateRefundAmount(req));
    }

    @ApiOperation("退款明细试算")
    @PostMapping("/refundTrial")
    @DataPermission(type = DataPermissionType.NONE)
    public ApiResponse<List<RefundTrialResp>> refundTrial(@Validated @RequestBody List<RefundTrialReq> req) {
        return ApiResponse.success(refundServiceImp.refundTrial(req));
    }

    @ApiOperation("退款费率计算")
    @PostMapping(value = "/refundRateCalculation")
    public ApiResponse<List<RefundFeeRatioProcessResp>> refundRateCalculation(@Valid @RequestBody List<RefundFeeRatioProcessReq> req) {
        for (RefundFeeRatioProcessReq feeRatio : req) {
            if (feeRatio.getTargetFeeRatio() == null && feeRatio.getTargetRefundAmt() == null) {
                throw new IgnoreException(TechplayErrDtlEnum.REQ_PARAM_NOT_VALID, "目标费率和目标减免金额不能都传空");
            }

            if (feeRatio.getTargetFeeRatio() != null && feeRatio.getTargetRefundAmt() != null) {
                throw new IgnoreException(TechplayErrDtlEnum.REQ_PARAM_NOT_VALID, "目标费率和目标减免金额不能都传");
            }
        }
        return ApiResponse.success(refundServiceImp.refundRateCalculation(req));
    }

    @ApiOperation("订单退款申请")
    @PostMapping("/orderRefund")
    @OperateLogAnnotation(type = OperateType.TYPE_EIGHT, description = "订单退款申请")
    public ApiResponse<String> queryRefundDetail(@Validated @RequestBody List<RefundApplyReq> request) {
        return ApiResponse.success(refundServiceImp.refundApply(request));
    }

    @ApiOperation("溢缴款退款申请")
    @PostMapping("/overPayRefund")
    @OperateLogAnnotation(type = OperateType.TYPE_EIGHT, description = "溢缴款退款申请")
    public ApiResponse<Boolean> queryRefundDetail(@Validated @RequestBody OverPayRefundReq request) {
        Boolean result = refundServiceImp.overPayRefundApply(request);
        if (result) {
            return ApiResponse.success(true);
        } else {
            return ApiResponse.fail("溢缴款退款申请失败");
        }
    }


    @ApiOperation("订单退款记录")
    @PostMapping("/orderRecords")
    public ApiResponse<List<OrderRefundRecods>> orderRefundApplyRecords(@Validated @RequestBody OrderRefundRecordsReq req) {
        return ApiResponse.success(refundServiceImp.orderRefundApplyRecords(req));
    }

    @ApiOperation("审核退款单列表")
    @PostMapping("/refundList")
    public ApiResponse<PageResultResponse<RefundRequestDto>> orderRefunds(@Validated @RequestBody OrderRefundReviewReq req) {
        return ApiResponse.success(refundServiceImp.orderRefundReview(req));
    }

    @ApiOperation("审核退款单")
    @PostMapping("/reviewOrderRefund")
    @OperateLogAnnotation(type = OperateType.TYPE_EIGHT, description = "审核退款单")
    public ApiResponse<Boolean> reviewOrderRefund(@Validated @RequestBody ReviewOrderRefundReq req) {
        return ApiResponse.success(refundServiceImp.reviewOrderRefund(req));
    }

    @ApiOperation("订单立即退款")
    @PostMapping("/refundRightNow")
    @OperateLogAnnotation(type = OperateType.TYPE_EIGHT, description = "订单立即退款")
    public ApiResponse<RefundRightNowResponse> refundRightNow(@Validated @RequestBody RefundRightNowReq req) {
        return ApiResponse.success(refundServiceImp.refundRightNow(req));
    }

    @ApiOperation("订单退款撤销")
    @PostMapping("/orderCancel")
    @OperateLogAnnotation(type = OperateType.TYPE_EIGHT, description = "订单退款撤销")
    public ApiResponse<RefundCancelResponse> cancellation(@Validated @RequestBody RefundCancellationReq req) {
        return ApiResponse.success(refundServiceImp.refundCancel(req));
    }

    @ApiOperation("溢缴款退款记录")
    @PostMapping("/overPayRecords")
    public ApiResponse<List<OverPayRecordsResp>> overPayRecords(@Validated @RequestBody OverPayRecordsReq req) {
        return ApiResponse.success(refundServiceImp.overPayRecords(req));
    }

    @ApiOperation("溢缴款退款撤销")
    @PostMapping("/overPayCancel")
    @OperateLogAnnotation(type = OperateType.TYPE_EIGHT, description = "溢缴款退款撤销")
    public ApiResponse<Boolean> overPayCancellation(@Validated @RequestBody OverPayCancellationReq req) {
        Boolean result = refundServiceImp.overPayCancellation(req);
        if (result) {
            return ApiResponse.success(result);
        } else {
            return ApiResponse.fail("溢缴款退款撤销失败");
        }
    }

    @ApiOperation("退款金额多条维度计算")
    @PostMapping(value = "/calRefundAmtMany")
    public ApiResponse<List<CalRefundAmtManyResponse>> calRefundAmtMany(@Validated @RequestBody List<CalRefundAmtManyResp> calRefundAmtManyRespList){
        return ApiResponse.success(refundServiceImp.calRefundAmtMany(calRefundAmtManyRespList));
    }
}