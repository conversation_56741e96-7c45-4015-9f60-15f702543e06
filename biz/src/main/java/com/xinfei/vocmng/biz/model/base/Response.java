package com.xinfei.vocmng.biz.model.base;

import lombok.Getter;
import lombok.Setter;
import org.springframework.http.HttpStatus;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

/**
 * <p>
 * 结果统一返回
 * <p/>
 *
 */
@Getter
@Setter
public class Response<T> implements Serializable {

    private static final DateTimeFormatter DATE_TIME_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

    private static final long serialVersionUID = 3177777670245790919L;

    private int code;
    private String message;
    private T result;
    private String dateTime;

    public Response() {
        this.dateTime = DATE_TIME_FORMATTER.format(LocalDateTime.now());
    }


    public Response(int httpCode, String message) {
        this();
        this.code = httpCode;
        this.message = message;
    }

    public Response(int httpCode, T result) {
        this();
        this.code = httpCode;
        this.result = result;
    }


    public Response(int httpCode, String message, T result) {
        this();
        this.code = httpCode;
        this.message = message;
        this.result = result;
    }

    public static <T> Response<T> fail(String message) {
        return new Response<T>(HttpStatus.BAD_REQUEST.value(), message);
    }

    public static <T> Response<T> fail(int code, String message) {
        return new Response<T>(code, message);
    }

    public static <T> Response<T> success(String message) {
        return new Response<T>(HttpStatus.OK.value(), message);
    }

    public static <T> Response<T> success() {
        return new Response<T>(HttpStatus.OK.value(), HttpStatus.OK.name());
    }

    public static <T> Response<T> success(T result) {
        return new Response<T>(HttpStatus.OK.value(), result);
    }

    /**
     * <p>
     * 当返回成功结果时，推荐使用该方法
     * <p/>
     *
     * @param message
     * @param result
     */
    public static <T> Response<T> success(String message, T result) {
        return new Response<T>(HttpStatus.OK.value(), message, result);
    }


}
