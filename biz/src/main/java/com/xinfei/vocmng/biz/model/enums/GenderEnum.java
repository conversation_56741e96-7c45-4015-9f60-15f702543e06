package com.xinfei.vocmng.biz.model.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 *
 * <AUTHOR>
 * @version $ GenderEnum, v 0.1 2025/3/11 10:52 shaohui.chen Exp $
 */
@AllArgsConstructor
@Getter
public enum GenderEnum {
    UNKNOWN(0, "未知"),
    MALE(1, "男"),
    FEMALE(2, "女");

    private final int code;
    private final String description;

    /**
     * 根据 code 获取 GenderEnum 枚举值
     * @param code 数字代码，1 表示男，2 表示女，0 或其它表示未知
     * @return 对应的 GenderEnum 枚举
     */
    public static GenderEnum fromCode(int code) {
        for (GenderEnum gender : GenderEnum.values()) {
            if (gender.getCode() == code) {
                return gender;
            }
        }
        return UNKNOWN;
    }
}
