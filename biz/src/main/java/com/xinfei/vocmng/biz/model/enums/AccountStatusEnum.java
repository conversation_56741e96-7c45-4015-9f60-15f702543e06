package com.xinfei.vocmng.biz.model.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 *
 * <AUTHOR>
 * @version $ AccountStatusEnum, v 0.1 2025/3/11 14:59 shaohui.chen Exp $
 */
@AllArgsConstructor
@Getter
public enum AccountStatusEnum {
    CANCELLED(0, "注销"),
    NORMAL(10, "正常"),
    TEMPORARILY_CANCELLED(20, "临时注销"),
    CANCELLING(2, "注销中");

    private final int code;
    private final String description;

    /**
     * 根据 code 获取对应的枚举
     * @param code 状态码
     * @return 对应的 AccountStatusEnum，若未匹配返回 null
     */
    public static AccountStatusEnum fromCode(int code) {
        for (AccountStatusEnum status : AccountStatusEnum.values()) {
            if (status.getCode() == code) {
                return status;
            }
        }
        return null;
    }
}
