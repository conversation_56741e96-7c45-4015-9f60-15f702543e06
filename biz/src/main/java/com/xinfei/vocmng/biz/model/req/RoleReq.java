/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2023 All Rights Reserved.
 */
package com.xinfei.vocmng.biz.model.req;

import com.xinfei.vocmng.biz.model.base.PageRequestDto;
import com.xinfei.vocmng.biz.model.resp.FeeStrategyConfig;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 *
 * <AUTHOR>
 * @version $ RoleReq, v 0.1 2023/12/20 14:50 wancheng.qu Exp $
 */

@Data
public class RoleReq extends PageRequestDto implements Serializable {
    @ApiModelProperty(value = "角色id")
    private Long id;

    @ApiModelProperty(value = "角色名称，新增用")
    private String addName;

    @ApiModelProperty(value = "角色名称，查询多选用")
    private List<String> name;

    @ApiModelProperty(value = "角色描述")
    private String description;

    @ApiModelProperty(value = "资源id")
    private List<Long> resourceList;

    @ApiModelProperty(value = "数据资源列表id")
    private List<Long> dataList;

    @ApiModelProperty(value = "费控列表")
    private List<ControlReq> controlList;

    @ApiModelProperty(value = "是否删除：1已删除 0未删除")
    private Integer isDeleted;

    @ApiModelProperty(value = "新费控列表")
    private List<FeeStrategyConfig> feeStrategyConfigs;



}