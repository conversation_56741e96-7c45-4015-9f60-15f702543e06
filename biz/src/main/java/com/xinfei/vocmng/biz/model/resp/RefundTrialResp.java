/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xinfei.vocmng.biz.model.resp;

import com.xinfei.vocmng.biz.model.annotation.DataPermission;
import com.xinfei.vocmng.biz.model.enums.DataPermissionType;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @version $ RefundResp, v 0.1 2024/3/25 15:45 wancheng.qu Exp $
 */
@Data
public class RefundTrialResp implements Serializable {

    @ApiModelProperty("退款金额(元)")
    private BigDecimal refundAmt;

    @ApiModelProperty("退款方式")
    private String refundType;

    @ApiModelProperty("退款类型,ONLINE_REFUND:线上原路原退,OFFLINE_REFUND:线下退款")
    private String refundMethod;

    @ApiModelProperty("绑卡")
    private String bankCardId;

    @ApiModelProperty("退款客户姓名")
    private String userName;

    @ApiModelProperty("退款银行")
    private String bankName;

    @ApiModelProperty("退款银行账号")
    @DataPermission(type = DataPermissionType.MASK_CARDNO)
    private String bankCardNo;

    @ApiModelProperty("失败原因")
    private String reason;

    @ApiModelProperty(value = "借据号")
    private String loanNo;

    @ApiModelProperty(value = "订单号")
    private String orderNo;

}