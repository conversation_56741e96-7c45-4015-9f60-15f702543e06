package com.xinfei.vocmng.biz;


import lombok.extern.slf4j.Slf4j;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.web.servlet.ServletComponentScan;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.web.bind.annotation.RestController;

@SpringBootApplication(scanBasePackages = {"com.xinfei"})
@ServletComponentScan
@RestController
@MapperScan({"com.xinfei.vocmng.dal.mapper","com.xinfei.vocmng.dal"})
@Slf4j
@EnableFeignClients
@EnableAsync
public class Application {
    public static void main(String[] args) {
        SpringApplication.run(Application.class, args);
        log.info("***** vocmng start success *****");
    }

}
