/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xinfei.vocmng.biz.model.req;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @version $ OverPayRecordsReq, v 0.1 2024-05-22 14:13 junjie.yan Exp $
 */
@Data
public class OverPayRecordsReq {
    @ApiModelProperty(value = "还款方式,支付宝:zfb,银行卡:bank", name = "还款方式,支付宝:zfb,银行卡:bank", required = true)
    @NotNull
    private String channelCode;

    @ApiModelProperty(value = "交易流水号", name = "交易流水号", required = true)
    @NotNull
    private String transNo;
}