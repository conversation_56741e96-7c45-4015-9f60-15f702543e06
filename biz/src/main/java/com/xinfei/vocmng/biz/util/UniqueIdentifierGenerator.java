/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2023 All Rights Reserved.
 */
package com.xinfei.vocmng.biz.util;

/**
 *
 * <AUTHOR>
 * @version $ UniqueIdentifierGenerator, v 0.1 2023/12/26 20:42 wancheng.qu Exp $
 */

import cn.hutool.core.lang.UUID;
import net.sourceforge.pinyin4j.PinyinHelper;

public class UniqueIdentifierGenerator {

    public static String getUserIdentify(){
        return UUID.randomUUID().toString();
    }

    // 员工手机号和姓名都可以修改，不修改的话可作为唯一标识
    public static String generateUniqueIdentifier(String name, String phoneNumber) {
        if (name == null || phoneNumber == null) {
            throw new IllegalArgumentException("Name and phoneNumber cannot be null");
        }

        String namePinyin = convertToPinyin(name);
        long phoneNumberLong = Long.parseLong(phoneNumber);
        long phoneNumberTripled = phoneNumberLong * 3;

        return namePinyin + phoneNumberTripled;
    }

    private static String convertToPinyin(String input) {
        StringBuilder pinyin = new StringBuilder(input.length() * 3);
        for (char c : input.toCharArray()) {
            String pinyinString = convertCharToPinyin(c);
            pinyin.append(pinyinString);
        }
        return pinyin.toString().replace("1", "");
    }

    private static String convertCharToPinyin(char c) {
        if (Character.toString(c).matches("[\\u4E00-\\u9FA5]+")) {
            // 如果是中文字符，进行拼音转换
            String[] pinyinArray = PinyinHelper.toHanyuPinyinStringArray(c);
            if (pinyinArray != null && pinyinArray.length > 0) {
                return pinyinArray[0];
            }
        }
        return Character.toString(c);
    }

    public static void main(String[] args) {
        String name = "张三";
        String phoneNumber = "1234567890";

        String uniqueIdentifier = generateUniqueIdentifier(name, phoneNumber);
        System.out.println("Unique Identifier: " + uniqueIdentifier);
    }
}




