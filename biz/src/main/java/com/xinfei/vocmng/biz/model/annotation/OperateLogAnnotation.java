/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2023 All Rights Reserved.
 */
package com.xinfei.vocmng.biz.model.annotation;

/**
 *
 * <AUTHOR>
 * @version $ OperateLogAnnotation, v 0.1 2023/12/29 11:41 wancheng.qu Exp $
 */
import com.xinfei.vocmng.biz.model.enums.OperateType;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
public @interface OperateLogAnnotation {
    String description() default ""; // 操作描述
    OperateType type() default OperateType.TYPE_ONE; // 操作类型，默认为 TYPE_ONE
}