/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2023 All Rights Reserved.
 */
package com.xinfei.vocmng.biz.config;

import apollo.com.google.gson.JsonObject;
import com.ctrip.framework.apollo.spring.annotation.ApolloJsonValue;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.Getter;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR> 2025/03/20 15:52
 * DepartmentNameSourceConfig
 */
@Getter
@Component
@RefreshScope
public class DepartmentNameSourceConfig {

    @ApolloJsonValue("${departmentNameSourceList}")
    private List<JsonObject> departmentNameSourceList;

}