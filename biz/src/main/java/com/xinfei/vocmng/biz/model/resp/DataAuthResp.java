/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2023 All Rights Reserved.
 */
package com.xinfei.vocmng.biz.model.resp;


import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 *
 * <AUTHOR>
 * @version $ DataAuthResp, v 0.1 2023/12/20 17:42 wancheng.qu Exp $
 */
@Data
public class DataAuthResp implements Serializable {

    @ApiModelProperty(value = "主键ID")
    private Long id;

    @ApiModelProperty(value = "数据信息")
    private String data;

    @ApiModelProperty(value = "数据类型，1:信息掩码")
    private Integer dataType;

}