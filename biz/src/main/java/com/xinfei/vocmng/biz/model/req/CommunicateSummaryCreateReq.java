/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2023 All Rights Reserved.
 */
package com.xinfei.vocmng.biz.model.req;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @version $ CommunicateSummaryCreateReq, v 0.1 2023/12/19 13:57 wancheng.qu Exp $
 */
@Data
public class CommunicateSummaryCreateReq implements Serializable {

    @ApiModelProperty(value = "主键ID")
    private Long id;

    @ApiModelProperty(value = "一级问题类型分类")
    @NotBlank(message = "一级问题类型分类为空")
    private Long issueCategoryLv1;

    @ApiModelProperty(value = "二级问题类型分类")
    @NotBlank(message = "二级问题类型分类为空")
    private Long issueCategoryLv2;

    @ApiModelProperty(value = "三级问题类型分类")
    private Long issueCategoryLv3;

    @ApiModelProperty(value = "进线来源：1热线 2在线 3其他 4:IB贷后 5：投诉 6：客户回访")
    @NotBlank(message = "进线来源为空")
    private Integer source;

    @ApiModelProperty(value = "订单号")
    private String orderNo;

    @ApiModelProperty(value = "飞享会员订单号")
    private String vipOrderNo;

    @ApiModelProperty(value = "飞享会员订单号")
    private String sVipOrderNo;

    @ApiModelProperty(value = "进线电话")
    private String telephone;

    @ApiModelProperty(value = "回电手机")
    private String callBackMobile;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "userNo")
    private Long userId;

    @ApiModelProperty(value = "custNo")
    private String custNo;

    @ApiModelProperty(value = "通话编号")
    private String callId;

    @ApiModelProperty(value = "会话小结状态：1待补充 2跟进中 3已完成")
    private Integer status;

    @ApiModelProperty(value = "创建人身份标识")
    private String createUserIdentify;

    @ApiModelProperty(value = "中继号")
    private String relayNumber;

    @ApiModelProperty(value = "用户手机")
    private String regMobile;


}