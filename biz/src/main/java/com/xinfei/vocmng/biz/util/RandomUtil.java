/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xinfei.vocmng.biz.util;

import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.UUID;

/**
 * <AUTHOR>
 * @version $ RandomUtil, v 0.1 2024/2/20 17:06 wancheng.qu Exp $
 */

public class RandomUtil {

    public static String get5Random() {
        String input = UUID.randomUUID().toString();
        try {
            MessageDigest digest = MessageDigest.getInstance("SHA-256");
            digest.update(input.getBytes());
            byte[] hash = digest.digest();
            // 将字节数组的前16个字节转换为十六进制字符串表示
            StringBuilder hexString = new StringBuilder();
            for (byte b : hash) {
                String hex = Integer.toHexString(0xff & b);
                if (hex.length() == 1) {
                    hexString.append('0');
                }
                hexString.append(hex);
            }
            String res =hexString.toString();
            return res.substring(0, Math.min(res.length(), 5));
        } catch (NoSuchAlgorithmException e) {}
        return input.substring(0, Math.min(input.length(), 5));
    }

}