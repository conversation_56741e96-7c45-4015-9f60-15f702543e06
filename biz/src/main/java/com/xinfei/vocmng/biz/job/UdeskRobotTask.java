/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xinfei.vocmng.biz.job;

import com.xinfei.vocmng.biz.component.RepaymentTaskManage;
import com.xinfei.vocmng.biz.service.UDeskService;
import com.xinfei.vocmng.biz.util.LocalDateTimeUtils;
import com.xinfei.vocmng.biz.util.RedisUtils;
import com.xinfei.vocmng.itl.util.LogUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @version $ UdeskRobotTask, v 0.1 2025-06-11 14:22 pengming.liu Exp $
 */
@Component
@Slf4j
public class UdeskRobotTask extends RepaymentTaskManage {

    @Resource
    private UDeskService uDeskService;

    @Value(value = "${udesk.Sync:false}")
    private Boolean isOpen;

    @Value(value = "${udesk.uDeskRobotSync:0 0/1 * * * ?}")
    private String cron;

    private static final String redisKey = "uDeskRobotDetailsSync";

    @Override
    protected String getCron() {
        return cron;
    }

    @Override
    protected void processTask() {
        if (isOpen) {
                LocalDateTime now = LocalDateTime.now();
                String startTime = LocalDateTimeUtils.format(now.minusMinutes(2));
                String endTime = LocalDateTimeUtils.format(now.minusMinutes(1));
                uDeskService.robotDetailsSync(redisKey, startTime, endTime);
        }
    }
}