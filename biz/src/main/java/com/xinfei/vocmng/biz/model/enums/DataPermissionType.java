/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2023 All Rights Reserved.
 */
package com.xinfei.vocmng.biz.model.enums;

import com.xinfei.vocmng.biz.util.DataMaskingUtil;
import com.xinfei.vocmng.dal.po.DictDetail;
import lombok.Getter;

import java.util.List;

/**
 *
 * <AUTHOR>
 * @version $ DataPermissionType, v 0.1 2023/12/24 16:54 wancheng.qu Exp $
 */
@Getter
public enum DataPermissionType {
    NONE("none", "无数据权限"),
    MASK_TELE("mask_tele", "手机号掩码"),//，前2后3
    MASK_NAME("mask_name", "姓名掩码"),//隐藏最后一个字举例：施小*
    MASK_IDCARD("mask_idcard", "身份证掩码"),//,展示前5后4
    MASK_ADDRESS("mask_address", "地址掩码"),//,展示省市，不展示具体地址明细
    MASK_MONEY("mask_money", "资金池掩码"),//展示资方编号，不展示资方名称 举例： 哈银消金140 ==》****140
    MASK_CARDNO("mask_cardno", "银行卡号掩码"),//前6后4，举例621660******4341
    MASK_ALIPAY("mask_alipay", "支付宝账号掩码");//前3后2

    private final String code;
    private final String desc;

    DataPermissionType(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static String getNameByCode(String code) {
        for (DataPermissionType type : DataPermissionType.values()) {
            if (type.code.equals(code)) {
                return type.getDesc();
            }
        }
        return null;
    }



    public String maskData(String data, List<DictDetail> list) {
        switch (this) {
            case MASK_TELE:
                return DataMaskingUtil.maskTele(data);
            case MASK_NAME:
                return DataMaskingUtil.maskName(data);
            case MASK_IDCARD:
                return DataMaskingUtil.maskIdCard(data);
            case MASK_ADDRESS:
                return DataMaskingUtil.maskAddress(data);
            case MASK_MONEY:
                return DataMaskingUtil.maskMoney(data,list);
            case MASK_CARDNO:
                return DataMaskingUtil.maskCardNo(data);
            case MASK_ALIPAY:
                return DataMaskingUtil.maskAlipay(data);
            default:
                return data;
        }
    }

    public List<String> maskDataList(List<String> data, List<DictDetail> list) {
        if (this == DataPermissionType.MASK_MONEY) {
            return DataMaskingUtil.maskMoneyList(data, list);
        }
        return data;
    }

}
