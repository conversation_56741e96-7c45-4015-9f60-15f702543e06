package com.xinfei.vocmng.biz.remote.impl;

import com.google.common.collect.Lists;
import com.xinfei.vocmng.biz.config.VocConfig;
import com.xinfei.vocmng.biz.model.base.PageResultResponse;
import com.xinfei.vocmng.biz.remote.WorkOrderRemoteService;
import com.xinfei.vocmng.biz.rr.dto.WorkOrderDetailDto;
import com.xinfei.vocmng.biz.rr.dto.WorkOrderSimpleDto;
import com.xinfei.vocmng.biz.rr.request.QueryWorkOrderMobileRequest;
import com.xinfei.vocmng.biz.rr.request.QueryWorkOrderRequest;
import com.xinfei.vocmng.biz.rr.request.QueryWorkOrderUrlRequest;
import com.xinfei.vocmng.itl.WorkOrderFeignClient;
import com.xinfei.vocmng.itl.rr.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @version $ WorkOrderRemoteServiceImpl, v 0.1 2024/1/15 20:11 qu.lu Exp $
 */
@Slf4j
@Service
public class WorkOrderRemoteServiceImpl implements WorkOrderRemoteService {

    @Autowired
    private WorkOrderFeignClient workOrderFeignClient;

    @Autowired
    private VocConfig vocConfig;

    @Override
    public PageResultResponse<WorkOrderDetailDto> queryWorkOrderDetail(QueryWorkOrderRequest request) {
        try{
            WorkOrderRequest workOrderRequest = buildWorkOrderRequest(request);
            WorkOrderResponse<WorkOrderListInfo> response = workOrderFeignClient.queryWorkOrderInfo(workOrderRequest);
            if(response.isFailed()){
                log.error("query work detail failed, request={},response={}",request,response);
                return null;
            }

            return new PageResultResponse<>(convertOrderDetail(response.getData().getList()),request.getCurrentPage(),request.getPageSize(),response.getData().getTotal());
        }catch (Exception e){
            log.error("query work order detail failed, request="+request,e);
        }

        return null;
    }

    @Override
    public PageResultResponse<WorkOrderDetailDto> queryWorkMobileDetail(QueryWorkOrderMobileRequest request) {
        try {
            WorkOrderMobileRequest workOrderRequest = buildWorkMobileRequest(request);
            WorkOrderResponse<WorkOrderListInfo> response = workOrderFeignClient.queryWorkMobileInfo(workOrderRequest);
            if (response.isFailed()) {
                log.error("query work detail failed, request={},response={}", request, response);
                return null;
            }

            return new PageResultResponse<>(convertOrderDetail(response.getData().getList()), request.getCurrentPage(), request.getPageSize(), response.getData().getTotal());
        } catch (Exception e) {
            log.error("query work order detail failed, request=" + request, e);
        }

        return null;
    }

    @Override
    public WorkOrderSimpleDto loadWorkOrderUrl(QueryWorkOrderUrlRequest request) {
        try{
            WorkOrderResponse<WorkOrderSimple> response = workOrderFeignClient.loadWorkOrderInfo(buildWorkOrderSummaryRequest(request));
            if(response.isFailed()){
                log.error("load work order url failed, request={},response={}",request,response);
                return null;
            }

            WorkOrderSimple simple = response.getData();
            if(simple == null){
                return null;
            }

            return new WorkOrderSimpleDto(simple.getId(), buildWorkOrderUrl(simple));
        }catch (Exception e){
            log.error("load work order url failed, request="+request,e);
        }

        return null;
    }

    private String buildWorkOrderUrl(WorkOrderSimple simple){
        return String.format(vocConfig.getWorkOrderUrlFormat(),simple.getId(),simple.getStatus(),simple.getSource(),simple.getSceneId(),simple.getTypeId(),simple.getQuestionTypeId());
    }

    private WorkOrderBySummaryRequest buildWorkOrderSummaryRequest(QueryWorkOrderUrlRequest request){
        WorkOrderBySummaryRequest summaryRequest = new WorkOrderBySummaryRequest();
        summaryRequest.setCommunitySummaryId(request.getCommunitySummaryId());

        return summaryRequest;
    }

    private List<WorkOrderDetailDto> convertOrderDetail(List<WorkOrderDetail> detailList){
        if(CollectionUtils.isEmpty(detailList)){
            return Collections.emptyList();
        }

        List<WorkOrderDetailDto> result = Lists.newArrayListWithCapacity(detailList.size());
        WorkOrderDetailDto dto;
        for (WorkOrderDetail detail : detailList){
            dto = new WorkOrderDetailDto();
            BeanUtils.copyProperties(detail,dto);
            result.add(dto);
        }

        return result;
    }

    @Override
    public void addWorkOrderFeedback(AddFeedbackRequest request) {
        WorkOrderResponse<Object> response = workOrderFeignClient.addFeedback(request);
        log.info("WorkOrderRemoteServiceImpl#addWorkOrderFeedback,request:{}", request);
        if (response.isFailed()) {
            log.error("add work order feedback failed, request={}, response={}", request, response);
        }
        log.info("add work order feedback success, taskId={}", request.getTaskId());
    }

    @Override
    public void finishWorkOrder(FinishTaskRequest request) {
        WorkOrderResponse<Object> response = workOrderFeignClient.finishTask(request);
        log.info("WorkOrderRemoteServiceImpl#finishWorkOrder,request:{}", request);
        if (response.isFailed()) {
            log.error("finish work order failed, request={}, response={}", request, response);
            throw new RuntimeException("工单完结失败");
        }
        log.info("finish work order success, taskId={}", request.getTaskId());
    }

    private WorkOrderRequest buildWorkOrderRequest(QueryWorkOrderRequest request){
        WorkOrderRequest result = new WorkOrderRequest();
        result.setOrderNo(request.getOrderNo());
        result.setPage(request.getCurrentPage());
        result.setPageSize(request.getPageSize());
        return result;
    }

    private WorkOrderMobileRequest buildWorkMobileRequest(QueryWorkOrderMobileRequest request){
        WorkOrderMobileRequest result = new WorkOrderMobileRequest();
        result.setMobiles(request.getMobiles());
        result.setPage(request.getCurrentPage());
        result.setPageSize(request.getPageSize());
        return result;
    }
}
