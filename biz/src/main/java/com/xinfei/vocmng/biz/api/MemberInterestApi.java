package com.xinfei.vocmng.biz.api;

import com.xinfei.supervip.interfaces.model.admin.dto.VipOpsResultAdminDTO;
import com.xinfei.vipcore.facade.rr.dto.RefundApplyResDto;
import com.xinfei.vipcore.facade.rr.dto.RefundStartDto;
import com.xinfei.vocmng.biz.model.base.PageResultResponse;
import com.xinfei.vocmng.biz.model.resp.ApiResponse;
import com.xinfei.vocmng.biz.rr.RightCardPackInfoDto;
import com.xinfei.vocmng.biz.rr.dto.*;
import com.xinfei.vocmng.biz.rr.request.*;
import com.xinfei.vocmng.biz.rr.response.CardRefund;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import javax.validation.Valid;
import java.util.List;
import java.util.Map;

/**
 * 会员权益相关接口
 *
 * <AUTHOR>
 * @version $ MemberInterestApi, v 0.1 2023/12/21 20:23 qu.lu Exp $
 */
@Api(tags = "会员权益相关接口")
@Validated
public interface MemberInterestApi {

    @ApiOperation("查询会员卡列表信息接口")
    @PostMapping("/member/card/list")
    ApiResponse<List<MemberCardDto>> queryMemberCardList(@RequestBody QueryMemberCardListRequest request);

    @ApiOperation("飞享会员停止续费")
    @PostMapping("/renew/card/stop")
    ApiResponse<Boolean> renewStopAdmin(@RequestBody RenewStopRequest request);

    @ApiOperation("飞享会员取消扣款")
    @PostMapping("/vip/card/stopWithhold")
    ApiResponse<Boolean> stopWithhold(@Validated @RequestBody StopWithholdRequest stopWithholdRequest);

    @ApiOperation("根据会员卡ID查询会员卡权益使用明细信息")
    @PostMapping("/member/card/used/info")
    ApiResponse<List<MemberCardUseInfoDto>> queryMemberCardUsedInfo(@RequestBody QueryMemberCardUsedListRequest request);

    @ApiOperation("查询权益卡信息")
    @PostMapping("/right/card/info")
    ApiResponse<RightCardPackInfoDto> loadRightCardDetail(@RequestBody LoadRightCardRequest request);


    //会员卡退款流程
    @ApiOperation("查询会员卡可退金额")
    @PostMapping("/vip/card/refundInfo")
    ApiResponse<List<RightCardRefundDto>> queryRightCardRefund(@RequestBody List<VipCardRefundLog> request);

    @ApiOperation("会员卡原路原退明细")
    @PostMapping("/vip/card/vipRefundDetail")
    ApiResponse<List<VipRefundDetailDto>> vipRefundDetail(@Validated @RequestBody List<VipRefundDetailReq> request);

    @ApiOperation("会员卡退费申请")
    @PostMapping("/vip/card/refund")
    ApiResponse<RefundApplyResDto> vipCardRefundApply(@RequestBody @Valid List<VipCardRefundApply> request);

    @ApiOperation("会员卡立即退款")
    @PostMapping("/vip/card/refundNow")
    ApiResponse<RefundStartDto> vipCardRefundStart(@Validated @RequestBody VipCardRefundApplyStart request);

    @ApiOperation("会员卡退款撤销")
    @PostMapping("/vip/card/refundCancel")
    ApiResponse<RefundStartDto> vipCardRefundCancel(@Validated @RequestBody VipCardRefundApplyStart request);

    @ApiOperation("查询会员卡退卡记录")
    @PostMapping("/vip/card/refundLog")
    ApiResponse<Map<String, List<CardRefund>>> queryRightCardRefundList(@Validated @RequestBody VipCardRefundLogReq request);

    @ApiOperation("查询会员卡加黑")
    @PostMapping("/vip/card/blackRecord")
    ApiResponse<Boolean> queryVipBlack(@Validated @RequestBody VipCardBlackReq request);

    @ApiOperation("会员卡加黑")
    @PostMapping("/vip/card/black")
    ApiResponse<Boolean> vipBlack(@Validated @RequestBody VipCardBlackReq request);

    @ApiOperation("会员卡支付明细")
    @PostMapping("/vip/order/payLog")
    ApiResponse<List<VipOrderPayLogDetailDTO>> orderPayLog(@Validated @RequestBody VipOrderPayLogRequest request);

    //会员卡减免方案
    @ApiOperation("查询会员卡减免信息")
    @PostMapping("/vip/card/discountInfo")
    ApiResponse<RightCardDiscountDto> queryRightCardDiscount(@RequestBody @Valid VipCardRefundLog request);

    @ApiOperation("发送会员卡减免方案")
    @PostMapping("/vip/card/reduceApply")
    ApiResponse<VipOpsResultAdminDTO> reduceApply(@RequestBody @Valid ReduceApplyRequest request);

    @ApiOperation("失效减免方案")
    @PostMapping("/vip/card/cancelVipReduceApply")
    ApiResponse<VipOpsResultAdminDTO> cancelVipReduceApply(@RequestBody @Valid CancelVipReduceApplyRequest request);

    @ApiOperation("减免方案明细")
    @PostMapping("/vip/card/reduceDetail")
    ApiResponse<VipOrderReduceDetailAdmin> reduceDetail(@RequestBody @Valid CancelVipReduceApplyRequest request);

    @ApiOperation("减免方案列表")
    @PostMapping("/vip/card/queryVipReduceList")
    ApiResponse<PageResultResponse<VipOrderReducePriceApplyAdmin>> queryVipReduceList(@RequestBody @Valid CardDiscountListRequest request);

    @ApiOperation("会员卡减免计算")
    @PostMapping("/vip/card/cardDeductionCalculation")
    ApiResponse<CardDeductionCalculationDto> cardDeductionCalculation(@RequestBody @Valid CardDeductionCalculationRequest request);
}
