package com.xinfei.vocmng.biz.model.annotation;

import com.xinfei.vocmng.itl.model.enums.ThirdPartyServiceEnum;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 请求头构建注解
 * <AUTHOR>
 * @since 2023/12/17
 */
@Target(ElementType.TYPE)
@Retention(RetentionPolicy.RUNTIME)
public @interface ProcessRule {
    /** 应用名称 */
    ThirdPartyServiceEnum service();
}
