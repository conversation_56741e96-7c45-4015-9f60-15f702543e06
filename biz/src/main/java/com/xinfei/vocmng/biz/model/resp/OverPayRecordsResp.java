/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xinfei.vocmng.biz.model.resp;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @version $ RecordListByTransNoResp, v 0.1 2024-05-22 14:21 junjie.yan Exp $
 */
@Data
public class OverPayRecordsResp {
    @ApiModelProperty("退款流水ID")
    private String reqNo;
    @ApiModelProperty("交易流水号")
    private String transNo;
    @ApiModelProperty("退款金额")
    private BigDecimal refundAmount;

    @ApiModelProperty("退款方式code")
    private String refundMethod;
    @ApiModelProperty("退款方式")
    private String refundMethodDesc;
    @ApiModelProperty("退款状态code")
    private Integer status;
    @ApiModelProperty("退款状态desc")
    private String statusDesc;
    @ApiModelProperty("退款客户姓名")
    private String payeeName;

    @ApiModelProperty(value = "转账渠道,zfb,bank")
    private String channelCode;

    @ApiModelProperty("退款类型code")
    private String refundChannelCode;

    @ApiModelProperty("退款类型Desc")
    private String refundChannelCodeDesc;
    @ApiModelProperty("退款账号")
    private String payeeAccNo;
    @ApiModelProperty("退款银行")
    private String payeeBankName;
    @ApiModelProperty("坐席名称")
    private String applyOp;
    @ApiModelProperty("创建时间")
    private LocalDateTime createdTime;
    @ApiModelProperty("退款时间")
    private LocalDateTime applyTime;
    @ApiModelProperty("退款完成时间")
    private LocalDateTime completeTime;

}