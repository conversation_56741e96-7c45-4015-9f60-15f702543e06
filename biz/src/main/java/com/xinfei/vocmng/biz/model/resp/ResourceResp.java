/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2023 All Rights Reserved.
 */
package com.xinfei.vocmng.biz.model.resp;


import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 *
 * <AUTHOR>
 * @version $ ResourceResp, v 0.1 2023/12/20 17:42 wancheng.qu Exp $
 */
@Data
public class ResourceResp implements Serializable {

    @ApiModelProperty(value = "主键ID")
    private Long id;

    @ApiModelProperty(value = "资源名称")
    private String name;

    @ApiModelProperty(value = "父资源ID")
    private Long parentId;

    @ApiModelProperty(value = "资源排序")
    private Integer orderIndex;

    @ApiModelProperty(value = "权限标识")
    private String permissionIdentify;

    @ApiModelProperty(value = "资源编码")
    private String code;

    @ApiModelProperty(value = "资源路径")
    private String path;

    @ApiModelProperty(value = "资源类型：1菜单 2页面 3按钮")
    private Integer type;

}