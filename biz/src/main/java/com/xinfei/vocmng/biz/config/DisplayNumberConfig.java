/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xinfei.vocmng.biz.config;

import apollo.com.google.gson.Gson;
import apollo.com.google.gson.JsonObject;
import com.ctrip.framework.apollo.spring.annotation.ApolloJsonValue;
import lombok.Getter;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @version $ LabelSolutionConfig, v 0.1 2024-01-09 15:37 junjie.yan Exp $
 */
@Getter
@Component
@RefreshScope
public class DisplayNumberConfig {

    @ApolloJsonValue("${udeskDisplayNumber}")
    private List<JsonObject> udeskDisplayNumber;

}