/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xinfei.vocmng.biz.util;

import javax.activation.DataHandler;
import javax.activation.DataSource;
import javax.activation.FileDataSource;
import javax.mail.*;
import javax.mail.internet.*;
import javax.mail.util.ByteArrayDataSource;
import java.io.*;
import java.net.URL;
import java.nio.file.*;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

import com.sun.mail.util.MailSSLSocketFactory;
import com.xinfei.vocmng.itl.client.exeception.ClientException;
import com.xinfei.vocmng.itl.client.exeception.ErrorLevelsEnum;
import com.xinfei.vocmng.itl.client.exeception.TechplayErrDtlEnum;
import com.xinfei.vocmng.itl.rr.LoanInfoRequest;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;

/**
 * <AUTHOR>
 * @version $ MailUtil, v 0.1 2024/6/5 16:33 wancheng.qu Exp $
 */
@Slf4j
public class MailUtil {


    public static void main(String[] args) throws Exception {
        /** sendTXMail(Arrays.asList("http://test-img.xyfstatic.com/contract-archives/xyf01/other/2024/0607/4b/2024060710504800068153_jqzm1001.pdf?OSSAccessKeyId=LTAI5tHJLgDZ6ce4YsPsCatA&Expires=1717732251&Signature=6kGe%2Bpv%2FseAZKL5PUZzKeaGlcow%3D","http://test-img.xyfstatic.com/contract-archives/xyf01/other/2024/0607/4b/2024060710504800068153_jqzm1001.pdf?OSSAccessKeyId=LTAI5tHJLgDZ6ce4YsPsCatA&Expires=1717732251&Signature=6kGe%2Bpv%2FseAZKL5PUZzKeaGlcow%3D")
         , "<EMAIL>", "<EMAIL>", "结清证明", "<EMAIL>,<EMAIL>", "尊敬的客户：\n" +
         "\n" +
         "      您好！附件为您的贷款结清证明，解压密码为您身份证号码后6位。祝您生活愉快！", "2sU9SvbR6S","587","信飞"
         );  */

        List<LoanInfoRequest> v = new ArrayList<>();
        LoanInfoRequest a = new LoanInfoRequest();
        a.setContractNumber("wfef");
        a.setLoanAmount("222");
        a.setDateSettle("2024-06-06");
        LoanInfoRequest b = new LoanInfoRequest();
        b.setContractNumber("1wfef");
        b.setLoanAmount("2122");
        b.setDateSettle("2024-06-06");
        v.add(a);
        v.add(b);
        StringBuilder contentBuilder = new StringBuilder();
        contentBuilder.append("你好").append("\n\n");
        contentBuilder.append("以下是表格数据：\n");

        // 添加表头
        contentBuilder.append(String.format("%-20s %-10s\n", "姓名", "年龄"));
        contentBuilder.append("------------------------------------\n");
        contentBuilder.append(String.format("%-20s %-10s\n", "张三", "12"));
        contentBuilder.append(String.format("%-20s %-10s\n", "里斯", "啊"));
        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        try {
            Workbook workbook = new XSSFWorkbook();
            Sheet sheet = workbook.createSheet("Users");
            Row headerRow = sheet.createRow(0);
            headerRow.createCell(0).setCellValue("申请日期");
            headerRow.createCell(1).setCellValue("客户姓名");
            headerRow.createCell(2).setCellValue("身份证");
            headerRow.createCell(3).setCellValue("合同编号");
            headerRow.createCell(4).setCellValue("借款金额");
            headerRow.createCell(5).setCellValue("账单结清时间");
            int rowNum = 1;
            for (LoanInfoRequest user : v) {
                Row row = sheet.createRow(rowNum++);
                row.createCell(0).setCellValue(LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));
                row.createCell(1).setCellValue("小明");
                row.createCell(2).setCellValue("1212321232");
                row.createCell(3).setCellValue(user.getContractNumber());
                row.createCell(4).setCellValue(user.getLoanAmount());
                row.createCell(5).setCellValue(user.getDateSettle());
            }
            workbook.write(outputStream);
            sendExcelMail(outputStream, "<EMAIL>", "<EMAIL>",
                    "【加急】申请结清证明", "",
                    contentBuilder.toString(), "2sU9SvbR6S",
                    "587", MimeUtility.encodeText("申请结清证明.xlsx", "UTF-8", "B"),
                    "信飞"
            );
        } catch (Exception e) {
            throw new ClientException(TechplayErrDtlEnum.CLIENT_CODE_ERROR, "Excel export failed", ErrorLevelsEnum.ERROR);
        }
    }

    /**
     * @param urls:       附件文件集合
     * @param sendMail:   发件人
     * @param toMail:     收件人，多个逗号分隔
     * @param sub:        主题
     * @param recipients: 抄送人，多个逗号分隔
     * @param text:       内容
     * @return void
     * <AUTHOR>
     * @description 支持附件是压缩包
     * @date 2024/6/5 16:44
     */
    public static void sendTXMail(List<String> urls, String sendMail, String toMail, String sub, String recipients, String text, String password, String port, String person) throws Exception {
        String zipFilePath = MimeUtility.encodeText("附件.zip", "UTF-8", "B");
        createZipFromUrls(urls, zipFilePath);
        try {
            sendEmailWithAttachment(sendMail, password, toMail, recipients, zipFilePath, null, sub, text, port, null, person);
        } catch (Exception e) {
            log.error("sendTXMail error,tomail={}", toMail, e);
            throw new RuntimeException("sendTXMail error");
        }
        Files.deleteIfExists(Paths.get(zipFilePath));
    }

    public static void sendExcelMail(ByteArrayOutputStream file, String sendMail, String toMail, String sub, String recipients, String text, String password, String port, String fileName, String person) throws Exception {
        try {
            sendEmailWithAttachment(sendMail, password, toMail, recipients, null, file, sub, text, port, fileName, person);
        } catch (Exception e) {
            log.error("sendExcelMail error,tomail={}", toMail, e);
            throw new ClientException("sendTXMail error");
        }
    }

    public static void createZipFromUrls(List<String> urls, String zipFilePath) throws IOException {
        Set<String> existingEntries = new HashSet<>();
        try (ZipOutputStream zipOut = new ZipOutputStream(Files.newOutputStream(Paths.get(zipFilePath)))) {
            for (String fileUrl : urls) {
                try (InputStream in = new URL(fileUrl).openStream()) {
                    String fileName = Paths.get(new URL(fileUrl).getPath()).getFileName().toString();
                    fileName = generateUniqueFileName(fileName, existingEntries);
                    ZipEntry zipEntry = new ZipEntry(fileName);
                    zipOut.putNextEntry(zipEntry);
                    IOUtils.copy(in, zipOut);
                    zipOut.closeEntry();
                } catch (IOException e) {
                    log.error("Failed to download or add file to zip: " + fileUrl, e);
                    throw new RuntimeException(e);
                }
            }
        }

    }

    private static String generateUniqueFileName(String fileName, Set<String> existingEntries) {
        String baseName = fileName;
        String extension = "";
        int dotIndex = fileName.lastIndexOf(".");
        if (dotIndex > 0) {
            baseName = fileName.substring(0, dotIndex);
            extension = fileName.substring(dotIndex);
        }

        int counter = 1;
        String uniqueFileName = fileName;
        while (existingEntries.contains(uniqueFileName)) {
            uniqueFileName = baseName + "_" + counter + extension;
            counter++;
        }

        existingEntries.add(uniqueFileName);
        return uniqueFileName;
    }

    public static void sendEmailWithAttachment(String fromEmail, String password, String toEmails, String ccEmails, String attachmentFilePath, ByteArrayOutputStream file, String subject, String text, String port, String fileName, String person) throws Exception {
        Properties props = new Properties();
        props.put("mail.smtp.host", "smtp.exmail.qq.com");
        props.put("mail.transport.protocol", "smtp");
        props.put("mail.smtp.port", port);
        props.put("mail.smtp.auth", "true");
        props.put("mail.debug", "true");
        MailSSLSocketFactory sf = null;
        try {
            sf = new MailSSLSocketFactory();
            sf.setTrustAllHosts(true);
        } catch (Exception e) {
            log.error("ssl connect error", e);
        }
        props.put("mail.smtp.ssl.enable", "true");
        props.put("mail.smtp.ssl.socketFactory", sf);
        Session session = Session.getInstance(props, new Authenticator() {
            protected PasswordAuthentication getPasswordAuthentication() {
                return new PasswordAuthentication(fromEmail, password);
            }
        });
        session.setDebug(true);
        Message message = new MimeMessage(session);
        message.setFrom(new InternetAddress(fromEmail, person, "UTF-8"));
        message.setRecipients(Message.RecipientType.TO, InternetAddress.parse(toEmails));
        if (StringUtils.isNotBlank(ccEmails)) {
            message.setRecipients(Message.RecipientType.CC, InternetAddress.parse(ccEmails));
        }
        message.setSubject(subject);

        MimeBodyPart messageBodyPart = new MimeBodyPart();
        messageBodyPart.setText(text);

        MimeBodyPart attachmentPart = new MimeBodyPart();
        if (StringUtils.isNotBlank(attachmentFilePath)) {
            DataSource source = new FileDataSource(attachmentFilePath);
            attachmentPart.setDataHandler(new DataHandler(source));
            attachmentPart.setFileName(new File(attachmentFilePath).getName());
        } else if (file != null) {
            attachmentPart.setDataHandler(new DataHandler(new ByteArrayDataSource(file.toByteArray(), "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet")));
            attachmentPart.setFileName(fileName);
        }
        Multipart multipart = new MimeMultipart();
        multipart.addBodyPart(messageBodyPart);
        multipart.addBodyPart(attachmentPart);

        message.setContent(multipart);
        Transport.send(message);
        log.info("Email sent successfully,tomail={}", toEmails);
    }

    public static void sendEmailToSpecial(String fromEmail, String password, String toEmails, String ccEmails, List<ByteArrayOutputStream> attachmentStreams,
                                          List<String> attachmentFileNames, String subject, String text, String port, String person, boolean sendText) throws Exception {
        Properties props = new Properties();
        props.put("mail.smtp.host", "smtp.exmail.qq.com");
        props.put("mail.transport.protocol", "smtp");
        props.put("mail.smtp.port", port);
        props.put("mail.smtp.auth", "true");
        props.put("mail.debug", "true");
        MailSSLSocketFactory sf = null;
        try {
            sf = new MailSSLSocketFactory();
            sf.setTrustAllHosts(true);
        } catch (Exception e) {
            log.error("ssl connect error", e);
            throw new RuntimeException("Failed to configure SSL for email sending", e);
        }
        props.put("mail.smtp.ssl.enable", "true");
        props.put("mail.smtp.ssl.socketFactory", sf);
        Session session = Session.getInstance(props, new Authenticator() {
            protected PasswordAuthentication getPasswordAuthentication() {
                return new PasswordAuthentication(fromEmail, password);
            }
        });
        session.setDebug(true);

        Message message = new MimeMessage(session);
        message.setFrom(new InternetAddress(fromEmail, person, "UTF-8"));
        if (StringUtils.isNotBlank(toEmails)) {
            message.setRecipients(Message.RecipientType.TO, InternetAddress.parse(toEmails));
        }
        if (StringUtils.isNotBlank(ccEmails)) {
            message.setRecipients(Message.RecipientType.CC, InternetAddress.parse(ccEmails));
        }
        message.setSubject(subject);

        MimeBodyPart messageBodyPart = new MimeBodyPart();
        //发送文本还是html
        if (sendText) {
            messageBodyPart.setText(text, "UTF-8");
        } else {
            messageBodyPart.setContent(text, "text/html; charset=UTF-8");
        }
        Multipart multipart = new MimeMultipart();
        multipart.addBodyPart(messageBodyPart);

        //添加附件
        if (CollectionUtils.isNotEmpty(attachmentStreams)) {
            for (int i = 0; i < attachmentStreams.size(); i++) {
                MimeBodyPart attachmentPart = new MimeBodyPart();
                ByteArrayDataSource dataSource = new ByteArrayDataSource(
                        attachmentStreams.get(i).toByteArray(),
                        getMimeTypeByFileName(attachmentFileNames.get(i))
                );
                attachmentPart.setDataHandler(new DataHandler(dataSource));
                attachmentPart.setFileName(attachmentFileNames.get(i));
                multipart.addBodyPart(attachmentPart);
            }
        }

        message.setContent(multipart);
        Transport.send(message);
        log.info("sendEmailToSpecial Email sent successfully,tomail={}", toEmails);
    }

    private static String getMimeTypeByFileName(String fileName) {
        if (fileName.endsWith(".xlsx")) {
            return "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet";
        } else if (fileName.endsWith(".docx")) {
            return "application/vnd.openxmlformats-officedocument.wordprocessingml.document";
        } else {
            return "application/octet-stream";
        }
    }

}