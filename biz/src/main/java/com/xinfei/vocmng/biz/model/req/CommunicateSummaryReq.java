/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2023 All Rights Reserved.
 */
package com.xinfei.vocmng.biz.model.req;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.xinfei.vocmng.biz.model.base.PageRequestDto;
import com.xinfei.vocmng.biz.model.base.TimeFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 *
 * <AUTHOR>
 * @version $ CommunicateSummaryReq, v 0.1 2023/12/18 11:55 wancheng.qu Exp $
 */

@Data
public class CommunicateSummaryReq extends PageRequestDto {


    @ApiModelProperty(value = "小结id")
    private Long id;

    @ApiModelProperty(value = "筛选框userNo")
    private Long userId;

    @ApiModelProperty(value = "问题类型：0 咨询,1 投诉")
    private Integer questionType;

    @ApiModelProperty(value = "筛选框custNo")
    private String custNo;

    @ApiModelProperty(value = "进线电话")
    private String telephone;

    @ApiModelProperty(value = "注册手机号")
    private String mobile;

    @ApiModelProperty(value = "手机号查CustNo")
    private String mobileCust;

    @JsonFormat(pattern = TimeFormat.DATE_TIME)
    @ApiModelProperty(value = "开始时间")
    private LocalDateTime startTime;

    @JsonFormat(pattern = TimeFormat.DATE_TIME)
    @ApiModelProperty(value = "结束时间")
    private LocalDateTime endTime;

    @ApiModelProperty(value = "进线来源：1热线 2在线 3其他")
    private Integer source;

    @ApiModelProperty(value = "会话小结状态：1待补充 2跟进中 3已完成")
    private Integer status;

    @ApiModelProperty(value = "创建人身份标识集合")
    private List<String> createUserIdentify;

    @ApiModelProperty(value = "创建人姓名")
    private List<String> createUserNames;

    @ApiModelProperty(value = "一级问题类型分类")
    private Long issueCategoryLv1;

    @ApiModelProperty(value = "二级问题类型分类")
    private Long issueCategoryLv2;

    @ApiModelProperty(value = "三级问题类型分类")
    private Long issueCategoryLv3;

    @ApiModelProperty(value = "订单号")
    private String orderNo;

    @ApiModelProperty(value = "userNos")
    private List<String> userNos;

    @ApiModelProperty(value = "mobiles")
    private List<String> mobiles;


}