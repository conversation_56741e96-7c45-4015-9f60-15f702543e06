/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2023 All Rights Reserved.
 */
package com.xinfei.vocmng.biz.util;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.xinfei.xfframework.common.JsonUtil;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.dao.DataAccessException;
import org.springframework.data.redis.core.*;
import org.springframework.data.redis.core.script.DefaultRedisScript;
import org.springframework.stereotype.Component;
import javax.annotation.Resource;
import java.time.Duration;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Supplier;

/**
 * redis工具类
 *
 */
@Component
public class RedisUtils {

    private final Logger log = LoggerFactory.getLogger(getClass());

    @Resource
    private RedisTemplate<String, Object> redisTemplate;

    @Resource
    private StringRedisTemplate stringRedisTemplate;

    @Resource
    private ValueOperations<String, String> valueOperations;

    @Resource
    private HashOperations<String, String, Object> hashOperations;

    @Resource
    private ListOperations<String, Object> listOperations;

    @Resource
    private SetOperations<String, Object> setOperations;

    @Resource
    private ZSetOperations<String, Object> zSetOperations;

    /**
     * lua脚本，执行setNx命令同时设置有效期
     */
    String lockLuaScript = "local acquired = redis.call('SETNX', KEYS[1], ARGV[1]) " +
            "if acquired == 1 then " +
            "    redis.call('PEXPIRE', KEYS[1], tonumber(ARGV[2])) " +
            "end " +
            "return acquired";

    /**
     * 默认过期时长1天，单位：秒
     */
    public static final long DEFAULT_EXPIRE_DAYS = 60 * 60 * 24L;
    /**
     * 默认过期时长1分钟，单位：秒
     */
    public static final long DEFAULT_EXPIRE_SECONDS = 60;
    /**
     * 分布式锁默认过期时长1分钟，单位：秒
     */
    public static final long DEFAULT_LOCK_EXPIRE = 60;
    /**
     * 不设置过期时长
     */
    public static final long NOT_EXPIRE = -1;

    /**
     * 分布式锁，默认过期时间一天
     */
    public boolean lock(String key, Object value) {
        return lock(key, value, DEFAULT_EXPIRE_SECONDS);
    }

    /**
     * 分布式锁
     */
    public boolean lock(String key, Object value, long timeout) {
        return lock(key, value, timeout, TimeUnit.SECONDS);
    }

    /**
     * 分布式锁
     */
    public boolean lock(String key, Object value, long timeout, TimeUnit timeUnit) {
        if (timeout < 0) {
            timeout = DEFAULT_LOCK_EXPIRE;
        }
        boolean flag = valueOperations.setIfAbsent(key, toJson(value), Duration.ofSeconds(timeout));
        if (flag) {
            redisTemplate.expire(key, timeout, timeUnit);
        }
        return flag;
    }

    /**
     * 给指定key重新上锁
     *
     * @param key           指定key
     * @param timeoutMillis 过期时间
     */
    public void relock(String key, long timeoutMillis) {
        redisTemplate.expire(key, timeoutMillis < 0 ? DEFAULT_EXPIRE_SECONDS : timeoutMillis, TimeUnit.MILLISECONDS);
    }

    /**
     * 获取redis的setNx命令加锁
     *
     * @param lockKey               锁key
     * @param lockExpirationSeconds 锁过期时间,单位：毫秒
     * @return 1:成功,0:失败
     */
    public boolean acquireLock(String lockKey, int lockExpirationSeconds) {
        DefaultRedisScript<Long> acquireLockScript = new DefaultRedisScript<>(lockLuaScript, Long.class);
        Long acquired = redisTemplate.execute(acquireLockScript,
                Collections.singletonList(lockKey),
                1,
                String.valueOf(lockExpirationSeconds));
        return acquired != null && acquired == 1;
    }

    /**
     * 释放redis锁
     *
     * @param lockKey 锁key
     */
    public void releaseLock(String lockKey) {
        redisTemplate.delete(lockKey);
    }

    /**
     * 设置key-value并设置过期时间
     */
    public void set(String key, Object value, long expire) {
        valueOperations.set(key, toJson(value));
        if (expire != NOT_EXPIRE) {
            redisTemplate.expire(key, expire, TimeUnit.SECONDS);
        }
    }

    public void set(String key, Object value, long expire, TimeUnit timeUnit) {
        valueOperations.set(key, toJson(value));
        if (expire != NOT_EXPIRE) {
            redisTemplate.expire(key, expire, timeUnit);
        }
    }

    public void set(String key, String value, long expire, TimeUnit timeUnit) {
        valueOperations.set(key, value);
        if (expire != NOT_EXPIRE) {
            redisTemplate.expire(key, expire, timeUnit);
        }
    }

    /**
     * 设置key-value 默认过期时间一天
     */
    public void set(String key, Object value) {
        set(key, value, DEFAULT_EXPIRE_SECONDS);
    }

    /**
     * 获取指定key的值并转成指定的对象类型并设置过期时间
     *
     * @param key    指定key
     * @param clazz  需要转换的类型
     * @param expire 过期时间
     * @param <T>    返回值类型
     * @return 指定key的值
     */
    public <T> T get(String key, Class<T> clazz, long expire) {
        String value = valueOperations.get(key);
        if (expire != NOT_EXPIRE) {
            redisTemplate.expire(key, expire, TimeUnit.SECONDS);
        }
        return value == null ? null : fromJson(value, clazz);
    }

    /**
     * 获取指定key的值并转成指定的对象类型
     *
     * @param key   指定key
     * @param clazz 需要转换的类型
     * @param <T>   返回值类型
     * @return 指定key的值
     */
    public <T> T get(String key, Class<T> clazz) {
        return get(key, clazz, NOT_EXPIRE);
    }

    /**
     * 获取指定key的值并转成指定的对象类型,如果发生异常，返回null;
     *
     * @param key   指定key
     * @param clazz 需要转换的类型
     * @param <T>   返回值类型
     * @return 指定key的值
     */
    public <T> T getIfExceptionNull(String key, Class<T> clazz) {
        T result = null;
        try {
            result = get(key, clazz, NOT_EXPIRE);
        } catch (Exception e) {
            log.error("get info error:{}", e);
        }
        return result;
    }

    /**
     * 获取指定key的值并设置过期时间
     *
     * @param key    指定key
     * @param expire 过期时间
     * @return 指定key的值
     */
    public String get(String key, long expire) {
        String value = valueOperations.get(key);
        if (expire != NOT_EXPIRE) {
            redisTemplate.expire(key, expire, TimeUnit.SECONDS);
        }
        return value;
    }

    /**
     * 获取指定key的值
     *
     * @param key 指定key
     * @return key对应的值
     */
    public String get(String key) {
        return get(key, NOT_EXPIRE);
    }

    public Integer getAsInt(String key) {
        String value = get(key, NOT_EXPIRE);
        return ObjectUtil.isNotNull(value) ? Integer.parseInt(value) : 0;
    }

    /**
     * 删除key
     *
     * @param key 需要删除的key
     */
    public void delete(String key) {
        redisTemplate.delete(key);
    }

    /**
     * 判断是否存在该key
     *
     * @param key key
     * @return Boolean
     */
    public Boolean hasKey(String key) {
        return redisTemplate.hasKey(key);
    }

    /**
     * 设置过期时间
     *
     * @param key     key
     * @param timeout 过期时间
     * @param unit    时间单位
     */
    public void expire(String key, long timeout, TimeUnit unit) {
        redisTemplate.expire(key, timeout, unit);
    }

    /**
     * 获取并设置
     */
    public String getAndSet(String key, String value) {
        return valueOperations.getAndSet(key, value);
    }

    /**
     * 自增
     */
    public Long increment(String key, Long count) {
        return valueOperations.increment(key, count);
    }

    /**
     * 自减
     */
    public Long decrement(String key, Long count) {
        return valueOperations.decrement(key, count);
    }

    /**
     * 获取过期时间
     */
    public Long getExpireTime(String key) {
        return redisTemplate.opsForValue().getOperations().getExpire(key);
    }

    /**
     * Object转成JSON数据
     */
    private String toJson(Object object) {
        if (object instanceof Integer || object instanceof Long || object instanceof Float ||
                object instanceof Double || object instanceof Boolean || object instanceof String) {
            return String.valueOf(object);
        }
        return JSON.toJSONString(object);
    }

    /**
     * JSON数据，转成Object
     */
    private <T> T fromJson(String json, Class<T> clazz) {
        return JSON.toJavaObject(JSON.parseObject(json), clazz);
    }

    public void unlock(String redisLockKey) {
        redisTemplate.delete(redisLockKey);
    }

    public Boolean add(String invitationRank, String mobile, double score) {
        return redisTemplate.opsForZSet().add(invitationRank, mobile, score);
    }

    public void increaseScore(String invitationRank, String mobile) {
        zSetOperations.incrementScore(invitationRank, mobile, 1.0);
    }


    public Long getPersonRank(String invitationRank, String mobile) {
        return redisTemplate.opsForZSet().reverseRank(invitationRank, mobile);
    }

    public Double getScore(String invitationRank, String mobile) {
        return redisTemplate.opsForZSet().score(invitationRank, mobile);
    }

    public Set<ZSetOperations.TypedTuple<Object>> getRangeWithScore(String invitationRank, long start, long end) {
        return redisTemplate.opsForZSet().reverseRangeWithScores(invitationRank, start, end);
    }

    public Long count(String invitationRank, double score) {
        return redisTemplate.opsForZSet().count(invitationRank, score, score);
    }

    public Long getSize(String invitationRank) {
        return redisTemplate.opsForZSet().size(invitationRank);
    }

    public Long rightPushAll(String key, Collection<String> valueList) {
        return listOperations.rightPushAll(key, valueList);
    }

    public String leftPopStr(String key) {
        Object value = listOperations.leftPop(key);
        return value == null ? null : value.toString();
    }

    /**
     * 判断value是否是key中存在
     *
     * @param key
     * @param value
     * @return
     */
    public Boolean isMember(String key, String value) {
        return redisTemplate.opsForSet().isMember(key, value);
    }

    public Double zscore(String key, String member) {
        return stringRedisTemplate.opsForZSet().score(key, member);
    }

    public Map<Object, Object> hgetAll(String key) {
        return stringRedisTemplate.opsForHash().entries(key);
    }

    public boolean del(String key) {
        return stringRedisTemplate.delete(key);
    }

    public boolean sadd(String key, String val, Long timeout, TimeUnit unit) {

        Long effCount = redisTemplate.opsForSet().add(key, val);
        if (timeout != null && unit != null) {
            redisTemplate.expire(key, timeout, unit);
        }
        return effCount > 0;
    }

    public <T> T get(String key, Class<T> clazz, Supplier<T> supplier, Long timeout, TimeUnit unit) {

        String val = stringRedisTemplate.opsForValue().get(key);
        if (StringUtils.isEmpty(val)) {
            T t = null;
            if (supplier != null) {
                t = supplier.get();
                if (t != null) {
                    if (t instanceof String) {
                        stringRedisTemplate.opsForValue().set(key, (String) t, timeout, unit);
                    } else {
                        stringRedisTemplate.opsForValue().set(key, JsonUtil.toJson(t), timeout, unit);
                    }

                }
            }
            // 可能会是null,调用的时候如果有必要，要处理空指针
            return t;
        } else {
            if (clazz == String.class) {
                return (T) val;
            }
            return JsonUtil.parseJson(val, clazz);
        }
    }





    public boolean setnx(String key, Object value, Long expire, TimeUnit unit) {
        String valStr = value.toString();
        return stringRedisTemplate.opsForValue().setIfAbsent(key, valStr, expire, unit);
    }

    public Long lpush(String key, Object obj) {
        return stringRedisTemplate.opsForList().leftPush(key, obj.toString());
    }

    public List<String> lrange(String key, int start, int end) {
        return stringRedisTemplate.opsForList().range(key, start, end);
    }

    public void hSet(String key, String field, String value) {
        stringRedisTemplate.opsForHash().put(key, field, value);
    }

    public void hMset(String key, Map<String, String> values) {
        stringRedisTemplate.opsForHash().putAll(key, values);
    }

    public void pipeline(SessionCallback<?> callback) {
        stringRedisTemplate.executePipelined(callback);
    }

    public void remrangeByScore(String key, long min, long max) {
        stringRedisTemplate.opsForZSet().removeRangeByScore(key, min, max);
    }

    public Long ttl(String key) {
        return stringRedisTemplate.getExpire(key);
    }

    public void setex(String key, String value, long expire, TimeUnit timeUnit) {
        stringRedisTemplate.opsForValue().set(key, value, expire, timeUnit);
    }

    public String getString(String key) {
        return stringRedisTemplate.opsForValue().get(key);
    }

    /**
     * 通过multi批量删除key
     *
     * @param keyList
     */
    public void batchDeleteByMulti(Collection<String> keyList) {
        redisTemplate.multi();
        for (String key : keyList) {
            redisTemplate.delete(key);
        }
        redisTemplate.exec();
    }

    /**
     * 通过pipe批量删除key
     *
     * @param keyList
     */
    public void batchDeleteByPipeline(Collection<String> keyList) {
        pipeline(new SessionCallback<Object>() {
            @Override
            public <K, V> Object execute(RedisOperations<K, V> operations) throws DataAccessException {
                for (String redisKey : keyList) {
                    redisTemplate.delete(redisKey);
                }
                return null;
            }
        });
    }


    public List<String> mGet(List<String> keys) {
        return stringRedisTemplate.opsForValue().multiGet(keys);
    }
}
