/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xinfei.vocmng.biz.job;

import com.xinfei.vocmng.biz.component.RepaymentTaskManage;
import com.xinfei.vocmng.biz.service.CommonService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @version $ EasyComplaintUserTask, v 0.1 2025-06-18 14:22 pengming.liu Exp $
 */
@Component
@Slf4j
public class EasyComplaintUserTask extends RepaymentTaskManage {

    @Value(value = "${easyComplaintUserSync:true}")
    private Boolean isOpen;

    @Value(value = "${easyComplaintUserSync.cron:0 0 23 * * ?}")
    private String cron;

    @Override
    protected String getCron() {
        return cron;
    }

    @Resource
    private CommonService commonService;

    @Override
    protected void processTask() {
        if (isOpen) {
                commonService.easyComplaintUserSync();
        }
    }
}