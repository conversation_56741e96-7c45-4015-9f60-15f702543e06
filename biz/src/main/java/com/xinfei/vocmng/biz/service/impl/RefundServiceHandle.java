/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xinfei.vocmng.biz.service.impl;

import com.xinfei.repaytrade.facade.rr.enums.FeeSubjectEnum;
import com.xinfei.repaytrade.facade.rr.request.RefundApplyRequest;
import com.xinfei.repaytrade.facade.rr.response.RefundApplyResponse;
import com.xinfei.vocmng.biz.config.VocConfig;
import com.xinfei.vocmng.biz.constants.ReqNoConstants;
import com.xinfei.vocmng.biz.mapstruct.RefundConverter;
import com.xinfei.vocmng.biz.model.enums.ControlEnum;
import com.xinfei.vocmng.biz.model.enums.FeeStrategyEnum;
import com.xinfei.vocmng.biz.model.enums.OrderType;
import com.xinfei.vocmng.biz.model.enums.TechplayErrDtlEnum;
import com.xinfei.vocmng.biz.model.exception.IgnoreException;
import com.xinfei.vocmng.biz.model.exception.TechplayException;
import com.xinfei.vocmng.biz.model.req.RefundApplyReq;
import com.xinfei.vocmng.biz.strategy.dto.OrderRefundProcessDto;
import com.xinfei.vocmng.biz.strategy.dto.OrderRefundStrategyInput;
import com.xinfei.vocmng.biz.strategy.dto.StrategyExecutionResult;
import com.xinfei.vocmng.biz.util.LocalDateTimeUtils;
import com.xinfei.vocmng.biz.util.UserContextHolder;
import com.xinfei.vocmng.dal.dto.resp.ControlData;
import com.xinfei.vocmng.dal.dto.resp.FeeStrategyConfig;
import com.xinfei.vocmng.dal.mapper.RefundRequestMapper;
import com.xinfei.vocmng.dal.po.RefundRequest;
import com.xinfei.vocmng.itl.client.feign.impl.RefundFacadeClientImpl;
import com.xinfei.vocmng.itl.client.feign.service.CisFacadeClientService;
import com.xinfei.vocmng.itl.util.LogUtil;
import com.xinfei.xfframework.common.JsonUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.xinfei.vocmng.biz.constants.ReqNoConstants.getRequestNo;

/**
 * <AUTHOR>
 * @version $ RefundServiceHandle, v 0.1 2024-10-12 11:15 junjie.yan Exp $
 */
@Slf4j
@Component
public class RefundServiceHandle {

    @Resource
    private CisFacadeClientService cisFacadeClientService;

    @Resource
    private RefundFacadeClientImpl refundFacadeClient;

    @Resource
    private RefundRequestMapper refundRequestMapper;

    @Autowired
    private VocConfig vocConfig;

    @Resource
    private OrderRefundStrategyServiceImpl orderRefundStrategyService;

    @Transactional
    public void refundApplyHandle(RefundApplyReq req, String creator) {
        RefundRequest refundRequest = RefundConverter.INSTANCE.refundApplyReqToRefundRequest(req);
        getEncodeInfo(refundRequest);
        if (CollectionUtils.isNotEmpty(req.getRepaymentNos())) {
            refundRequest.setRepaymentNos(JsonUtil.toJson(req.getRepaymentNos()));
        }

        List<FeeSubjectEnum> reductionFeeList = getFeeSubjectEnums(req.getOrderType(), req.getLoanNo());
        refundRequest.setFeeSubjectEnum(JsonUtil.toJson(reductionFeeList));

        checkOfflineInfo(req, reductionFeeList);

        refundRequestMapper.insert(refundRequest);

        //需要审核申请单，直接落表
        if (req.getStatus() == 1) {
            return;
        }

        //无需审核申请单，落表后直接调还款引擎退款接口
        if (req.getStatus() == 0) {
            RefundApplyRequest refundApplyRequest = RefundConverter.INSTANCE.refundApplyReqToRefundApplyRequest(req);
            refundApplyRequest.setRefundFeeList(reductionFeeList);
            refundApplyRequest.setCreatedBy(creator);
            refundApplyRequest.setPlanExecuteTime(LocalDateTimeUtils.parseDateByLocalDateTime(refundRequest.getRefundTime()));
            refundApplyRequest.setRefundReason(req.getRefundReason());
            refundApplyRequest.setRequestNo(getRequestNo(ReqNoConstants.VOCRA, refundRequest.getId()));
            RefundApplyResponse response = refundFacadeClient.refundApply(refundApplyRequest);
            if (response != null) {
                refundRequest.setUpdateTime(LocalDateTime.now());
                refundRequest.setRefundOrderId(response.getRefundOrderId());
                refundRequestMapper.updateById(refundRequest);
            }
        }
    }

    /**
     * 若试算明细包含线下退款，校验必填字段
     *
     * @param req
     * @param reductionFeeList
     */
    private void checkOfflineInfo(RefundApplyReq req, List<FeeSubjectEnum> reductionFeeList) {
        //待审核时，需要取所有费控进行试算

        if ("OFFLINE_REFUND".equals(req.getRefundType())) {
            if (StringUtils.isEmpty(req.getOfflineRefundMethod()) || StringUtils.isEmpty(req.getOfflineRefundAccount()) || StringUtils.isEmpty(req.getOfflineRefundUserName())) {
                throw new IgnoreException(TechplayErrDtlEnum.REFUND_ERROR, "线下退款，请重新试算明细，需要填写退款类型、用户姓名、账号");
            }
            if ("BANK_CARD".equals(req.getOfflineRefundMethod()) && StringUtils.isEmpty(req.getOfflineRefundBank())) {
                throw new IgnoreException(TechplayErrDtlEnum.REFUND_ERROR, "线下退款类型为银行卡时，需要填写银行名称");
            }
        }
    }

    /**
     * 退款可退费项
     *
     * @param orderType
     * @return
     */
    public List<FeeSubjectEnum> getFeeSubjectEnums(String orderType, String loanNo) {
        List<FeeSubjectEnum> reductionFeeList = new ArrayList<>();

        if (vocConfig.getIsNewRefund() && OrderType.MAIN.getCode().equals(orderType)) {
            OrderRefundStrategyInput input = OrderRefundStrategyInput
                    .builder()
                    .loanNo(loanNo)
                    .build();
            StrategyExecutionResult<FeeStrategyConfig> strategyExecutionResult = orderRefundStrategyService.executeStrategy(FeeStrategyEnum.ORDER_REFUND, input);
            Map<String, String> result = strategyExecutionResult.getResult();

            for (Map.Entry<String, String> entry : result.entrySet()) {
                String key = entry.getKey();
                BigDecimal value;
                try {
                    value = new BigDecimal(entry.getValue());
                } catch (NumberFormatException e) {
                    log.warn(LogUtil.infoLog("Error parsing BigDecimal for key " + key + ", value: " + entry.getValue()));
                    continue; // 跳过这个条目
                }

                if (value.compareTo(BigDecimal.ZERO) > 0 && OrderRefundProcessDto.NEW_FEE_MAPPING.get(key) != null) {
                    reductionFeeList.add(OrderRefundProcessDto.NEW_FEE_MAPPING.get(key));
                }
            }

        } else {

            List<ControlData> controlDataList = UserContextHolder.getUserContext().getControlDataList();

            if (OrderType.PROFIT.getCode().equals(orderType)) {
                controlDataList = controlDataList.stream().filter(r -> r.getControlScene() == 3 && r.getControlChildType() == 8).collect(Collectors.toList());
            } else {
                controlDataList = controlDataList.stream().filter(r -> r.getControlScene() == 3 && r.getControlChildType() != 8).collect(Collectors.toList());
            }

            if (CollectionUtils.isEmpty(controlDataList)) {
                return reductionFeeList;
            }

            try {
                for (ControlData controlData : controlDataList) {
                    ControlEnum controlEnum = ControlEnum.getByControlChildType(controlData.getControlChildType());
                    switch (controlEnum) {
                        case GUARANTEE_REDUCTION:
                            reductionFeeList.add(FeeSubjectEnum.FEE1);
                            reductionFeeList.add(FeeSubjectEnum.FEE2);
                            break;
                        case PENALTY_REDUCTION:
                            reductionFeeList.add(FeeSubjectEnum.OINT_AMT);
                            reductionFeeList.add(FeeSubjectEnum.FEE3);
                            reductionFeeList.add(FeeSubjectEnum.FEE6);
                            break;
                        case PREPAYMENT_FEE:
                            reductionFeeList.add(FeeSubjectEnum.FEE4);
                            break;
                        case PRINCIPAL_AMOUNT:
                            reductionFeeList.add(FeeSubjectEnum.PRIN_AMT);
                            break;
                        case INTEREST_AMOUNT:
                            reductionFeeList.add(FeeSubjectEnum.INT_AMT);
                            break;
                        default:
                            break;
                    }
                }
            } catch (Exception e) {
                log.error(LogUtil.clientErrorLog("RefundServiceImp", "getFeeSubjectEnums", null, null, "现金订单可退款费项获取错误"), e);
                throw new TechplayException(TechplayErrDtlEnum.REFUND_ERROR);
            }
        }

        return reductionFeeList;
    }


    private void getEncodeInfo(RefundRequest refundRequest) {
        if (StringUtils.isNotEmpty(refundRequest.getOfflineRefundAccount())) {
            refundRequest.setOfflineRefundAccount(cisFacadeClientService.getEncodeByField("bankcard", Collections.singletonList(refundRequest.getOfflineRefundAccount())));
        }

        if (StringUtils.isNotEmpty(refundRequest.getOfflineRefundUserName())) {
            refundRequest.setOfflineRefundUserName(cisFacadeClientService.getEncodeByField("name", Collections.singletonList(refundRequest.getOfflineRefundUserName())));
        }
    }

}