/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xinfei.vocmng.biz.api;

import com.xinfei.vocmng.biz.config.VocConfig;
import com.xinfei.vocmng.biz.model.resp.ApiResponse;
import io.swagger.annotations.Api;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @version $ TestApi, v 0.1 2024-02-28 19:26 junjie.yan Exp $
 */
@Api(tags = "测试相关")
@RestController
@RequestMapping("/test")
public class TestApi {
    @Resource
    private VocConfig vocConfig;

    @GetMapping("/t")
    public ApiResponse<String> t() {
        return ApiResponse.success("通了");
    }


    @GetMapping("/getOssConfig")
    public ApiResponse<String> getOssConfig() {return ApiResponse.success(String.valueOf(vocConfig.isSsoOpenFlag()));}

}