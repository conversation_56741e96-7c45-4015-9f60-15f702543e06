/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2023 All Rights Reserved.
 */
package com.xinfei.vocmng.biz.util;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Objects;

public final class BigDecimalUtil {


    /**
     * b1是否大于b2
     * @param b1
     * @param b2
     * @return
     */
    public static boolean g(BigDecimal b1, BigDecimal b2){
        return parse(b1).compareTo(parse(b2))>0;
    }


    /**
     * b1是否大于等于b2
     * @param b1
     * @param b2
     * @return
     */
    public static boolean ge(BigDecimal b1, BigDecimal b2){
        return parse(b1).compareTo(parse(b2))>=0;
    }


    /**
     * 将BigDecimal转为String
     * @param source
     * @return
     */
    public static String toString(BigDecimal source){
        if (Objects.isNull(source)){
            return "";
        }
        return source.toPlainString();
    }


    /**
     * 将String转为BigDecimal
     * @param source
     * @return
     */
    public static BigDecimal fromString(String source){
        if (source == null){
            return null;
        }
        return new BigDecimal(source);
    }



    /**
     * 将NULL转为零值，避免空指针
     * @param source
     * @return
     */
    public static BigDecimal parse(BigDecimal source){
        if (source == null){
            return BigDecimal.ZERO;
        }
        return source;
    }


    /**
     * 加法
     * @param b1
     * @param b2
     * @return
     */
    public static BigDecimal add(BigDecimal b1, BigDecimal b2){
        return parse(b1).add(parse(b2));
    }


    /**
     * 累加
     * @param b1
     * @param b2
     * @return
     */
    public static BigDecimal add(BigDecimal b1, BigDecimal... b2){
        BigDecimal res = parse(b1);
        for (BigDecimal b : b2){
            res = add(res, b);
        }
        return res;
    }


    /**
     * b1减去b2
     * @param b1
     * @param b2
     * @return
     */
    public static BigDecimal subtract(BigDecimal b1, BigDecimal b2){
        return parse(b1).subtract(parse(b2));
    }


    /**
     * 乘法
     * @param b1
     * @param b2
     * @return
     */
    public static BigDecimal multiply(BigDecimal b1, BigDecimal b2){
        return parse(b1).multiply(parse(b2));
    }


    /**
     * b1除以b2，保留2位小数
     * @param b1
     * @param b2
     * @return
     */
    public static BigDecimal divide(BigDecimal b1, BigDecimal b2){
        return divide(b1, b2, 2);
    }


    /**
     * b1除以b2
     * @param b1
     * @param b2
     * @param scale 保留小数位
     * @return
     */
    public static BigDecimal divide(BigDecimal b1, BigDecimal b2, int scale){
        return parse(b1).divide(parse(b2), scale, RoundingMode.HALF_UP);
    }

}