package com.xinfei.vocmng.biz.model.enums;

import lombok.Getter;

/**
 *
 * <AUTHOR>
 * @version $ SenderEnum, v 0.1 2025/3/21 16:40 pengming.liu Exp $
 */
@Getter
public enum SenderEnum {
    CUSTOMER("customer", "客户"),
    AGENT("agent", "客服"),
    BLANK("blank", "未知");

    private final String code;
    private final String description;

    SenderEnum(String code, String description) {
        this.code = code;
        this.description = description;
    }

    // 根据 code 获取对应的中文描述
    public String getDescription() {
        return this.description;
    }

    // 根据 code 获取对应的枚举实例
    public static String getDescriptionByCode(String code) {
        for (SenderEnum sender : SenderEnum.values()) {
            if (sender.code.equals(code)) {
                return sender.getDescription();
            }
        }
        return "未知";  // 如果没有找到匹配的 code，返回默认值
    }
}
