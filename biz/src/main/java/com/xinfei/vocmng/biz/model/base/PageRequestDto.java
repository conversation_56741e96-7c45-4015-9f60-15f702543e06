package com.xinfei.vocmng.biz.model.base;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 分页请求基础参数-mybatis-plus
 * <p/>
 *
 */
@Setter
@Getter
@ApiModel(value = "分页请求基础参数", description = "分页请求基础参数")
public abstract class PageRequestDto {

    private static final long serialVersionUID = -8824017318196056592L;

    /**
     * 每页显示条数，默认 10，直接使用父类的属性即可
     */
    @ApiModelProperty(value = "每页显示条数，默认 10")
    protected int pageSize = 10;

    /**
     * 当前页-默认为1，直接使用父类的属性即可
     */
    @ApiModelProperty(value = "当前页-默认为1")
    protected int currentPage = 1;

    /**
     * 获取开始序号
     *
     * @return
     */
    public int getBeginNum() {
        return (currentPage - 1) * pageSize;
    }

}
