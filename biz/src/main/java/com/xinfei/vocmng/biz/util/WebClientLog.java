package com.xinfei.vocmng.biz.util;

import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.StrUtil;
import cn.hutool.core.util.URLUtil;
import com.alibaba.fastjson.JSON;
import com.xinfei.xfframework.common.JsonUtil;
import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.Tag;
import io.micrometer.core.instrument.Timer;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.net.URL;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;


@Slf4j
@Component
public class WebClientLog {
    @Resource
    private MeterRegistry meterRegistry;

    private static MeterRegistry smeterRegistry;

    @PostConstruct
    public void init() {
        smeterRegistry = meterRegistry;
    }

    public static void addLog(String url, Object headers, Object body, String res, long timeCost, int statusCode) {
        URL uri = URLUtil.url(url);
        String pureUrl = uri.getProtocol() + "://" + uri.getHost() + uri.getPath();

        if (smeterRegistry != null) {
            timer("http_request", pureUrl, Convert.toStr(statusCode), timeCost);
        }
        Map<String, Object> logContent = new HashMap<>(8);
        logContent.put("message_tag", "web_client");
        logContent.put("category", "web_client");
        logContent.put("cost", timeCost);
        logContent.put("response_status", statusCode);
        logContent.put("pure_url", pureUrl);
        logContent.put("url", url);
        logContent.put("headers", headers);

        Object resultObject;
        try {
            resultObject = JSON.parseObject(res, Object.class);
        } catch (Exception e) {
            resultObject = res;
        }
        logContent.put("body", body);
        logContent.put("res", resultObject);
        log.info(JsonUtil.toJson(logContent));
    }

    /**
     * 记录耗时指标
     *
     * @param name
     * @param tag
     * @param time
     */
    public static void timer(String name, HashMap<String, String> tag, long time) {
        Timer.builder("telemkt_" + name)
                .tags(buildLabel(tag))
                .publishPercentileHistogram(true)
                .publishPercentiles(0.5, 0.9, 0.95, 0.99)
                .register(smeterRegistry).record(time, TimeUnit.MILLISECONDS);
    }

    public static void timer(String name, String requestUri, String responseStatus, long timeCost) {
        HashMap<String, String> tag = new HashMap<>(4);
        tag.put("request_api", requestUri);
        tag.put("response_status", responseStatus);
        timer(name, tag, timeCost);
    }

    private static List<Tag> buildLabel(HashMap<String, String> hashMap) {
        List<Tag> list = new ArrayList<>();
        hashMap.entrySet().stream().forEach((entry) -> {
            list.add(Tag.of(entry.getKey(), StrUtil.isNotBlank(entry.getValue()) ? entry.getValue() : ""));
        });
        return list;
    }
}