/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xinfei.vocmng.biz.model.req;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @version $ AsrInfoRequest, v 0.1 2024-10-23 16:33 junjie.yan Exp $
 */
@Data
public class AsrInfoRequest {

    @ApiModelProperty(value = "会话id")
    @JsonProperty(value = "source_id")
    private String sourceId;

    @ApiModelProperty(value = "文本id")
    private String id;

    @ApiModelProperty(value = "文本开始时间")
    @JsonProperty(value = "start_timestamp")
    private Long startTimestamp;

    @ApiModelProperty(value = "文本结束时间")
    @JsonProperty(value = "end_timestamp")
    private Long endTimestamp;

    @ApiModelProperty(value = "文本内容")
    private String text;

    @ApiModelProperty(value = "文本状态")
    private String state;

    @ApiModelProperty(value = "该段文本的说话人")
    private String speaker;

}