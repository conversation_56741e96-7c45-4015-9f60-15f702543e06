package com.xinfei.vocmng.biz.model.resp;

import com.xinfei.vocmng.biz.model.req.OrderRefundRecordsResp;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2024-05-21 11:13:38
 */

@Data
public class OrderRefundRecods {

    @ApiModelProperty(value = "id")
    private Long id;

    @ApiModelProperty("借据号")
    private String loanNo;

    @ApiModelProperty("订单号")
    private String orderNo;

    @ApiModelProperty("账单号")
    private String billNo;

    @ApiModelProperty("审核状态 0：无需审核，1：待审核，2：审核通过，3：审核拒绝")
    private Integer status;

    @ApiModelProperty("申请单状态")
    private String statusStr;

    @ApiModelProperty("custNo")
    private String custNo;

    @ApiModelProperty("userNo")
    private String userNo;

    @ApiModelProperty("退款金额(元)")
    private BigDecimal refundAmount;

    @ApiModelProperty("退款方式（1：线上原路原退，2：线下退款）")
    private Integer refundType;

    @ApiModelProperty("还款时间，1：立即(2小时)，2：50天")
    private Integer executeType;

    @ApiModelProperty("自定义天数")
    private Integer executeDay;;

    @ApiModelProperty("退款时间")
    private LocalDateTime refundTime;

    @ApiModelProperty("创建时间")
    private LocalDateTime crateTime;

    @ApiModelProperty("更新时间")
    private LocalDateTime updateTime;

    @ApiModelProperty("创建人")
    private String creator;

    @ApiModelProperty("退款类型（1：订单，2：账单，3：溢缴款）")
    private Integer requestType;

    @ApiModelProperty("订单类型，PROFIT:营收订单（权益产品订单），MAIN:现金订单（信贷产品订单）")
    private String orderType;

    @ApiModelProperty("审核人")
    private String reviewer;

    @ApiModelProperty("退款单号")
    private String refundOrderId;

    @ApiModelProperty("退款原因")
    private String refundReason;

    @ApiModelProperty("退款明细")
    private List<OrderRefundRecordsResp> recordsResps;

    @ApiModelProperty("第三方退款渠道")
    private String channelCode;

    @ApiModelProperty("第三方退款渠道流水号")
    private String channelOrderNo;
}
