package com.xinfei.vocmng.biz.api;

import com.xinfei.vocmng.biz.model.resp.ApiResponse;
import com.xinfei.vocmng.biz.rr.dto.bill.RiskUserDto;
import com.xinfei.vocmng.itl.rr.GetRiskUserRequest;
import com.xinfei.vocmng.itl.rr.GetSolutionsRequest;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2023 All Rights Reserved.
 */
@Api(tags = "标签解决方案")
@RequestMapping("/label")
public interface LabelSolutionsApi {

    @ApiOperation("标签解决方案")
    @PostMapping(value = "/solution")
    ApiResponse<String> getSolutionsByScore(@RequestBody GetSolutionsRequest getSolutionsRequest);

    @ApiOperation("高风险用户查询")
    @PostMapping(value = "/getRiskUser")
    ApiResponse<RiskUserDto> getRiskUser(@Validated  @RequestBody GetRiskUserRequest getRiskUserRequest);

    @ApiOperation("易投诉用户查询")
    @PostMapping(value = "/getComplainUser")
    ApiResponse<Boolean> getComplainUser(@Validated  @RequestBody GetRiskUserRequest getRiskUserRequest);
}
