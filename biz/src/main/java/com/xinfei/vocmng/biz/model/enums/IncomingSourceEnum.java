/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2023 All Rights Reserved.
 */
package com.xinfei.vocmng.biz.model.enums;

import lombok.Getter;

/**
 * <AUTHOR>
 * @version $ DataPermissionType, v 0.1 2023/12/24 16:54 wancheng.qu Exp $
 */
@Getter
public enum IncomingSourceEnum {
    HOT_WIRE(1, "客服热线"),
    ONLINE_CUSTOMER(2, "在线客服"),
    OTHER(3, "其他"),
    IB(4, "贷后IB"),
    COMPLAINT(5, "投诉"),
    RETURN_VISIT(6, "客户回访");

    private final Integer code;
    private final String desc;

    IncomingSourceEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static String getDescByCode(Integer code) {
        for (IncomingSourceEnum value : values()) {
            if (value.code.equals(code)) {
                return value.desc;
            }
        }
        throw new IllegalArgumentException("No matching constant for code: " + code);
    }

}
