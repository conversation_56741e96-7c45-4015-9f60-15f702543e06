package com.xinfei.vocmng.biz.api.controller;

import com.xinfei.vocmng.biz.api.MergeQueueApi;
import com.xinfei.vocmng.biz.constants.RedisKeyConstants;
import com.xinfei.vocmng.biz.model.resp.ApiResponse;
import com.xinfei.vocmng.biz.service.MergeQueueService;
import com.xinfei.vocmng.biz.service.MergeTaskService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 合并队列管理Controller
 *
 * <AUTHOR>
 * @version $ MergeQueueController, v 0.1 2025/5/1 $
 */
@Slf4j
@RestController
@RequiredArgsConstructor
public class MergeQueueController implements MergeQueueApi {

    private final MergeQueueService mergeQueueService;
    private final MergeTaskService mergeTaskService;
    private final StringRedisTemplate redisTemplate;

    @Override
    public ApiResponse<Map<String, Object>> getQueueStatus() {
        try {
            Map<String, Object> status = new HashMap<>();

            // 获取队列长度
            Map<String, Long> queueSizes = mergeQueueService.getQueueSizes();
            status.put("queueSizes", queueSizes);

            // 获取webToken集合大小
            Set<String> webTokens = mergeTaskService.getAllWebTokens();
            status.put("webTokenSetSize", webTokens != null ? webTokens.size() : 0);

            // 获取已合并用户集合大小
            Set<String> mergedUsers = redisTemplate.opsForSet().members(RedisKeyConstants.MERGED_ZSET);
            status.put("mergedSetSize", mergedUsers != null ? mergedUsers.size() : 0);

            return ApiResponse.success(status);
        } catch (Exception e) {
            log.error("Failed to get queue status", e);
            return ApiResponse.fail("获取队列状态失败: " + e.getMessage());
        }
    }

    @Override
    public ApiResponse<Boolean> pushTask(String webToken) {
        try {
            if (StringUtils.isBlank(webToken)) {
                return ApiResponse.fail("webToken不能为空");
            }

            mergeTaskService.pushSingleWebToken(webToken);
            return ApiResponse.success(true);
        } catch (Exception e) {
            log.error("Failed to push task", e);
            return ApiResponse.fail("推送任务失败: " + e.getMessage());
        }
    }

    @Override
    public ApiResponse<Map<String, Object>> pushBatchTasks(Long filterId, String query) {
        try {
            if (filterId == null && StringUtils.isBlank(query)) {
                return ApiResponse.fail("filterId和query不能同时为空");
            }

            Map<String, Integer> pushResult = mergeTaskService.pushWebTokens(filterId, query);

            Map<String, Object> result = new HashMap<>();
            result.put("success", true);
            result.put("total", pushResult.get("total"));
            result.put("successCount", pushResult.get("success"));
            result.put("processingCount", pushResult.get("processing"));
            result.put("duplicateCount", pushResult.get("duplicate"));
            result.put("errorCount", pushResult.get("error"));

            return ApiResponse.success(result);
        } catch (Exception e) {
            log.error("Failed to push batch tasks", e);
            return ApiResponse.fail("批量推送任务失败: " + e.getMessage());
        }
    }

    @Override
    public ApiResponse<Map<String, Integer>> pushTaskList(List<String> webTokens) {
        try {
            if (CollectionUtils.isEmpty(webTokens)) {
                return ApiResponse.fail("webToken列表不能为空");
            }

            Map<String, Integer> result = mergeTaskService.pushWebTokenList(webTokens);
            return ApiResponse.success(result);
        } catch (Exception e) {
            log.error("Failed to push task list", e);
            return ApiResponse.fail("批量推送任务列表失败: " + e.getMessage());
        }
    }

    @Override
    public ApiResponse<Boolean> clearQueue(String queueType) {
        try {
            if (StringUtils.isBlank(queueType)) {
                return ApiResponse.fail("队列类型不能为空");
            }

            boolean success = false;

            switch (queueType.toLowerCase()) {
                case "main":
                    redisTemplate.delete(RedisKeyConstants.MERGE_QUEUE);
                    // 重新初始化队列
                    redisTemplate.opsForList().rightPush(RedisKeyConstants.MERGE_QUEUE, "INIT");
                    redisTemplate.opsForList().leftPop(RedisKeyConstants.MERGE_QUEUE);
                    success = true;
                    break;
                case "retry":
                    redisTemplate.delete(RedisKeyConstants.MERGE_RETRY_QUEUE);
                    // 重新初始化队列
                    redisTemplate.opsForList().rightPush(RedisKeyConstants.MERGE_RETRY_QUEUE, "INIT");
                    redisTemplate.opsForList().leftPop(RedisKeyConstants.MERGE_RETRY_QUEUE);
                    success = true;
                    break;
                case "processing":
                    redisTemplate.delete(RedisKeyConstants.MERGE_PROCESSING_SET);
                    // 重新初始化集合
                    redisTemplate.opsForSet().add(RedisKeyConstants.MERGE_PROCESSING_SET, "INIT");
                    redisTemplate.opsForSet().remove(RedisKeyConstants.MERGE_PROCESSING_SET, "INIT");
                    success = true;
                    break;
                case "failed":
                    // 清空失败集合
                    redisTemplate.delete(RedisKeyConstants.MERGE_FAILED_SET);
                    success = true;
                    break;
                default:
                    return ApiResponse.fail("无效的队列类型: " + queueType);
            }

            return ApiResponse.success(success);
        } catch (Exception e) {
            log.error("Failed to clear queue: {}", queueType, e);
            return ApiResponse.fail("清空队列失败: " + e.getMessage());
        }
    }

    @Override
    public ApiResponse<Map<String, Object>> getFailedTasks() {
        try {
            Set<String> failedTasks = mergeQueueService.getFailedTasks();

            Map<String, Object> result = new HashMap<>();
            result.put("count", failedTasks != null ? failedTasks.size() : 0);
            result.put("tasks", failedTasks);

            return ApiResponse.success(result);
        } catch (Exception e) {
            log.error("Failed to get failed tasks", e);
            return ApiResponse.fail("获取失败任务列表失败: " + e.getMessage());
        }
    }

}
