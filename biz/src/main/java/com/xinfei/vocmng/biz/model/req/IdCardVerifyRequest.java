/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2025 All Rights Reserved.
 */
package com.xinfei.vocmng.biz.model.req;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 身份证验证请求
 *
 * <AUTHOR>
 * @version $ IdCardVerifyRequest, v 0.1 2025/5/14 16:30 shaohui.chen Exp $
 */
@Data
@ApiModel(description = "身份证验证请求")
public class IdCardVerifyRequest {

    @ApiModelProperty(value = "手机号", required = true)
    private String mobile;

    @ApiModelProperty(value = "身份证后6位", required = true)
    private String idCardLast6;
}
