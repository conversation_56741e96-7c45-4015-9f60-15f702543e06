/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2023 All Rights Reserved.
 */
package com.xinfei.vocmng.biz.log;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 日志Annotated
 *
 * <AUTHOR>
 * @version $ DigestLogAnnotated, v 0.1 2023/8/28 21:29 <PERSON><PERSON>.Huang Exp $
 */
@Retention(RetentionPolicy.RUNTIME)
@Target({ElementType.METHOD})
public @interface DigestLogAnnotated {

    /**
     * 注解值
     */
    String value();
}
