/*
 * Xinfei.com Inc.
 * Copyright (c) 2015-2024. All Rights Reserved.
 */

package com.xinfei.vocmng.biz.model.enums;

import lombok.Getter;

/**
 * <AUTHOR>
 * @version $ AmtTypeEnum, v 0.1 2024/8/13 11:49 you.zhang Exp $
 */
@Getter
public enum AmtTypeEnum {

    ADJUST("ADJUST", "授信额度", "固额调额"),
    TEMPORARY("TEMPORARY", "临时额度", "临额调额");
    private final String code;
    private final String desc;

    private final String msg;

    AmtTypeEnum(String code, String desc, String msg) {
        this.code = code;
        this.desc = desc;
        this.msg = msg;
    }

    public static AmtTypeEnum fromCode(String code) {
        for (AmtTypeEnum type : AmtTypeEnum.values()) {
            if (type.getCode().equalsIgnoreCase(code)) {
                return type;
            }
        }
        return null;
    }
}
