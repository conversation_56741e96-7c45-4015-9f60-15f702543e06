package com.xinfei.vocmng.biz.api;

import com.xinfei.vocmng.biz.model.resp.ApiResponse;
import com.xinfei.vocmng.biz.rr.dto.CallRecordDto;
import com.xinfei.vocmng.biz.rr.dto.MarketingBlackDto;
import com.xinfei.vocmng.biz.rr.dto.SmsRecordsDto;
import com.xinfei.vocmng.biz.rr.dto.StopMarketDto;
import com.xinfei.vocmng.biz.rr.request.*;
import com.xinfei.vocmng.biz.rr.response.Paging;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

import java.util.List;

/**
 * 客户触达相关接口
 *
 * <AUTHOR>
 * @version $ UserReachApi, v 0.1 2023/12/28 13:27 qu.lu Exp $
 */
@Api(tags = "客户触达记录相关接口")
@RequestMapping("/user/reach")
public interface UserReachApi {

    @ApiOperation("查询话务信息列表")
    @PostMapping("/call/list")
    ApiResponse<Paging<CallRecordDto>> queryCallRecordList(@RequestBody QueryCallListRequest request);

    @ApiOperation("查询短信列表")
    @PostMapping("/sms/list")
    ApiResponse<Paging<SmsRecordsDto>> querySmsRecordList(@RequestBody QuerySmsRecordRequest request);

    @ApiOperation("暂停营销列表")
    @PostMapping("/queryStopMarketList")
    ApiResponse<Paging<StopMarketDto>> queryStopMarketList(@RequestBody StopMarketRequest request);

    @ApiOperation("新增暂停营销")
    @PostMapping("/addStopMarket")
    ApiResponse<Boolean> addStopMarket(@RequestBody StopMarketRequest request);

    @ApiOperation("更新暂停营销")
    @PostMapping("/updateStopMarket")
    ApiResponse<Boolean> updateStopMarket(@RequestBody StopMarketRequest request);

    @ApiOperation("营销加黑列表查询")
    @PostMapping("/marketingBlackList")
    ApiResponse<List<MarketingBlackDto>> marketingBlackList(@RequestBody MarketingBlackListRequest request);

    @ApiOperation("营销加黑创建")
    @PostMapping("/marketingBlackCreate")
    ApiResponse<Boolean> marketingBlackCreate(@RequestBody List<MarketingBlackRequest> request);

    @ApiOperation("营销加黑更新")
    @PostMapping("/marketingBlackUpdate")
    ApiResponse<Boolean> marketingBlackUpdate(@RequestBody MarketingBlackRequest request);


}
