package com.xinfei.vocmng.biz.api.controller;

import com.xinfei.vocmng.biz.api.RedisManageApi;
import com.xinfei.vocmng.biz.model.annotation.LoginRequired;
import com.xinfei.vocmng.biz.model.resp.ApiResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.connection.RedisConnection;
import org.springframework.data.redis.connection.stream.MapRecord;
import org.springframework.data.redis.connection.stream.ReadOffset;
import org.springframework.data.redis.connection.stream.StreamInfo;
import org.springframework.data.redis.connection.stream.StreamOffset;
import org.springframework.data.redis.connection.stream.StreamReadOptions;
import org.springframework.data.redis.core.Cursor;
import org.springframework.data.redis.core.ScanOptions;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.web.bind.annotation.RestController;

import java.util.*;
import java.util.stream.Collectors;

/**
 * Redis管理Controller
 *
 * <AUTHOR>
 * @version $ RedisManageController, v 0.1 2025/5/1 $
 */
@Slf4j
@RestController
@RequiredArgsConstructor
public class RedisManageController implements RedisManageApi {

    private final StringRedisTemplate redisTemplate;

    @Override
    public ApiResponse<Set<String>> getSetMembers(String key) {
        try {
            Set<String> members = redisTemplate.opsForSet().members(key);
            if (members == null) {
                members = new HashSet<>();
            }
            log.info("Get {} members from Redis Set: {}", members.size(), key);
            return ApiResponse.success(members);
        } catch (Exception e) {
            log.error("Failed to get members from Redis Set: {}", key, e);
            return ApiResponse.fail("获取Set数据失败: " + e.getMessage());
        }
    }

    @Override
    public ApiResponse<Boolean> deleteSet(String key) {
        try {
            Boolean result = redisTemplate.delete(key);
            log.info("Delete Redis Set: {}, result: {}", key, result);
            return ApiResponse.success(result);
        } catch (Exception e) {
            log.error("Failed to delete Redis Set: {}", key, e);
            return ApiResponse.fail("删除Set失败: " + e.getMessage());
        }
    }

    @Override
    public ApiResponse<Boolean> deleteSetMember(String key, String value) {
        try {
            Long result = redisTemplate.opsForSet().remove(key, value);
            boolean success = result != null && result > 0;
            log.info("Delete member {} from Redis Set: {}, result: {}", value, key, success);
            return ApiResponse.success(success);
        } catch (Exception e) {
            log.error("Failed to delete member {} from Redis Set: {}", value, key, e);
            return ApiResponse.fail("删除Set成员失败: " + e.getMessage());
        }
    }

    @Override
    public ApiResponse<List<Map<String, Object>>> getStreamMessages(String key, int count) {
        try {
            // 检查Stream是否存在
            if (!Boolean.TRUE.equals(redisTemplate.hasKey(key))) {
                return ApiResponse.success(new ArrayList<>());
            }

            // 获取Stream中的消息
            List<MapRecord<String, Object, Object>> messages = redisTemplate.opsForStream()
                    .read(StreamReadOptions.empty().count(count), StreamOffset.fromStart(key));

            // 转换消息格式
            List<Map<String, Object>> result = new ArrayList<>();
            if (messages != null) {
                for (MapRecord<String, Object, Object> message : messages) {
                    Map<String, Object> messageMap = new HashMap<>();
                    messageMap.put("id", message.getId().getValue());
                    messageMap.put("data", message.getValue());
                    result.add(messageMap);
                }
            }

            log.info("Get {} messages from Redis Stream: {}", result.size(), key);
            return ApiResponse.success(result);
        } catch (Exception e) {
            log.error("Failed to get messages from Redis Stream: {}", key, e);
            return ApiResponse.fail("获取Stream数据失败: " + e.getMessage());
        }
    }

    @Override
    public ApiResponse<Boolean> deleteStream(String key) {
        try {
            Boolean result = redisTemplate.delete(key);
            log.info("Delete Redis Stream: {}, result: {}", key, result);
            return ApiResponse.success(result);
        } catch (Exception e) {
            log.error("Failed to delete Redis Stream: {}", key, e);
            return ApiResponse.fail("删除Stream失败: " + e.getMessage());
        }
    }

    @Override
    public ApiResponse<Boolean> deleteStreamMessage(String key, String messageId) {
        try {
            Long result = redisTemplate.opsForStream().delete(key, messageId);
            boolean success = result != null && result > 0;
            log.info("Delete message {} from Redis Stream: {}, result: {}", messageId, key, success);
            return ApiResponse.success(success);
        } catch (Exception e) {
            log.error("Failed to delete message {} from Redis Stream: {}", messageId, key, e);
            return ApiResponse.fail("删除Stream消息失败: " + e.getMessage());
        }
    }

    @Override
    public ApiResponse<Set<String>> getKeys(String pattern) {
        try {
            Set<String> keys = new HashSet<>();

            // 使用scan命令获取匹配的keys，避免使用keys命令对Redis性能的影响
            redisTemplate.execute((RedisConnection connection) -> {
                try (Cursor<byte[]> cursor = connection.scan(ScanOptions.scanOptions().match(pattern).count(1000).build())) {
                    while (cursor.hasNext()) {
                        keys.add(new String(cursor.next()));
                    }
                    return null;
                } catch (Exception e) {
                    log.error("Error scanning Redis keys with pattern: {}", pattern, e);
                    return null;
                }
            });

            log.info("Found {} keys matching pattern: {}", keys.size(), pattern);
            return ApiResponse.success(keys);
        } catch (Exception e) {
            log.error("Failed to get keys with pattern: {}", pattern, e);
            return ApiResponse.fail("获取Keys失败: " + e.getMessage());
        }
    }
}
