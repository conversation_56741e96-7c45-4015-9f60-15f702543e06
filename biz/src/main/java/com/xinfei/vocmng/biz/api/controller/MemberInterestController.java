package com.xinfei.vocmng.biz.api.controller;

import com.xinfei.supervip.interfaces.model.admin.dto.VipOpsResultAdminDTO;
import com.xinfei.supervip.interfaces.model.admin.dto.VipOrderDetailAdminDTO;
import com.xinfei.vipcore.facade.rr.dto.RefundApplyResDto;
import com.xinfei.vipcore.facade.rr.dto.RefundStartDto;
import com.xinfei.vocmng.biz.api.MemberInterestApi;
import com.xinfei.vocmng.biz.config.VocConfig;
import com.xinfei.vocmng.biz.log.DigestLogAnnotated;
import com.xinfei.vocmng.biz.model.annotation.DataPermission;
import com.xinfei.vocmng.biz.model.annotation.LoginRequired;
import com.xinfei.vocmng.biz.model.base.PageResultResponse;
import com.xinfei.vocmng.biz.model.enums.DataPermissionType;
import com.xinfei.vocmng.biz.model.enums.TechplayErrDtlEnum;
import com.xinfei.vocmng.biz.model.exception.IgnoreException;
import com.xinfei.vocmng.biz.model.resp.ApiResponse;
import com.xinfei.vocmng.biz.remote.MemberInterestRemoteService;
import com.xinfei.vocmng.biz.rr.RightCardPackInfoDto;
import com.xinfei.vocmng.biz.rr.dto.*;
import com.xinfei.vocmng.biz.rr.request.*;
import com.xinfei.vocmng.biz.rr.response.CardRefund;
import com.xinfei.vocmng.itl.client.feign.impl.VipFacadeClientImpl;
import com.xinfei.vocmng.itl.model.enums.VipCardStatusEnum;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @version $ MemberInterestController, v 0.1 2023/12/22 16:54 qu.lu Exp $
 */
@RestController
@LoginRequired
public class MemberInterestController implements MemberInterestApi {
    @Autowired
    private MemberInterestRemoteService memberInterestRemoteService;

    @Resource
    private VocConfig vocConfig;

    @Autowired
    private VipFacadeClientImpl vipFacadeClient;

    @Override
    @DigestLogAnnotated("loadRightCardDetail")
    public ApiResponse<RightCardPackInfoDto> loadRightCardDetail(LoadRightCardRequest request) {
        return memberInterestRemoteService.loadRightCardDetail(request);
    }

    @Override
    public ApiResponse<Boolean> renewStopAdmin(RenewStopRequest request) {
        if (request.getType() != 3 && request.getType() != 4) {
            throw new IgnoreException(TechplayErrDtlEnum.REQUEST_PARAM_ERROR, "只有飞享会员/飞跃会员能停止续费");
        }

        if (!request.getVipStatus().equals(VipCardStatusEnum.PAYMENT_SUCCESSFUL.getName())) {
            throw new IgnoreException(TechplayErrDtlEnum.REQUEST_PARAM_ERROR, "只有" + VipCardStatusEnum.PAYMENT_SUCCESSFUL.getName() + "状态能停止续费");
        }

        Boolean result;
        if (request.getType() == 4 && vocConfig.getIsSuperVipCard()) {
            result = memberInterestRemoteService.disableVipRenew(request.getUserNo());
        } else {
            result = memberInterestRemoteService.renewStopAdmin(request.getUserNo());
        }
        if (result != null && result) {
            return ApiResponse.success(result);
        } else {
            return ApiResponse.fail(result == null ? "result == null" : "取消失败");
        }
    }

    @Override
    @DataPermission(type = DataPermissionType.NONE)
    @DigestLogAnnotated("queryMemberCardList")
    public ApiResponse<List<MemberCardDto>> queryMemberCardList(QueryMemberCardListRequest request) {
        return memberInterestRemoteService.queryMemberCardList(request);
    }

    @Override
    public ApiResponse<Boolean> stopWithhold(StopWithholdRequest stopWithholdRequest) {
        if (stopWithholdRequest.getType() != 3 && stopWithholdRequest.getType() != 4) {
            throw new IgnoreException(TechplayErrDtlEnum.REQUEST_PARAM_ERROR, "只有飞享会员/飞跃会员能取消扣款");
        }
        if (!stopWithholdRequest.getVipStatus().equals(VipCardStatusEnum.PROCESSING_AWAITING_PAYMENT.getName()) &&
                !stopWithholdRequest.getVipStatus().equals(VipCardStatusEnum.PAYMENT_PROCESSING.getName())) {
            throw new IgnoreException(TechplayErrDtlEnum.REQUEST_PARAM_ERROR, "只有处理中/支付发起状态能取消扣款");
        }
        if (stopWithholdRequest.getType() != 4 &&
                stopWithholdRequest.getVipStatus().equals(VipCardStatusEnum.PAYMENT_PROCESSING.getName())) {
            throw new IgnoreException(TechplayErrDtlEnum.REQUEST_PARAM_ERROR, "支付发起状态只有飞跃会员能取消扣款");
        }

        Boolean result;
        if (stopWithholdRequest.getType() == 4 && vocConfig.getIsSuperVipCard()) {
            result = memberInterestRemoteService.cancelVipDeduct(stopWithholdRequest.getCardId());
        } else {
            result = memberInterestRemoteService.stopWithhold(stopWithholdRequest.getCardId());
        }
        if (result != null && result) {
            return ApiResponse.success(result);
        } else {
            return ApiResponse.fail(result == null ? "result == null" : "取消扣款失败");
        }
    }

    @Override
    @DigestLogAnnotated("queryMemberCardUsedInfo")
    public ApiResponse<List<MemberCardUseInfoDto>> queryMemberCardUsedInfo(QueryMemberCardUsedListRequest request) {
        return memberInterestRemoteService.queryMemberCardUsedInfo(request);
    }


    @Override
    @DigestLogAnnotated("queryRightCardRefund")
    public ApiResponse<List<RightCardRefundDto>> queryRightCardRefund(List<VipCardRefundLog> request) {
        return memberInterestRemoteService.queryRightCardRefund(request);
    }

    @Override
    public ApiResponse<RightCardDiscountDto> queryRightCardDiscount(VipCardRefundLog request) {
        if (request.getCardType() != 4) {
            throw new IgnoreException(TechplayErrDtlEnum.REQUEST_PARAM_ERROR, "只有飞跃会员可以减免");
        }

        List<VipOrderDetailAdminDTO> vipOrderDetailDTOS = vipFacadeClient.queryVipOrderList(null, null, null, request.getVipCardId());
        if (CollectionUtils.isEmpty(vipOrderDetailDTOS)) {
            throw new IgnoreException(TechplayErrDtlEnum.REQUEST_PARAM_ERROR, "该cardId无法查询到飞跃会员订单");
        }
        VipOrderDetailAdminDTO vipOrderDetailDTO = vipOrderDetailDTOS.get(0);

        return ApiResponse.success(memberInterestRemoteService.queryRightCardDiscount(request, vipOrderDetailDTO));
    }

    @Override
    public ApiResponse<VipOpsResultAdminDTO> reduceApply(ReduceApplyRequest request) {
        return ApiResponse.success(memberInterestRemoteService.reduceApply(request));
    }

    @Override
    public ApiResponse<VipOpsResultAdminDTO> cancelVipReduceApply(CancelVipReduceApplyRequest request) {
        return ApiResponse.success(memberInterestRemoteService.cancelVipReduceApply(request));
    }

    @Override
    public ApiResponse<VipOrderReduceDetailAdmin> reduceDetail(CancelVipReduceApplyRequest request) {
        return ApiResponse.success(memberInterestRemoteService.reduceDetail(request.getVipReduceId()));
    }

    @Override
    public ApiResponse<PageResultResponse<VipOrderReducePriceApplyAdmin>> queryVipReduceList(CardDiscountListRequest request) {
        return ApiResponse.success(memberInterestRemoteService.queryVipReduceList(request));
    }

    @Override
    public ApiResponse<RefundApplyResDto> vipCardRefundApply(List<VipCardRefundApply> request) {
        return memberInterestRemoteService.vipCardRefundApply(request);
    }

    @Override
    public ApiResponse<RefundStartDto> vipCardRefundStart(VipCardRefundApplyStart request) {
        RefundStartDto result = memberInterestRemoteService.vipCardRefundStart(request);
        if (result != null && result.getSuccess()) {
            return ApiResponse.success(result);
        } else {
            return ApiResponse.fail(result == null ? "result == null" : result.getMessage());
        }
    }

    @Override
    public ApiResponse<RefundStartDto> vipCardRefundCancel(VipCardRefundApplyStart request) {
        RefundStartDto result = memberInterestRemoteService.vipCardRefundCancel(request);
        if (result != null && result.getSuccess()) {
            return ApiResponse.success(result);
        } else {
            return ApiResponse.fail(result == null ? "result == null" : result.getMessage());
        }
    }

    @Override
    @DigestLogAnnotated("queryRightCardRefundList")
    public ApiResponse<Map<String, List<CardRefund>>> queryRightCardRefundList(VipCardRefundLogReq request) {
        return ApiResponse.success(memberInterestRemoteService.queryRightCardRefundList(request));
    }

    @Override
    @DigestLogAnnotated("queryRightCardRefundList")
    public ApiResponse<Boolean> queryVipBlack(VipCardBlackReq request) {
        return ApiResponse.success(memberInterestRemoteService.queryVipBlack(request));
    }

    @Override
    @DigestLogAnnotated("queryRightCardRefundList")
    public ApiResponse<Boolean> vipBlack(VipCardBlackReq request) {
        return ApiResponse.success(memberInterestRemoteService.vipBlack(request));
    }

    @Override
    @DigestLogAnnotated("vipRefundDetail")
    public ApiResponse<List<VipRefundDetailDto>> vipRefundDetail(List<VipRefundDetailReq> request) {
        return ApiResponse.success(memberInterestRemoteService.vipRefundDetail(request));
    }

    @Override
    @DigestLogAnnotated("orderPayLog")
    public ApiResponse<List<VipOrderPayLogDetailDTO>> orderPayLog(VipOrderPayLogRequest request) {
        return ApiResponse.success(memberInterestRemoteService.queryOrderPayLog(request.getCardId()));
    }

    @Override
    public ApiResponse<CardDeductionCalculationDto> cardDeductionCalculation(CardDeductionCalculationRequest request) {
        return ApiResponse.success(memberInterestRemoteService.cardDeductionCalculation(request));
    }
}
