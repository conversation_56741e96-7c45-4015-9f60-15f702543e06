/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xinfei.vocmng.biz.model.event;

import lombok.Getter;
import org.springframework.context.ApplicationEvent;

/**
 * <AUTHOR>
 * @version $ SseEvent, v 0.1 2024/2/7 15:28 wancheng.qu Exp $
 */

public class SseEvent extends ApplicationEvent {

    @Getter
    private String userIdentify;
    @Getter
    private String customerPhone;
    @Getter
    private String displayNumber;
    @Getter
    private Long summaryId;

    public SseEvent(Object source, String userIdentify, String customerPhone, String displayNumber, Long summaryId) {
        super(source);
        this.userIdentify = userIdentify;
        this.customerPhone = customerPhone;
        this.displayNumber = displayNumber;
        this.summaryId = summaryId;
        // 初始化其他信息
    }

    public SseEvent(Object source) {
        super(source);
    }

    public void setUserIdentify(String userIdentify) {
        this.userIdentify = userIdentify;
    }

    public void setCustomerPhone(String customerPhone) {
        this.customerPhone = customerPhone;
    }

    public void setDisplayNumber(String displayNumber) {
        this.displayNumber = displayNumber;
    }

    public void setSummaryId(Long summaryId) {
        this.summaryId = summaryId;
    }
}