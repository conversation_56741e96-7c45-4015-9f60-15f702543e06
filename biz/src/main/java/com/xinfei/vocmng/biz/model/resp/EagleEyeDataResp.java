/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2025 All Rights Reserved.
 */
package com.xinfei.vocmng.biz.model.resp;

import com.xinfei.vocmng.biz.rr.CategoryNode;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @version $ EagleEyeDataResp, v 0.1 2025-04-09 14:10 junjie.yan Exp $
 */
@Data
public class EagleEyeDataResp {

    @ApiModelProperty(value = "统计两个数据集的小时数据量")
    private Map<String, Map<String, Integer>> hourlyData;

    @ApiModelProperty(value = "统计当天小结的 source 和 status 数据量")
    private Map<String, Map<String, Integer>> sourceAndStatus;

    @ApiModelProperty(value = "统计 当天小结 中 createUserIdentify 的每小时数据量")
    private Map<String, Map<String, Object>> userPerHour;

    @ApiModelProperty(value = "统计带层级关系的分类及 dataList2 对应数据量")
    private  List<Map<String, Object>> categoryNodes;

}