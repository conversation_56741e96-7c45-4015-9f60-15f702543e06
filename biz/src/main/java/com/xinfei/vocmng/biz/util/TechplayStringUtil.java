/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2023 All Rights Reserved.
 */
package com.xinfei.vocmng.biz.util;

import org.apache.commons.beanutils.BeanUtils;
import org.springframework.util.StringUtils;

import java.lang.reflect.InvocationTargetException;
import java.util.Set;

/**
 * <AUTHOR>
 * @version $ TechplayStringUtil, v 0.1 2023/8/28 21:01 <PERSON>yan.Huang Exp $
 */
public final class TechplayStringUtil {

    /**
     * 用默认字符串替换空字符串
     *
     * @param str        被检查的字符串
     * @param defaultStr 默认字符串
     * @return 用默认值处理后的字符串
     */
    public static String defaultIfBlank(String str, String defaultStr) {

        if (StringUtils.hasText(str)) {
            return str;
        }

        return defaultStr;
    }

    /**
     * @param bean:
     * @param propertyName:
     * @return Object  根据对象字段名获取对象的属性值
     * <AUTHOR>
     * @description TODO
     * @date 2024/8/27 18:31
     */
    public static Object getProperty(Object bean, String propertyName) {
        try {
            return BeanUtils.getProperty(bean, propertyName);
        } catch (IllegalAccessException | InvocationTargetException | NoSuchMethodException e) {
            return null;
        }
    }

    /**
     * @param assemble:
     * @param obj:
     * @return boolean
     * <AUTHOR>
     * @description isContain
     * @date 2024/12/20 14:14
     */
    public static boolean isContain(String assemble,String obj) {
        Set<String> specialCapitals = StringUtils.commaDelimitedListToSet(assemble);
        return specialCapitals.contains(obj);
    }

}
