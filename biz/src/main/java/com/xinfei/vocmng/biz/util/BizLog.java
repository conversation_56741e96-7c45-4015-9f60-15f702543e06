/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2023 All Rights Reserved.
 */
package com.xinfei.vocmng.biz.util;

import cn.hutool.core.lang.Dict;
import com.alibaba.fastjson.JSON;
import com.xinfei.vocmng.biz.constants.LoginUserConstants;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @version $ Bizlog, v 0.1 2023/10/20 23:24 longjie.yuan Exp $
 */
@Slf4j
public class BizLog {
    public static void addLog(String messageTag, Dict dict) {
        dict.set(LoginUserConstants.MESSAGE_TAG, messageTag);
        dict.set("category", "biz");
        log.info(JSON.toJSONString(dict));
    }

    public static void addLog(String messageTag, String message) {
        Dict dict = Dict.create()
                .set(LoginUserConstants.MESSAGE_TAG, messageTag)
                .set("category", "biz")
                .set("message", message);
        log.info(JSON.toJSONString(dict));
    }
}