package com.xinfei.vocmng.biz.constants;

import java.time.LocalDateTime;
import java.time.ZoneOffset;

/**
 * CIS服务调用相关的常量配置信息
 *
 * <AUTHOR>
 * @since 2023/12/14
 */
public class ReqNoConstants {

    /** 以下为请求流水前缀 */
    /**
     * 订单退款
     */
    public static final String VOCRA = "VOCRA";
    /**
     * 溢缴款退款
     */
    public static final String VOCORA = "VOCORA";

    /**
     * 创建还款减免方案
     */
    public static final String VOCCRP = "VOCCRP";

    /**
     * 线下销账
     */
    public static final String VOCLRA = "VOCLRA";

    /**
     * 系统代扣
     */
    public static final String VOCSRP = "VOCSRP";

    /**
     * 聚合支付-收银台短链创建
     */
    public static final String VOCSLC = "VOCSLC";

    /**
     * 聚合支付-收银台短链关闭
     */
    public static final String VOCSLCLOSE = "VOCSLCLOSE";

    public static String getTime() {
        long now = LocalDateTime.now().toInstant(ZoneOffset.of("+8")).toEpochMilli();
        return Long.toString(now);
    }

    public static String getRequestNo(String title, Object id) {
        return title + id + "_" + getTime();
    }
}
