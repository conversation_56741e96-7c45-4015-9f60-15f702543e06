/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xinfei.vocmng.biz.model.enums;

import lombok.Getter;

/**
 * <AUTHOR>
 * @version $ OfflineRefundMethodEnum, v 0.1 2024-05-21 14:38 junjie.yan Exp $
 */

@Getter
public enum RefundTypeEnum {
    ONLINE_REFUND(1, "ONLINE_REFUND", "线上原路原退"),
    OFFLINE_REFUND(2, "OFFLINE_REFUND", "线下退款");

    private final Integer code;
    private final String codeStr;
    private final String description;

    RefundTypeEnum(Integer code, String codeStr, String description) {
        this.code = code;
        this.codeStr = codeStr;
        this.description = description;
    }

    public static Integer getCodeByCodeStr(String codeStr) {
        if (codeStr == null) {
            return null;
        }

        for (RefundTypeEnum method : RefundTypeEnum.values()) {
            if (method.getCodeStr().equals(codeStr)) {
                return method.getCode();
            }
        }

        return null;
    }

    public static String getCodeStrByCode(Integer code) {
        if (code == null) {
            return null;
        }

        for (RefundTypeEnum method : RefundTypeEnum.values()) {
            if (method.getCode().equals(code)) {
                return method.getCodeStr();
            }
        }
        return null;
    }

}