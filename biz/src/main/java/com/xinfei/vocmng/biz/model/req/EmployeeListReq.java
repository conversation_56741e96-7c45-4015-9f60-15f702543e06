/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2023 All Rights Reserved.
 */
package com.xinfei.vocmng.biz.model.req;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 *
 * <AUTHOR>
 * @version $ EmployeeListReq, v 0.1 2023/12/20 19:56 wancheng.qu Exp $
 */

@Data
public class EmployeeListReq implements Serializable {

    @ApiModelProperty(value = "员工身份集合")
    private List<String> userIdentify;

    @ApiModelProperty(value = "组织id")
    private Long departmentId;

    @ApiModelProperty(value = "是否删除：1已删除 0未删除")
    private Integer isDeleted;


}