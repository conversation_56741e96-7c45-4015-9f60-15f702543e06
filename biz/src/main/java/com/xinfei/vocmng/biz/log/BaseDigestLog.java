/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2023 All Rights Reserved.
 */
package com.xinfei.vocmng.biz.log;

import cn.hutool.json.JSONObject;
import com.xinfei.vocmng.biz.util.TechplayStringUtil;
import com.xinfei.xfframework.common.ErrorContext;
import com.xinfei.xfframework.context.XFRpcUtil;
import lombok.Setter;

/**
 * 摘要日志基类
 *
 * <AUTHOR>
 * @version $ BaseDigestLog, v 0.1 2023/8/28 16:33 Jinyan.Huang Exp $
 */
@Setter
public abstract class BaseDigestLog {

    /**
     * 调用成功的字符串值
     */
    protected static final String TRUE_VALUE = "Y";

    /**
     * 调用失败的字符串值
     */
    protected static final String FALSE_VALUE = "N";

    /**
     * 调用成功的字符串描述
     */
    protected static final String TRUE_DESC = "SUCCESS";

    /**
     * 分割符
     */
    protected static final String SPLIT_TAG = ",";

    /**
     * 默认值
     */
    protected static final String DEFAULT_VALUE = "-";

    /**
     * 被调用方法
     */
    protected String invocationMethod;

    /**
     * 操作是否成功
     */
    protected boolean success;

    /**
     * 错误上下文
     */
    protected ErrorContext errorContext;

    /**
     * 操作耗时
     */
    protected long elapse;

    /**
     * 转换为摘要日志字符串。
     *
     * @return 摘要日志字符串
     */
    public String toDigestLog() {

        StringBuilder buffer = new StringBuilder();

        buffer.append("[");
        {
            // 构造调用信息
            composeInvokeInfo(buffer);

            // 构造事务信息
            composeTransInfo(buffer);

            // 构造交易信息
            composeBizInfo(buffer);

            // 构造调用方信息
            composeCallerInfo(buffer);
        }
        buffer.append("]");

        return buffer.toString();
    }

    /**
     * 组装事务信息
     *
     * @param buffer 日志信息
     */
    protected void composeTransInfo(StringBuilder buffer) {
        buffer.append("(");
        JSONObject json = new JSONObject();
        {
        }
        buffer.append(json.toJSONString(0));
        buffer.append(")");
    }


    /**
     * 组装交易信息
     *
     * @param buffer 日志信息
     */
    protected void composeBizInfo(StringBuilder buffer) {
        buffer.append("(");
        JSONObject json = new JSONObject();
        {
        }
        buffer.append(json.toJSONString(0));
        buffer.append(")");
    }

    /**
     * 构造调用者信息。
     *
     * @param buffer 日志字符串
     */
    final protected void composeCallerInfo(StringBuilder buffer) {

        String ip = TechplayStringUtil.defaultIfBlank(XFRpcUtil.getClientIp(), DEFAULT_VALUE);
        String appName = DEFAULT_VALUE;
        String invokeId = DEFAULT_VALUE;
        String traceId = TechplayStringUtil.defaultIfBlank(XFRpcUtil.getTraceId(), DEFAULT_VALUE);
        buffer.append("(");
        {
            buffer.append(ip).append(SPLIT_TAG);
            buffer.append(appName).append(SPLIT_TAG);
            buffer.append(traceId).append(SPLIT_TAG);
            buffer.append(invokeId);
        }
        buffer.append(")");

    }

    /**
     * 构造调用信息。
     *
     * @param buffer 日志字符串
     */
    final protected void composeInvokeInfo(StringBuilder buffer) {

        buffer.append("(");
        {

            String logSegment = TechplayStringUtil.defaultIfBlank(invocationMethod, DEFAULT_VALUE);
            buffer.append(logSegment).append(SPLIT_TAG);

            logSegment = TechplayStringUtil.defaultIfBlank(success ? TRUE_VALUE : FALSE_VALUE, DEFAULT_VALUE);
            buffer.append(logSegment).append(SPLIT_TAG);

            logSegment = TechplayStringUtil.defaultIfBlank(success ? TRUE_DESC : fetchErrorDetailCode(errorContext), DEFAULT_VALUE);
            buffer.append(logSegment).append(SPLIT_TAG);

            buffer.append(TechplayStringUtil.defaultIfBlank(String.valueOf(elapse), DEFAULT_VALUE));
        }
        buffer.append("ms)");
    }

    //~~内部方法~~

    /**
     * 从标准错误上下文中提取错误码
     *
     * @param errorContext 标准错误上下文
     * @return 错误明细英文描述
     */
    private String fetchErrorDetailCode(ErrorContext errorContext) {

        if (errorContext == null) {
            return DEFAULT_VALUE;
        }
        return TechplayStringUtil.defaultIfBlank(errorContext.getErrCode(), DEFAULT_VALUE);
    }

    /**
     * 从标准错误上下文中提取错误明细英文描述
     *
     * @param errorContext 标准错误上下文
     * @return 错误明细英文描述
     */
    private String fetchErrorDetailDesc(ErrorContext errorContext) {

        if (errorContext == null) {
            return DEFAULT_VALUE;
        }
        return TechplayStringUtil.defaultIfBlank(errorContext.getErrDesc(), DEFAULT_VALUE);
    }

}
