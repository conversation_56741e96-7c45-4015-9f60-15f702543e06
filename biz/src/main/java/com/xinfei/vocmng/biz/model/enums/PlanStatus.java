/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2025 All Rights Reserved.
 */
package com.xinfei.vocmng.biz.model.enums;

import lombok.Getter;

/**
 *
 * <AUTHOR>
 * @version $ PlanStatus, v 0.1 2025-04-07 15:52 junjie.yan Exp $
 */

@Getter
public enum PlanStatus {
    /**
     * 待生效
     */
    PENDING(0, "待生效"),
    /**
     * 生效中
     */
    ACTIVE(1, "生效中"),
    /**
     * 失效
     */
    INVALID(2, "失效"),
    /**
     * 成功
     */
    SUCCESS(3, "成功");

    /**
     * 状态码
     */
    private final int code;

    /**
     * 状态描述
     */
    private final String description;

    PlanStatus(int code, String description) {
        this.code = code;
        this.description = description;
    }

    /**
     * 根据状态码获取枚举实例
     *
     * @param code 状态码
     * @return 枚举实例
     * @throws IllegalArgumentException 若状态码不存在
     */
    public static PlanStatus fromCode(int code) {
        for (PlanStatus status : values()) {
            if (status.code == code) {
                return status;
            }
        }
        throw new IllegalArgumentException("PlanStatus无效的状态码: " + code);
    }


}