package com.xinfei.vocmng.biz.api.controller;

import com.xinfei.vocmng.biz.api.UserReachApi;
import com.xinfei.vocmng.biz.log.DigestLogAnnotated;
import com.xinfei.vocmng.biz.model.annotation.DataPermission;
import com.xinfei.vocmng.biz.model.annotation.LoginRequired;
import com.xinfei.vocmng.biz.model.enums.DataPermissionType;
import com.xinfei.vocmng.biz.model.resp.ApiResponse;
import com.xinfei.vocmng.biz.remote.UserReachRemoteService;
import com.xinfei.vocmng.biz.rr.dto.CallRecordDto;
import com.xinfei.vocmng.biz.rr.dto.MarketingBlackDto;
import com.xinfei.vocmng.biz.rr.dto.SmsRecordsDto;
import com.xinfei.vocmng.biz.rr.dto.StopMarketDto;
import com.xinfei.vocmng.biz.rr.request.*;
import com.xinfei.vocmng.biz.rr.response.Paging;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR>
 * @version $ CallCenterController, v 0.1 2023/12/28 13:25 qu.lu Exp $
 */
@Slf4j
@RestController
@LoginRequired
public class UserReachController implements UserReachApi {

    @Autowired
    private UserReachRemoteService userReachRemoteService;

    @Override
    @DigestLogAnnotated("queryCallRecordList")
    public ApiResponse<Paging<CallRecordDto>> queryCallRecordList(QueryCallListRequest request) {
        return userReachRemoteService.queryCallRecordList(request);
    }

    @Override
    @DigestLogAnnotated("querySmsRecordList")
    @DataPermission(type = DataPermissionType.NONE)
    public ApiResponse<Paging<SmsRecordsDto>> querySmsRecordList(QuerySmsRecordRequest request) {
        return userReachRemoteService.querySmsRecordList(request);
    }

    @Override
    @DigestLogAnnotated("queryStopMarketList")
    public ApiResponse<Paging<StopMarketDto>> queryStopMarketList(StopMarketRequest request) {
        return userReachRemoteService.queryStopMarketList(request);
    }

    @Override
    @DigestLogAnnotated("addStopMarket")
    public ApiResponse<Boolean> addStopMarket(StopMarketRequest request) {
        return userReachRemoteService.addStopMarket(request);
    }

    @Override
    @DigestLogAnnotated("updateStopMarket")
    public ApiResponse<Boolean> updateStopMarket(StopMarketRequest request) {
        return userReachRemoteService.updateStopMarket(request);
    }

    @Override
    @DigestLogAnnotated("marketingBlackList")
    @DataPermission(type = DataPermissionType.NONE)
    public ApiResponse<List<MarketingBlackDto>> marketingBlackList(MarketingBlackListRequest request) {
        return userReachRemoteService.marketingBlackList(request);

    }

    @Override
    @DigestLogAnnotated("marketingBlackCreate")
    public ApiResponse<Boolean> marketingBlackCreate(List<MarketingBlackRequest> request) {
        return userReachRemoteService.marketingBlackCreate(request);

    }

    @Override
    @DigestLogAnnotated("marketingBlackUpdate")
    public ApiResponse<Boolean> marketingBlackUpdate(MarketingBlackRequest request) {
        return userReachRemoteService.marketingBlackUpdate(request);
    }
}
