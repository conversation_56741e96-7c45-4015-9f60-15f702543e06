/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xinfei.vocmng.biz.model.enums;

import lombok.Getter;

/**
 * <AUTHOR>
 * @version $ OfflineRefundMethodEnum, v 0.1 2024-05-21 14:38 junjie.yan Exp $
 */

@Getter
public enum OrderRefundStatusEnum {

    NO_REVIEW(0, "无需审核"),
    AWAITING(1, "待审核"),
    APPROVED(2, "审核通过"),
    REJECTED(3, "审核拒绝");

    private final Integer code;
    private final String description;

    OrderRefundStatusEnum(Integer code, String description) {
        this.code = code;
        this.description = description;
    }

    public static Integer getCode(String description) {
        if (description == null) {
            return null;
        }

        for (OrderRefundStatusEnum method : OrderRefundStatusEnum.values()) {
            if (method.getDescription().equals(description)) {
                return method.getCode();
            }
        }

        return null;
    }

    public static String getDescription(Integer code) {
        if (code == null) {
            return null;
        }

        for (OrderRefundStatusEnum method : OrderRefundStatusEnum.values()) {
            if (method.getCode().equals(code)) {
                return method.getDescription();
            }
        }

        return null;
    }

}