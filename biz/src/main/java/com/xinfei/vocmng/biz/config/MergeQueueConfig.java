package com.xinfei.vocmng.biz.config;

import com.xinfei.vocmng.biz.constants.RedisKeyConstants;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.scheduling.annotation.EnableScheduling;

import javax.annotation.Resource;

/**
 * 合并队列配置
 *
 * <AUTHOR>
 * @version $ MergeQueueConfig, v 0.1 2025/5/1 $
 */
@Slf4j
@Configuration
@EnableScheduling
public class MergeQueueConfig {

    @Resource
    private StringRedisTemplate stringRedisTemplate;

    /**
     * 初始化Redis队列
     * 确保队列相关的Redis键存在
     */
    @Bean
    public void initializeRedisQueues() {
        try {
            // 检查主队列是否存在，如果不存在则创建
            if (Boolean.FALSE.equals(stringRedisTemplate.hasKey(RedisKeyConstants.MERGE_QUEUE))) {
                log.info("Creating main queue: {}", RedisKeyConstants.MERGE_QUEUE);
                // 初始化时不需要添加任何元素，只需确保键存在
                stringRedisTemplate.opsForList().rightPush(RedisKeyConstants.MERGE_QUEUE, "INIT");
                stringRedisTemplate.opsForList().leftPop(RedisKeyConstants.MERGE_QUEUE);
            }

            // 检查重试队列是否存在，如果不存在则创建
            if (Boolean.FALSE.equals(stringRedisTemplate.hasKey(RedisKeyConstants.MERGE_RETRY_QUEUE))) {
                log.info("Creating retry queue: {}", RedisKeyConstants.MERGE_RETRY_QUEUE);
                // 初始化时不需要添加任何元素，只需确保键存在
                stringRedisTemplate.opsForList().rightPush(RedisKeyConstants.MERGE_RETRY_QUEUE, "INIT");
                stringRedisTemplate.opsForList().leftPop(RedisKeyConstants.MERGE_RETRY_QUEUE);
            }

            // 检查处理中集合是否存在，如果不存在则创建
            if (Boolean.FALSE.equals(stringRedisTemplate.hasKey(RedisKeyConstants.MERGE_PROCESSING_SET))) {
                log.info("Creating processing set: {}", RedisKeyConstants.MERGE_PROCESSING_SET);
                // 初始化时不需要添加任何元素，只需确保键存在
                stringRedisTemplate.opsForSet().add(RedisKeyConstants.MERGE_PROCESSING_SET, "INIT");
                stringRedisTemplate.opsForSet().remove(RedisKeyConstants.MERGE_PROCESSING_SET, "INIT");
            }

            log.info("Redis queues initialized successfully");
        } catch (Exception e) {
            log.error("Failed to initialize Redis queues", e);
        }
    }
}
