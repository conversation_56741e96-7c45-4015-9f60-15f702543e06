/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xinfei.vocmng.biz.model.req;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * <AUTHOR>
 * @version $ OrderRefundApplyRecordsReq, v 0.1 2024-05-21 21:00 junjie.yan Exp $
 */
@Data
public class OrderRefundRecordsReq {

    @ApiModelProperty(value = "借据号")
    @NotEmpty
    private List<String> loanNos;

    @ApiModelProperty(value = "明细退款状态 PENDING:待发起,PROCESSING:退款中,SUCCESS:成功,FAILURE:失败,CANCEL:取消,REJECT:拒绝")
    private String refundStatus;

}