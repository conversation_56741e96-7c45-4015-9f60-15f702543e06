/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xinfei.vocmng.biz.api.controller;

import com.xinfei.vocmng.biz.api.LabelSolutionsApi;
import com.xinfei.vocmng.biz.log.DigestLogAnnotated;
import com.xinfei.vocmng.biz.model.enums.TechplayErrDtlEnum;
import com.xinfei.vocmng.biz.model.exception.IgnoreException;
import com.xinfei.vocmng.biz.model.resp.ApiResponse;
import com.xinfei.vocmng.biz.rr.dto.bill.RiskUserDto;
import com.xinfei.vocmng.biz.service.RiskUserService;
import com.xinfei.vocmng.biz.service.impl.LabelSolutionsService;
import com.xinfei.vocmng.itl.rr.GetRiskUserRequest;
import com.xinfei.vocmng.itl.rr.GetSolutionsRequest;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @version $ LabelSolutionsFacadeImpl, v 0.1 2024-01-09 11:47 junjie.yan Exp $
 */
@RestController
public class LabelSolutionsFacadeImpl implements LabelSolutionsApi {

    @Resource
    private LabelSolutionsService labelSolutionsService;

    @Resource
    private RiskUserService riskUserService;

    @Override
    @DigestLogAnnotated("getSolutionsByScore")
    public ApiResponse<String> getSolutionsByScore(GetSolutionsRequest request) {
        if (StringUtils.isEmpty(request.getLabelType()) || StringUtils.isEmpty(request.getLabelName()) || request.getScore() == null || request.getScore() <= 0) {
            return ApiResponse.fail("入参非法");
        }
        return ApiResponse.success(labelSolutionsService.getSolution(request.getLabelType(), request.getLabelName(), request.getScore()));
    }

    @Override
    public ApiResponse<RiskUserDto> getRiskUser(GetRiskUserRequest getRiskUserRequest) {
        if (StringUtils.isEmpty(getRiskUserRequest.getMobile())) {
            throw new IgnoreException(TechplayErrDtlEnum.REQ_PARAM_NOT_VALID, "手机号必填");
        }
        return ApiResponse.success(riskUserService.getRiskUser(getRiskUserRequest.getMobile()));
    }

    @Override
    public ApiResponse<Boolean> getComplainUser(GetRiskUserRequest getRiskUserRequest) {
        return ApiResponse.success(riskUserService.getComplainUser(getRiskUserRequest.getMobile()));
    }
}