package com.xinfei.vocmng.biz.remote;

import com.xinfei.supervip.interfaces.model.admin.dto.VipOpsResultAdminDTO;
import com.xinfei.supervip.interfaces.model.admin.dto.VipOrderDetailAdminDTO;
import com.xinfei.vipcore.facade.rr.dto.RefundApplyResDto;
import com.xinfei.vipcore.facade.rr.dto.RefundStartDto;
import com.xinfei.vocmng.biz.model.base.PageResultResponse;
import com.xinfei.vocmng.biz.model.resp.ApiResponse;
import com.xinfei.vocmng.biz.rr.RightCardPackInfoDto;
import com.xinfei.vocmng.biz.rr.dto.*;
import com.xinfei.vocmng.biz.rr.request.*;
import com.xinfei.vocmng.biz.rr.response.CardRefund;

import java.util.List;
import java.util.Map;

/**
 * 会员权益相关服务
 *
 * <AUTHOR>
 * @version $ MemberInterestRemoteService, v 0.1 2023/12/25 11:02 qu.lu Exp $
 */
public interface MemberInterestRemoteService {
    /**
     * 查询会员卡列表新，聚合新老接口
     * @param request
     * @return
     */
    ApiResponse<List<MemberCardDto>> queryMemberCardList(QueryMemberCardListRequest request);

    /**
     * 飞享会员停止续费
     * @param userNo
     * @return
     */
    Boolean renewStopAdmin(String userNo);

    /**
     * 飞跃会员停止续费
     * @param userNo
     * @return
     */
    Boolean disableVipRenew(String userNo);

    /**
     * 飞享会员取消扣款
     * @return
     */
    Boolean stopWithhold(Long vipOrderId);

    /**
     * 飞跃会员取消扣款
     * @return
     */
    Boolean cancelVipDeduct(Long vipOrderId);

    /**
     * 根据会员卡ID查询会员卡权益使用明细信息
     *
     * @param request
     * @return
     */
    ApiResponse<List<MemberCardUseInfoDto>> queryMemberCardUsedInfo(QueryMemberCardUsedListRequest request);

    /**
     * 根据营收订单号查询权益卡信息
     * @param request
     * @return
     */
    ApiResponse<RightCardPackInfoDto> loadRightCardDetail(LoadRightCardRequest request);

    /**
     * 根据会员卡ID查询会员卡退款相关信息
     *
     * @param request
     * @return
     */
    ApiResponse<List<RightCardRefundDto>> queryRightCardRefund(List<VipCardRefundLog> request);

    RightCardDiscountDto queryRightCardDiscount(VipCardRefundLog request, VipOrderDetailAdminDTO vipOrderDetailDTO);

    /**
     * 会员卡退费申请
     * @param request
     * @return
     */
    ApiResponse<RefundApplyResDto> vipCardRefundApply(List<VipCardRefundApply> request);

    VipOpsResultAdminDTO reduceApply(ReduceApplyRequest request);

    VipOpsResultAdminDTO cancelVipReduceApply(CancelVipReduceApplyRequest request);

    /**
     * 会员卡立即退费
     * @param request
     * @return
     */
    RefundStartDto vipCardRefundStart(VipCardRefundApplyStart request);


    /**
     * 会员卡退费撤销
     * @param request
     * @return
     */
    RefundStartDto vipCardRefundCancel(VipCardRefundApplyStart request);


    /**
     * 根据会员卡ID查询会员卡退款记录相关信息
     * @param request
     * @return
     */
    Map<String, List<CardRefund>> queryRightCardRefundList(VipCardRefundLogReq request);


    /**
     * 根据会员卡手机号加黑
     * @param request
     * @return
     */
    Boolean queryVipBlack(VipCardBlackReq request);


    /**
     * 根据会员卡手机号查询会员卡加黑
     * @param request
     * @return
     */
    Boolean vipBlack(VipCardBlackReq request);


    /**
     * 会员卡原路原退明细
     * @param request
     * @return
     */
    List<VipRefundDetailDto> vipRefundDetail(List<VipRefundDetailReq> request);

    /**
     * 会员卡退款失败创建工单
     * @param request
     * @return
     */
    void refundVipFailed(RefundResultMsg t,SVipRefundResultMsg request);

    /**
     * 查询该订单下的支付记录
     * @param orderId 会员卡订单id
     * @return 支付记录列表
     */
    List<VipOrderPayLogDetailDTO> queryOrderPayLog(Long orderId);

    VipOrderReduceDetailAdmin reduceDetail(Long vipReduceId);

    PageResultResponse<VipOrderReducePriceApplyAdmin> queryVipReduceList(CardDiscountListRequest request);

    /**
     * 减免费率计算
     * @param cardDeductionCalculationRequest
     * @return 支付记录列表
     */
    CardDeductionCalculationDto cardDeductionCalculation(CardDeductionCalculationRequest cardDeductionCalculationRequest);
}
