/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xinfei.vocmng.biz.model.resp;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @version $ FeeStrategyConfig, v 0.1 2024-12-02 14:26 junjie.yan Exp $
 */
@Data
public class FeeStrategyConfig {

    @ApiModelProperty("字典明细表id")
    private Long dictDetailId;

    @ApiModelProperty("费控场景名称,字典value")
    private String sceneName;

    @ApiModelProperty("字典key")
    private String dictKey;

    @ApiModelProperty("策略id")
    private String strategyId;

    @ApiModelProperty("是否能跳出费控（0：不能，1：能）")
    private Integer canBreakOut;

    @ApiModelProperty("是否需要选择投诉渠道（0：否，1：是")
    private Integer needComplaintChannel;

}