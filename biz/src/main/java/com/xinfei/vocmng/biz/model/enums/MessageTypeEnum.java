package com.xinfei.vocmng.biz.model.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @version $ MessageTypeEnum, v 0.1 2025/3/12 18:42 shaohui.chen Exp $
 */
@AllArgsConstructor
@Getter
public enum MessageTypeEnum {
    /**
     * 消息
     */
    MESSAGE("message", "message"),

    /**
     * 图片
     */
    IMAGE("image", "image"),

    /**
     * 文件
     */
    FILE("file", "file"),

    /**
     * 文本
     */
    TEXT("text", "text"),

    /**
     * 表情
     */
    EMOTION("emotion", "emotion"),

    /**
     * 视频
     */
    VIDEO("video", "video"),

    /**
     * 语音
     */
    VOICE("voice", "voice"),

    /**
     * 音频
     */
    AUDIO("audio", "audio"),

    /**
     * 撤回类型
     */
    REVOKE("revoke", "revoke"),

    /**
     * 聊天记录
     */
    CHAT_RECORD("chatrecord", "item");

    private final String code;
    private final String description;

    /**
     * 根据 code 获取对应的枚举
     *
     * @param code 字符串 code
     * @return 对应的 MessageTypeEnum，如果不存在则返回 null
     */
    public static MessageTypeEnum fromCode(String code) {
        for (MessageTypeEnum type : MessageTypeEnum.values()) {
            if (type.getCode().equals(code)) {
                return type;
            }
        }
        return null;
    }
}
