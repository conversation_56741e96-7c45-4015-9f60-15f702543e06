/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2023 All Rights Reserved.
 */
package com.xinfei.vocmng.biz.model.resp;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.xinfei.vocmng.biz.model.annotation.DataPermission;
import com.xinfei.vocmng.biz.model.base.TimeFormat;
import com.xinfei.vocmng.biz.model.enums.DataPermissionType;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;
import java.util.List;

/**
 * <AUTHOR>
 * @version $ CommunicateSummaryResp, v 0.1 2023/12/18 11:57 wancheng.qu Exp $
 */

@Data
public class CommunicateSummaryResp implements Serializable {

    @ApiModelProperty(value = "小结id")
    private Long id;

    @ApiModelProperty(value = "进线电话")
    @DataPermission(type = DataPermissionType.MASK_TELE)
    private String telephone;

    @ApiModelProperty(value = "注册电话")
    @DataPermission(type = DataPermissionType.MASK_TELE)
    private String mobile;

    @ApiModelProperty(value = "注册app")
    private String app;

    @ApiModelProperty(value = "姓名")
    @DataPermission(type = DataPermissionType.MASK_NAME)
    private String name;

    @ApiModelProperty(value = "进线来源")
    private String source;

    @ApiModelProperty(value = "一级问题类型分类")
    private String issueCategoryLv1;

    @ApiModelProperty(value = "二级问题类型分类")
    private String issueCategoryLv2;

    @ApiModelProperty(value = "三级问题类型分类")
    private String issueCategoryLv3;

    @ApiModelProperty(value = "是否创建工单")
    private Boolean hasWork;

    @ApiModelProperty(value = "创建人")
    private String createUser;

    @ApiModelProperty("创建时间")
    @JsonFormat(pattern = TimeFormat.DATE_TIME)
    private LocalDateTime createdTime;

    @ApiModelProperty("最近一条备注")
    private String remark;

    @ApiModelProperty("备注信息列表（包含备注内容和创建时间）")
    private List<RemarkInfo> remarkInfoList;

    @ApiModelProperty(value = "userNo")
    private String userNo;

    @ApiModelProperty(value = "custNo")
    private String custNo;

    @ApiModelProperty(value = "status")
    private Integer status;

    @ApiModelProperty(value = "一级问题类型分类ID")
    private Long issueCategoryLv1Id;

    @ApiModelProperty(value = "二级问题类型分类ID")
    private Long issueCategoryLv2Id;

    @ApiModelProperty(value = "三级问题类型分类ID")
    private Long issueCategoryLv3Id;

    @ApiModelProperty("订单号")
    private String orderNo;

    @ApiModelProperty("投诉来源")
    private String complaintSource;

    @ApiModelProperty(value = "飞享会员订单号")
    private String vipOrderNo;

    @ApiModelProperty(value = "飞跃会员订单号")
    private String sVipOrderNo;

    @ApiModelProperty("回电手机")
    @DataPermission(type = DataPermissionType.MASK_TELE)
    private String callBackMobile;


}