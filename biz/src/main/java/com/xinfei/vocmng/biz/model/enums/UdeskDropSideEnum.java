package com.xinfei.vocmng.biz.model.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 *
 * <AUTHOR>
 * @version $ UdeskDropSideEnum, v 0.1 2025/3/7 10:41 shaohui.chen Exp $
 */
@AllArgsConstructor
@Getter
public enum UdeskDropSideEnum {
    // Udesk返回的挂断方描述 -> 对应我方枚举
    CUSTOMER("客户", DropSideEnum.CUSTOMER),
    AGENT("客服",  DropSideEnum.AGENT);

    private final String dropSideDesc;    // Udesk字符串：如 "客户" / "客服"
    private final DropSideEnum dropSide;  // 对应的我方挂断方枚举

    /**
     * 根据 Udesk 的字符串，获取对应的我方 code
     */
    public static int getMySideCode(String udeskDesc) {
        for (UdeskDropSideEnum e : UdeskDropSideEnum.values()) {
            if (e.getDropSideDesc().equals(udeskDesc)) {
                // 返回对应的我方 code
                return e.getDropSide().getCode();
            }
        }
        return -1;
    }
}
