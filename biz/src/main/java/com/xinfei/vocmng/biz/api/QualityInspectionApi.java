/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xinfei.vocmng.biz.api;

import com.xinfei.vocmng.biz.log.DigestLogAnnotated;
import com.xinfei.vocmng.biz.model.annotation.LoginRequired;
import com.xinfei.vocmng.biz.model.req.AsrInfoRequest;
import com.xinfei.vocmng.biz.model.req.QualityTokenReq;
import com.xinfei.vocmng.biz.model.resp.ApiResponse;
import com.xinfei.vocmng.biz.service.impl.QualityInspectionImp;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * <AUTHOR>
 * @version $ QualityInspectionApi, v 0.1 2024-10-15 19:20 junjie.yan Exp $
 */
@RestController
@Api(tags = "实时质检相关接口")
@RequestMapping("/realTime")
public class QualityInspectionApi {

    @Resource
    private QualityInspectionImp qualityInspectionImp;

    @ApiOperation("获取token")
    @LoginRequired
    @PostMapping("/token")
    public ApiResponse<String> token(@RequestBody @Valid QualityTokenReq req) {
        return ApiResponse.success(qualityInspectionImp.getToken(req.getId()));
    }

    @ApiOperation("保存ASR回调信息")
    @PostMapping("/saveAsrInfo")
    @DigestLogAnnotated("saveAsrInfo")
    public ApiResponse<Boolean> saveAsrInfo(@RequestBody @Valid AsrInfoRequest req) {
        return ApiResponse.success(qualityInspectionImp.saveAsrInfo(req));
    }

}