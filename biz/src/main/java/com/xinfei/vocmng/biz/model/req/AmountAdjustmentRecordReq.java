/*
 * Xinfei.com Inc.
 * Copyright (c) 2015-2024. All Rights Reserved.
 */

package com.xinfei.vocmng.biz.model.req;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @version $ AmountAdjustmentRecordReq, v 0.1 2024/8/12 17:19 you.zhang Exp $
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AmountAdjustmentRecordReq {

    @ApiModelProperty(value = "app")
    private String app;

    @ApiModelProperty(value = "客户号")
    private String customerNo;

    @ApiModelProperty(value = "调额类型")
    private String adjustType;
}
