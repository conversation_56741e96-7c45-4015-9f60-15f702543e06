/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xinfei.vocmng.biz.model.resp;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.xinfei.vocmng.biz.model.base.TimeFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 *
 * <AUTHOR>
 * @version $ RefundResp, v 0.1 2024/3/25 15:45 wancheng.qu Exp $
 */
@Data
public class RefundDetailResp implements Serializable {
    @ApiModelProperty(value = "退款申请单ID")
    private BigDecimal refundRequestId;

    @ApiModelProperty(value = "退款金额")
    private BigDecimal amountRefund;

    @ApiModelProperty(value = "退款方式，1:线上原路退，2:线下退款")
    private Integer refundMethod;

    @ApiModelProperty(value = "退款客户姓名")
    private String refundUserName;

    @ApiModelProperty(value = "退款类型，1:银行卡，2:支付宝")
    private Integer refundPayType;

    @ApiModelProperty(value = "退款账号")
    private BigDecimal refundAccount;

    @ApiModelProperty(value = "退款银行")
    private String refundBank;

    @ApiModelProperty(value = "坐席姓名")
    private String agentName;

    @JsonFormat(pattern = TimeFormat.DATE_TIME)
    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createTime;

    @JsonFormat(pattern = TimeFormat.DATE_TIME)
    @ApiModelProperty(value = "退款时间")
    private LocalDateTime refundTime;

    @JsonFormat(pattern = TimeFormat.DATE_TIME)
    @ApiModelProperty(value = "退款完成时间")
    private LocalDateTime refundCompletionTime;


}