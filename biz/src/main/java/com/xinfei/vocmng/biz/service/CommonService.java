/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xinfei.vocmng.biz.service;

import com.xinfei.repaytrade.facade.rr.response.QueryRepaymentPlanResponse;
import com.xinfei.repaytrade.facade.rr.response.RepayLoanCalcResponse;
import com.xinfei.vocmng.biz.model.enums.PlanStatus;
import com.xinfei.vocmng.biz.util.LocalDateTimeUtils;
import com.xinfei.vocmng.dal.mapper.EasyComplaintUserMapper;
import com.xinfei.vocmng.dal.mapper.LabelConfigMapper;
import com.xinfei.vocmng.dal.mapper.RepaymentPlanDetailMapper;
import com.xinfei.vocmng.dal.mapper.RepaymentPlanMapper;
import com.xinfei.vocmng.dal.mapper.RiskUserMapper;
import com.xinfei.vocmng.dal.mapper.SettleUserMapper;
import com.xinfei.vocmng.dal.mapper.StopUrgingMapper;
import com.xinfei.vocmng.dal.po.EasyComplaintUser;
import com.xinfei.vocmng.dal.po.LabelDto;
import com.xinfei.vocmng.dal.po.RepaymentPlan;
import com.xinfei.vocmng.dal.po.RepaymentPlanDetail;
import com.xinfei.vocmng.dal.po.RiskUser;
import com.xinfei.vocmng.dal.po.SettleUser;
import com.xinfei.vocmng.dal.po.StopUrging;
import com.xinfei.vocmng.itl.client.exeception.DeductPlanChannelEnum;
import com.xinfei.vocmng.itl.client.feign.CisFacadeClient;
import com.xinfei.vocmng.itl.client.feign.RepayFacadeClient;
import com.xinfei.vocmng.itl.client.feign.impl.RandomGeneratorClientImpl;
import com.xinfei.vocmng.itl.client.feign.service.CisFacadeClientService;
import com.xinfei.vocmng.itl.client.feign.service.RepayFacadeService;
import com.xinfei.vocmng.itl.client.feign.service.dto.RepayCalculateDto;
import com.xinfei.vocmng.itl.util.LogUtil;
import com.xinfei.vocmng.util.distributedLock.DistributedLock;
import com.xyf.cis.query.facade.dto.standard.response.ThreeElementsDTO;
import com.xyf.cis.query.facade.dto.standard.response.UserSearchDTO;
import com.xyf.user.facade.common.model.PageResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version $ CommonService, v 0.1 2024-04-10 10:20 junjie.yan Exp $
 */
@Component
@Slf4j
public class CommonService {

    @Value(value = "${settleLimitCalcFlag:true}")
    private Boolean isSwitch;

    @Resource
    private CisFacadeClient cisFacadeClient;

    @Resource
    private CisFacadeClientService cisFacadeClientService;

    @Resource
    private RepayFacadeClient repayFacadeClient;

    @Resource
    private RepayFacadeService repayFacadeService;

    @Resource
    private RepaymentPlanDetailMapper repaymentPlanDetailMapper;

    @Resource
    private RepaymentPlanMapper repaymentPlanMapper;

    @Resource
    private RiskUserMapper riskUserMapper;

    @Resource
    private LabelConfigMapper labelConfigMapper;

    @Resource
    private EasyComplaintUserMapper easyComplaintUserMapper;

    @Resource
    private SettleUserMapper settleUserMapper;

    @Resource
    private RandomGeneratorClientImpl randomGeneratorClient;

    @Resource
    private StopUrgingMapper stopUrgingMapper;

    @Value(value = "${easyComplaintUsersSelectSize:200}")
    private Integer selectSize;

    @Value(value = "${stopUrgingIsAll:false}")
    private Boolean stopUrgingIsAll;

    @Value(value = "${stopUrgingSelectSize:200}")
    private Integer stopUrgingSelectSize;

    /**
     * 根据手机号获取对应的custNo，再根据custNo获取用户信息
     *
     * @param mobileCust
     * @return
     */
    public List<UserSearchDTO> getUserByMobileAndCust(String mobileCust) {
        if (StringUtils.isEmpty(mobileCust)) {
            return new ArrayList<>();
        }

        List<UserSearchDTO> userByMobile = new ArrayList<>();

        PageResult<UserSearchDTO> mobileResult;
        int n = 1;
        do {
            mobileResult = cisFacadeClient.queryUserList(mobileCust, null, null, n, 30);
            if (CollectionUtils.isEmpty(mobileResult.getList())) {
                break;
            }
            userByMobile.addAll(mobileResult.getList());
            n++;
        } while (true);

        List<String> custNos = new ArrayList<>();
        if (!CollectionUtils.isEmpty(userByMobile)) {
            custNos = userByMobile.stream().map(UserSearchDTO::getCustNo).filter(StringUtils::isNotEmpty).distinct().collect(Collectors.toList());
        }
        if (CollectionUtils.isEmpty(custNos)) {
            return new ArrayList<>();
        }

        List<UserSearchDTO> userSearchDTOS = new ArrayList<>();
        for (String custNo : custNos) {
            PageResult<UserSearchDTO> custNoResult;
            int m = 1;
            do {
                custNoResult = cisFacadeClient.queryUserList(null, custNo, null, m, 30);
                if (CollectionUtils.isEmpty(custNoResult.getList())) {
                    break;
                }
                userSearchDTOS.addAll(custNoResult.getList());
                m++;
            } while (true);
        }

        return userSearchDTOS;
    }

    /**
     * 方案状态:（0：待生效、1：生效中 、2：失效、3：成功）
     * status = 0 =>失效
     * status = 1 && use_status != 2 && 时间有效判断 = >生效中
     * status = 1 && use_status != 2 && 时间无效判断 = >失效
     * status = 1 &&  use_status =2  =>成功
     *
     * @param planDeduct
     * @return
     */
    @NotNull
    public static Integer getPlanStatus(QueryRepaymentPlanResponse.PlanDeduct planDeduct) {
        if (planDeduct == null) {
            return -1;
        }

        if (planDeduct.getStatus() == 0) {
            return 2;
        }

        if (planDeduct.getDateExpire() != null) {
            if (planDeduct.getStatus() == 1 && Integer.parseInt(planDeduct.getUseStatus()) != 2 && LocalDateTime.now().isAfter(LocalDateTimeUtils.parseLocalDateTimeByDate(planDeduct.getDateExpire()))) {
                return 2;
            }

            if (planDeduct.getStatus() == 1 && Integer.parseInt(planDeduct.getUseStatus()) != 2 && LocalDateTime.now().isBefore(LocalDateTimeUtils.parseLocalDateTimeByDate(planDeduct.getDateExpire()))) {
                return 1;
            }
        }

        if (planDeduct.getStatus() == 1 && Integer.parseInt(planDeduct.getUseStatus()) == 2) {
            return 3;
        }

        return -1;
    }

    /**
     * 是否存在来自贷后的生效中方案
     *
     * @param loanNo
     * @return
     */
    public Boolean hasEffectivePlan(String loanNo) {
        QueryRepaymentPlanResponse planResponse = repayFacadeClient.queryRepaymentPlan(loanNo, null, DeductPlanChannelEnum.HUTTA.getCode(), 1);
        if (planResponse != null && CollectionUtils.isNotEmpty(planResponse.getPlanDeductList())) {
            for (QueryRepaymentPlanResponse.PlanDeduct planDeduct : planResponse.getPlanDeductList()) {
                Integer planStatus = getPlanStatus(planDeduct);
                if (planStatus == 1) {
                    return true;
                }
            }
        }
        return false;
    }

    //客服生效中方案中：若存在0减免方案明细 && 存在贷后生效中方案，则将0减免方案明细状态置为失效 && 方案状态置为失效
    public Boolean disabledPlan(List<RepaymentPlanDetail> repaymentPlanDetails, RepaymentPlan effectPlan) {
        boolean needDisabled = false;
        for (RepaymentPlanDetail repaymentPlanDetail : repaymentPlanDetails) {
            if (repaymentPlanDetail.hasAllZeroReductions() && hasEffectivePlan(repaymentPlanDetail.getLoanNo())) {
                repaymentPlanDetail.setStatus(0);
                needDisabled = true;
                repaymentPlanDetailMapper.updateById(repaymentPlanDetail);
            }
        }

        if (needDisabled) {
            effectPlan.setPlanStatus(PlanStatus.INVALID.getCode());
            effectPlan.setUpdater("vocmng");
            effectPlan.setUpdatedTime(LocalDateTime.now());
            repaymentPlanMapper.updateById(effectPlan);
        }

        return needDisabled;
    }

    public Boolean isEffectivePlan(String loanNo) {
        RepaymentPlan effectPlan = repaymentPlanDetailMapper.queryEffectPlan(loanNo, null);
        if (Objects.nonNull(effectPlan)) {
            return true;
        } else {
            return hasEffectivePlan(loanNo);
        }
    }

    public RepayLoanCalcResponse getRepayCalculate(Integer repayType, List<String> terms, String loanNo, BigDecimal transUnprofitDeduct) {
        String settleLimitCalcFlag = null;
        if (isSwitch && isEffectivePlan(loanNo)) {
            settleLimitCalcFlag = "off";
        }
        return repayFacadeService.repayCalculate(repayType, terms, loanNo, transUnprofitDeduct, settleLimitCalcFlag);
    }

    public RepayCalculateDto getRepayCalculateDto(Integer repayType, List<String> terms, String loanNo, BigDecimal transUnprofitDeduct) {
        String settleLimitCalcFlag = null;
        if (isSwitch && isEffectivePlan(loanNo)) {
            settleLimitCalcFlag = "off";
        }
        return repayFacadeService.repayCalculateDto(repayType, terms, loanNo, settleLimitCalcFlag, transUnprofitDeduct);
    }

    public List<LabelDto> getExternalLabel(String userNo, String enCodeTel) {
        List<LabelDto> externalLabels = new ArrayList<>();
        if (StringUtils.isNotBlank(userNo) && StringUtils.isEmpty(enCodeTel)) {
            ThreeElementsDTO ted = cisFacadeClient.queryThreeElementsByUserNo(Long.parseLong(userNo));
            if (ted != null) {
                enCodeTel = cisFacadeClientService.getEncodeMobileLocal(ted.getMobile());
            }
        }

        try {
            //查询客户是否为高风险用户
            if (!StringUtils.isEmpty(enCodeTel)) {
                RiskUser riskUser = riskUserMapper.getRiskUser(enCodeTel);
                if (riskUser != null && riskUser.getScoreBin() > 0) {
                    LabelDto labelConfig = labelConfigMapper.getLabelConfig("高风险用户");
                    if (labelConfig != null) {
                        externalLabels.add(labelConfig);
                    }
                }
            }
            //客服是否为易投诉用户
            if (!StringUtils.isEmpty(userNo)) {
                EasyComplaintUser easyComplaintUser = easyComplaintUserMapper.getEasyComplaintUser(userNo);
                if (easyComplaintUser != null && "groupA".equals(easyComplaintUser.getUserGroup())) {
                    LabelDto labelConfig = labelConfigMapper.getLabelConfig("易投诉");
                    if (labelConfig != null) {
                        externalLabels.add(labelConfig);
                    }
                }
            }
            //客服是否为结清用户
            if (!StringUtils.isEmpty(enCodeTel)) {
                SettleUser settleUser = settleUserMapper.getSettleUser(enCodeTel);
                if (settleUser != null) {
                    LabelDto labelConfig = labelConfigMapper.getLabelConfig("结清风险");
                    if (labelConfig != null) {
                        externalLabels.add(labelConfig);
                    }
                }
            }
        } catch (Exception e) {
            log.error("getExternalLabel warn,enCodeTel:{} userNo:{}", enCodeTel, userNo);
        }

        return externalLabels;
    }

    @DistributedLock(lockName = "'easyComplaintUserSync:lock:' + T(java.time.LocalDate).now().format(T(java.time.format.DateTimeFormatter).ofPattern('yyyyMMddHHmm'))",
            lockTime = 5, retryTimes = 3)
    public void easyComplaintUserSync() {
        //处理易投诉灰度策略
        log.info(LogUtil.infoLog("easyComplaintUserSync", "易投诉灰度数据同步"));
        Integer idMax = easyComplaintUserMapper.getEasyComplaintUserNewId();
        Integer id = 1;
        while (id <= idMax) {
            List<EasyComplaintUser> easyComplaintUsersList = easyComplaintUserMapper.getEasyComplaintUserList(id, id + selectSize);
            if (CollectionUtils.isNotEmpty(easyComplaintUsersList)) {
                List<EasyComplaintUser> filteredList = easyComplaintUsersList.stream()
                        .map(easyComplaintUser -> {
                            if (StringUtils.isBlank(easyComplaintUser.getUserGroup())) {
                                String userGroup = randomGeneratorClient.ab(easyComplaintUser.getUserNo(), "kf_tsmx_dbf_abtest");
                                if (StringUtils.isNotBlank(userGroup)) {
                                    easyComplaintUser.setUserGroup(userGroup);
                                    return easyComplaintUser;
                                }
                                return null;
                            }
                            return null;
                        })
                        .filter(Objects::nonNull)
                        .collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(filteredList)) {
                    easyComplaintUserMapper.easyComplaintUserBatchUpdate(filteredList);
                }
            }
            id = id + selectSize;
        }
    }

    @DistributedLock(lockName = "'stopUrgingSync:lock:' + T(java.time.LocalDate).now().format(T(java.time.format.DateTimeFormatter).ofPattern('yyyyMMddHHmm'))",
            lockTime = 5, retryTimes = 3)
    public void stopUrgingSync() {
        //处理易投诉灰度策略
        log.info(LogUtil.infoLog("stopUrgingSync", "停催灰度数据同步"));
        String startTime = LocalDateTimeUtils.format(LocalDateTime.now().toLocalDate().atStartOfDay(), LocalDateTimeUtils.FORMATTERDATE);

        Integer lastId = stopUrgingIsAll ? 0 : stopUrgingMapper.getStopUrgingSize(startTime);
        while (true && lastId !=null) {
            List<StopUrging> stopUrgingList = stopUrgingMapper.getStopUrging(lastId, lastId+stopUrgingSelectSize);
            if (CollectionUtils.isEmpty(stopUrgingList)) {
                break;
            }

            List<StopUrging> toUpdateList = new ArrayList<>();
            for (StopUrging stopUrging : stopUrgingList) {
                if (StringUtils.isBlank(stopUrging.getUserGroup())) {
                    String userGroup = randomGeneratorClient.ab(stopUrging.getCustNo(), "zxjdtc_kfxt");
                    if (StringUtils.isNotBlank(userGroup)) {
                        stopUrging.setUserGroup(userGroup);
                        toUpdateList.add(stopUrging);
                    }
                }
            }

            if (CollectionUtils.isNotEmpty(toUpdateList)) {
                stopUrgingMapper.stopUrgingBatchUpdate(toUpdateList);
            }
            lastId = lastId + stopUrgingSelectSize;
        }
    }
}