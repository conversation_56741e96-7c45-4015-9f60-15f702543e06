/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xinfei.vocmng.biz.config;


import com.xinfei.vocmng.biz.constants.LoginUserConstants;
import com.xinfei.vocmng.biz.model.event.SseEvent;
import com.xinfei.vocmng.biz.mq.SseProducer;
import com.xinfei.vocmng.biz.util.RandomUtil;
import com.xinfei.vocmng.biz.util.RedisUtils;
import com.xinfei.xfframework.common.JsonUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @version $ CustomEventListener, v 0.1 2024/2/7 15:35 wancheng.qu Exp $
 */
@Slf4j
@Component
public class CustomEventListener {

    @Resource
    private RedisUtils redisUtils;
    @Resource
    private SseProducer sseProducer;

    @Value("${waitTime}")
    private Long waitTime;

    @EventListener
    public void handleCustomEvent(SseEvent event) {
        String userIdentify = event.getUserIdentify();
        log.info("receive event,useridentify={},data={}", userIdentify, JsonUtil.toJson(event));
        Map<String, Object> data = new HashMap<>();
        data.put("userIdentify", userIdentify);
        data.put("customerPhone", event.getCustomerPhone());
        data.put("displayNumber", event.getDisplayNumber());
        data.put("summaryId", event.getSummaryId());
        String key = getRedisKey(userIdentify, event.getSummaryId());
        data.put("mesgId", key);
        log.info("sse redis key={},value={}", key, userIdentify);
        redisUtils.set(key, JsonUtil.toJson(data), waitTime);
        String delayKey = key.concat(LoginUserConstants.RETRY);
        redisUtils.set(delayKey, userIdentify, 60);
        sseProducer.sendSseMsg(data);
    }

    private String getRedisKey(String userIdentify, Long summaryId) {
        String random = RandomUtil.get5Random();
        return LoginUserConstants.SSE_KEY + random + userIdentify + "SUMMARYID" + summaryId;
    }
}

