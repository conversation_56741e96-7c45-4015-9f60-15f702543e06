/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2023 All Rights Reserved.
 */
package com.xinfei.vocmng.biz.util;



import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import java.util.Map;
import java.util.function.BiConsumer;
/**
 *
 * <AUTHOR>
 * @version $ QueryWrapperUtil, v 0.1 2023/12/23 19:49 wancheng.qu Exp $
 */

public class QueryWrapperUtil {

    public static <T, R> void applyConditions(LambdaQueryWrapper<T> queryWrapper, Map<R, BiConsumer<LambdaQueryWrapper<T>, R>> conditions) {
        conditions.forEach((value, condition) -> {
            if (value != null) {
                condition.accept(queryWrapper, value);
            }
        });
    }
}
