package com.xinfei.vocmng.biz.api;

import com.xinfei.contractcore.common.service.facade.vo.ContractVO;
import com.xinfei.vocmng.biz.model.annotation.JsonIgnoreParameter;
import com.xinfei.vocmng.biz.model.base.PageResultResponse;
import com.xinfei.vocmng.biz.model.resp.ApiResponse;
import com.xinfei.vocmng.biz.rr.dto.DocumentRecordDto;
import com.xinfei.vocmng.biz.rr.request.ContractCoreReq;
import com.xinfei.vocmng.biz.rr.request.ContractReq;
import com.xinfei.vocmng.biz.rr.request.DocumentRecordReq;
import com.xinfei.vocmng.biz.rr.request.LookFileResp;
import com.xinfei.vocmng.biz.rr.request.QueryContractListRequest;
import com.xinfei.vocmng.biz.rr.request.ResignContractReq;
import com.xinfei.vocmng.dal.po.CapitalMail;
import com.xinfei.vocmng.dal.po.SendMail;
import com.xinfei.vocmng.itl.rr.ContractStatusDetail;
import com.xinfei.vocmng.itl.rr.LoanInfoRequest;
import com.xinfei.vocmng.itl.rr.ProtocolDto;
import com.xinfei.vocmng.itl.rr.SettlementCertificateReq;
import com.xinfei.vocmng.itl.rr.dto.ContractBaseDataDto;
import com.xinfei.vocmng.itl.rr.dto.ContractDataDto;
import com.xinfei.vocmng.itl.rr.dto.ContractDetailDto;
import com.xinfei.vocmng.itl.rr.dto.LoanInfoDto;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * <AUTHOR>
 * @version $ ContractApi, v 0.1 2023/12/21 20:05 qu.lu Exp $
 */
@Api(tags = "合同相关接口")
@RequestMapping("/contract")
public interface ContractApi {
    @ApiOperation("根据合同号查询合同列表信息")
    @PostMapping("/list")
    ApiResponse<List<ContractDetailDto>> queryContractDetailList(@RequestBody QueryContractListRequest request);

    @ApiOperation("查询app和出具单位")
    @GetMapping("/queryBaseData")
    ApiResponse<ContractBaseDataDto> queryContractBaseData();

    @ApiOperation("结清证明生成预览")
    @PostMapping("/search-jqzm-info")
    ApiResponse<LoanInfoDto> queryJqzmInfo(@RequestBody LoanInfoRequest request);

    @ApiOperation("查询特殊资方列表")
    @GetMapping("/querySpecialList")
    ApiResponse<List<String>> querySpecialList();

    @ApiOperation("获取资方结清状态")
    @PostMapping("/queryObtainStatus")
    ApiResponse<List<ContractStatusDetail>> queryObtainStatus(@RequestBody List<String> orderIds);

    @ApiOperation("信飞结清证明发送邮件")
    @PostMapping("/sendMail")
    ApiResponse<Boolean> sendMail(@Validated @RequestBody SettlementCertificateReq req);

    @ApiOperation("居间协议发送邮件")
    @PostMapping("/sendIntermediaryAgreement")
    ApiResponse<Boolean> sendIntermediaryAgreement(@Validated @RequestBody SettlementCertificateReq req);

    @ApiOperation("信飞结清证明下载")
    @PostMapping("/downFile")
    ApiResponse<List<String>> downFile(@Validated @RequestBody SettlementCertificateReq req);

    @ApiOperation("立即申请")
    @PostMapping("/applyNow")
    ApiResponse<String> applyNow(@RequestBody SettlementCertificateReq orderIds);

    @ApiOperation("文件申请列表")
    @PostMapping("/queryApplyList")
    ApiResponse<PageResultResponse<DocumentRecordDto>> queryApplyList(@RequestBody DocumentRecordReq record);

    @ApiOperation("查看文件")
    @PostMapping("/lookFile")
    ApiResponse<LookFileResp> lookFile(@Validated @RequestBody DocumentRecordReq record);

    @ApiOperation("文件列表发送邮箱")
    @PostMapping("/sendFile")
    ApiResponse<String> sendFile(@Validated @RequestBody DocumentRecordReq record);

    @ApiOperation("上传证明")
    @PostMapping("/uploadDocumentRecord")
    ApiResponse<Boolean> uploadDocumentRecord(
            @RequestParam(name = "file", required = true) @JsonIgnoreParameter MultipartFile[] file,
            @RequestParam(name = "mail", required = true) String mail,
            @RequestParam(name = "type", required = true) Integer type,
            @RequestParam(name = "id", required = true) Long id);

    @ApiOperation("资方邮件配置列表")
    @GetMapping("/queryCapitalList")
    ApiResponse<List<CapitalMail>> queryCapitalList();

    @ApiOperation("资方邮件修改")
    @PostMapping("/updateCapital")
    ApiResponse<Boolean> updateCapital(@RequestBody CapitalMail req);

    @ApiOperation("资方邮件新增")
    @PostMapping("/insertCapital")
    ApiResponse<Boolean> insertCapital(@RequestBody CapitalMail req);

    @ApiOperation("发件邮箱列表 ")
    @PostMapping("/querySendList")
    ApiResponse<List<SendMail>> querySendList(@RequestBody SendMail req);

    @ApiOperation("新增发件邮箱")
    @PostMapping("/insertSend")
    ApiResponse<Boolean> insertSend(@RequestBody SendMail req);

    @ApiOperation("发件邮箱修改")
    @PostMapping("/updateSend")
    ApiResponse<Boolean> updateSend(@RequestBody SendMail req);

    @ApiOperation("合同列表")
    @PostMapping("/queryContractList")
    ApiResponse<List<ContractDataDto>> queryContractList(@Validated @RequestBody ContractReq req);

    @ApiOperation("合同协议获取")
    @PostMapping("/getProtocolsByType")
    ApiResponse<List<ProtocolDto>> getProtocolsByType(@Validated @RequestBody ContractReq req);

    @ApiOperation("重签合同")
    @PostMapping("/resignContract")
    ApiResponse<String> resignContract(@Validated @RequestBody ResignContractReq req);

    @ApiOperation("新合同列表")
    @PostMapping("/queryVipContractList")
    ApiResponse<List<ContractVO>> queryContractCoreList(@Validated @RequestBody ContractCoreReq req);

}
