/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2023 All Rights Reserved.
 */
package com.xinfei.vocmng.biz.api;

import com.xinfei.vocmng.biz.model.annotation.JsonIgnoreParameter;
import com.xinfei.vocmng.biz.model.annotation.LoginRequired;
import com.xinfei.vocmng.biz.model.annotation.OperateLogAnnotation;
import com.xinfei.vocmng.biz.model.annotation.ResourcePermission;
import com.xinfei.vocmng.biz.model.base.PageResultResponse;
import com.xinfei.vocmng.biz.model.enums.OperateType;
import com.xinfei.vocmng.biz.model.req.*;
import com.xinfei.vocmng.biz.model.resp.*;
import com.xinfei.vocmng.biz.service.AccountService;
import com.xinfei.vocmng.biz.service.EmployeeService;
import com.xinfei.vocmng.dal.po.ControlAuth;
import com.xinfei.vocmng.dal.po.IssueCategoryConfig;
import com.xinfei.vocmng.dal.po.LabelCategoryConfig;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;
import java.util.stream.Collectors;

/**
 *
 * <AUTHOR>
 * @version $ AccountApi, v 0.1 2023/12/20 10:35 wancheng.qu Exp $
 */

@Api(tags = "账号相关")
@RestController
@LoginRequired
@RequestMapping("/account")
public class AccountApi {

    @Resource
    private AccountService accountService;

    @Resource
    private EmployeeService employeeService;

    @ApiOperation("获取用户详情")
    @GetMapping("/getUserInfo")
    public ApiResponse<LoginResp> getUserInfo(){
        return ApiResponse.success(accountService.getUserInfo());
    }


    @ApiOperation("获取用户所有资源权限")
    @GetMapping("/getUserResource")
    public ApiResponse<LoginResp> getUserResource(){
        return ApiResponse.success(accountService.getUserResource());
    }

    @ApiOperation("获取用户所在组织")
    @GetMapping("/getUserDepartment")
    public ApiResponse<String> getUserDepartment(@RequestParam("userIdentify") String userIdentify){
        return ApiResponse.success(employeeService.getUserDepartment(userIdentify));
    }

    @ApiOperation("组织查询")
    @PostMapping("/queryDepartment")
    public ApiResponse<List<DepartmentResp>> queryDepartment(@RequestBody DepartmentReq departmentReq){
        return ApiResponse.success(accountService.queryDepartment(departmentReq));
    }

    @ApiOperation("组织修改")
    @PostMapping("/updateDepartment")
    @ResourcePermission("updatetjacc")
    @OperateLogAnnotation(type = OperateType.TYPE_TWO, description = "组织修改")
    public ApiResponse<Boolean> updateDepartment(@RequestBody DepartmentReq departmentReq){
        return ApiResponse.success(accountService.updateDepartment(departmentReq));
    }

    @ApiOperation("组织新增")
    @PostMapping("/addDepartment")
    @ResourcePermission("createtjacc")
    @OperateLogAnnotation(type = OperateType.TYPE_ONE, description = "组织新增")
    public ApiResponse<Boolean> addDepartment(@RequestBody DepartmentReq departmentReq){
        return ApiResponse.success(accountService.addDepartment(departmentReq));
    }

    @ApiOperation("获取用户新费控")
    @GetMapping("/getNewFeeConfig")
    public ApiResponse<List<FeeStrategyConfig>> getNewFeeConfig(){
        return ApiResponse.success(accountService.getFeeStrategyConfig());
    }

    @ApiOperation("角色查询")
    @PostMapping("/queryRole")
    public ApiResponse<PageResultResponse<RoleResp>> queryRole(@RequestBody RoleReq roleReq){
        return ApiResponse.success(accountService.queryRole(roleReq));
    }

    @ApiOperation("角色修改")
    @PostMapping("/updateRole")
    @ResourcePermission("updaterole")
    @OperateLogAnnotation(type = OperateType.TYPE_TWO, description = "角色修改")
    public ApiResponse<Boolean> updateRole(@RequestBody RoleReq roleReq){
        return ApiResponse.success(accountService.updateRole(roleReq));
    }

    @ApiOperation("角色新增")
    @PostMapping("/addRole")
    @ResourcePermission("createrole")
    @OperateLogAnnotation(type = OperateType.TYPE_ONE, description = "角色新增")
    public ApiResponse<Boolean> addRole(@RequestBody RoleReq roleReq){
        return ApiResponse.success(accountService.addRole(roleReq));
    }

    @ApiOperation("菜单列表")
    @GetMapping("/queryMenuList")
    public ApiResponse<List<ResourceResp>> queryMenuList(){
        return ApiResponse.success(accountService.queryMenuList());
    }

    @ApiOperation("数据列表")
    @GetMapping("/queryDataList")
    public ApiResponse<List<DataAuthResp>> queryDataList(){
        return ApiResponse.success(accountService.queryDataList());
    }

    @ApiOperation("员工列表")
    @PostMapping("/queryEmployeeList")
    public ApiResponse<PageResultResponse<EmployeeResp>> queryEmployeeList(@RequestBody EmployeeReq employeeReq){
        return ApiResponse.success(accountService.queryEmployeeList(employeeReq));
    }

    @ApiOperation("新增员工")
    @PostMapping("/addEmployee")
    @ResourcePermission("createtemp")
    @OperateLogAnnotation(type = OperateType.TYPE_ONE, description = "新增员工")
    public ApiResponse<Boolean> addEmployee(@RequestBody EmployeeReq employeeReq){
        return ApiResponse.success(accountService.addEmployee(employeeReq));
    }

    @ApiOperation("员工修改")
    @PostMapping("/updateEmployee")
    @ResourcePermission("deleteemp")
    @OperateLogAnnotation(type = OperateType.TYPE_TWO, description = "员工修改")
    public ApiResponse<Boolean> updateEmployee(@RequestBody EmployeeReq employeeReq){
        return ApiResponse.success(accountService.updateEmployee(employeeReq));
    }

    @ApiOperation("批量修改删除员工组织")
    @PostMapping("/batchUpdateEmployee")
    @ResourcePermission("batchupdatetjacc")
    @OperateLogAnnotation(type = OperateType.TYPE_TWO, description = "批量修改删除员工组织")
    public ApiResponse<Boolean> batchUpdateEmployee(@RequestBody EmployeeListReq employeeReq){
        return ApiResponse.success(accountService.batchUpdateEmployee(employeeReq));
    }

    @ApiOperation("员工模版下载")
    @GetMapping("/downloadEmp")
    @OperateLogAnnotation(type = OperateType.TYPE_FOUR, description = "员工模版下载")
    public void downloadEmp(HttpServletResponse httpServletResponse){
        accountService.downloadEmp(httpServletResponse);
    }

    @ApiOperation("员工上传Excel")
    @PostMapping("/uploadEmp")
    @OperateLogAnnotation(type = OperateType.TYPE_THREE, description = "员工上传Excel")
    ApiResponse<Boolean> uploadEmp(@RequestParam(name = "file", required = true) @JsonIgnoreParameter MultipartFile file) throws IOException {
        return ApiResponse.success(accountService.uploadEmp(file));
    }

    @ApiOperation("标签类型下拉列表")
    @GetMapping("/queryLabelType")
    public ApiResponse<List<LabelCategoryConfig>> queryLabelType(){
        return ApiResponse.success(accountService.queryLabelType());
    }

    @ApiOperation("标签查询")
    @PostMapping("/queryLabel")
    public ApiResponse<PageResultResponse<LabelResp>> queryLabel(@RequestBody LabelReq labelReq){
        return ApiResponse.success(accountService.queryLabel(labelReq));
    }

    @ApiOperation("新增标签")
    @PostMapping("/addLabel")
    @ResourcePermission("addlabel")
    @OperateLogAnnotation(type = OperateType.TYPE_ONE, description = "新增标签")
    public ApiResponse<Boolean> addLabel(@RequestBody LabelReq labelReq){
        return ApiResponse.success(accountService.addLabel(labelReq));
    }

    @ApiOperation("客户打标")
    @PostMapping("/addUserLabel")
    @ResourcePermission("custommark")
    @OperateLogAnnotation(type = OperateType.TYPE_ONE, description = "客户打标")
    public ApiResponse<Boolean> addUserLabel(@RequestBody LabelReq labelReq){
        return ApiResponse.success(accountService.addUserLabel(labelReq));
    }

    @ApiOperation("修改标签")
    @PostMapping("/updateLabel")
    @ResourcePermission("updatelabel")
    @OperateLogAnnotation(type = OperateType.TYPE_TWO, description = "修改标签")
    public ApiResponse<Boolean> updateLabel(@RequestBody LabelReq labelReq){
        return ApiResponse.success(accountService.updateLabel(labelReq));
    }

    @ApiOperation("标签关联用户列表")
    @PostMapping("/queryLabelUser")
    @OperateLogAnnotation(type = OperateType.TYPE_FIVE, description = "标签关联用户列表")
    public ApiResponse<PageResultResponse<LabelUserResp>> queryLabelUser(@RequestBody LabelUserReq labelUserReq){
        return ApiResponse.success(accountService.queryLabelUser(labelUserReq));
    }

    @ApiOperation("删除标签关联用户")
    @PostMapping("/updateLabelUser")
    @OperateLogAnnotation(type = OperateType.TYPE_SIX, description = "删除标签关联用户")
    public ApiResponse<Boolean> updateLabelUser(@RequestBody LabelUserReq labelUserReq){
        return ApiResponse.success(accountService.updateLabelUser(labelUserReq));
    }

    @ApiOperation("标签模版下载")
    @GetMapping("/downloadLabel")
    @OperateLogAnnotation(type = OperateType.TYPE_FOUR, description = "标签模版下载")
    public void downloadLabel(HttpServletResponse httpServletResponse){
        accountService.downloadLabel(httpServletResponse);
    }

    @ApiOperation("标签上传Excel")
    @PostMapping("/uploadLabel")
    @OperateLogAnnotation(type = OperateType.TYPE_THREE, description = "标签上传Excel")
    ApiResponse<Boolean> uploadLabel(@RequestParam(name = "file", required = true) @JsonIgnoreParameter MultipartFile file) throws IOException {
        return ApiResponse.success(accountService.uploadLabel(file));
    }

    @ApiOperation("一二三级问题分类列表")
    @PostMapping("/queryIssList")
    public ApiResponse<List<IssueCategoryConfig>> queryIssList(@RequestBody IssueCategoryConfig req) {
        List<IssueCategoryConfig> list = accountService.queryIssList(req);
        if (CollectionUtils.isNotEmpty(list)) {
            list = list.stream().filter(r -> r.getIsHide() == 0).collect(Collectors.toList());
        }
        return ApiResponse.success(list);
    }

    @ApiOperation("费控权限列表")
    @GetMapping("/queryControlList")
    public ApiResponse<List<ControlAuth>> queryControlList(){
        return ApiResponse.success(accountService.queryControlList());
    }

    @ApiOperation("字典列表")
    @PostMapping("/queryDictList")
    public ApiResponse<PageResultResponse<DictResp>> queryDictList(@RequestBody DictReq req){
        return ApiResponse.success(accountService.queryDictList(req));
    }

    @ApiOperation("字典操作")
    @PostMapping("/opertaDict")
    @OperateLogAnnotation(type = OperateType.TYPE_TWO, description = "字典操作")
    public ApiResponse<Boolean> opertaDict(@RequestBody DictResp req){
        return ApiResponse.success(accountService.opertaDict(req));
    }


}