/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xinfei.vocmng.biz.model.resp;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @version $ RefundResp, v 0.1 2024/3/25 15:45 wancheng.qu Exp $
 */
@Data
public class RefundResp implements Serializable {

    @ApiModelProperty("可退金额")
    private BigDecimal canRefundAmt;

    @ApiModelProperty("退款金额")
    private BigDecimal refundAmt;

    @ApiModelProperty("审核中金额")
    private BigDecimal reviewRefundAmt;

    @ApiModelProperty("可退金额下限")
    private BigDecimal canRefundAmtLower;

    @ApiModelProperty("可退金额上限")
    private BigDecimal canRefundAmtUpper;

    @ApiModelProperty("voc担保费=担保费+反担保费")
    private BigDecimal guaranteeFee;

    @ApiModelProperty("voc逾期费=罚息+罚息（我方）+催费")
    private BigDecimal lateFee;

    @ApiModelProperty("退款中金额")
    private BigDecimal ongoingRefundAmt;

    @ApiModelProperty("本金")
    private BigDecimal prinAmt;
    @ApiModelProperty("利息")
    private BigDecimal intAmt;
    @ApiModelProperty("罚金")
    private BigDecimal ointAmt;
    @ApiModelProperty("贷后管理费")
    private BigDecimal fee1Amt;
    @ApiModelProperty("平台服务费")
    private BigDecimal fee2Amt;
    @ApiModelProperty("逾期贷后管理费")
    private BigDecimal fee3Amt;
    @ApiModelProperty("提前结清手续费")
    private BigDecimal fee4Amt;
    @ApiModelProperty("违约金")
    private BigDecimal fee5Amt;
    @ApiModelProperty("催费")
    private BigDecimal fee6Amt;

    @ApiModelProperty("失败原因")
    private String reason;

    @ApiModelProperty(value = "借据号")
    private String loanNo;

    @ApiModelProperty(value = "订单号")
    private String orderNo;

    @ApiModelProperty(value = "账单号")
    private String billNo;

}