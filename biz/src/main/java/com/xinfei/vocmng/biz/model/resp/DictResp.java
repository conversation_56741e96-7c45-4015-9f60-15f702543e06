/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xinfei.vocmng.biz.model.resp;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.xinfei.vocmng.biz.model.base.TimeFormat;
import com.xinfei.vocmng.dal.po.DictDetail;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 *
 * <AUTHOR>
 * @version $ DictResp, v 0.1 2024/4/9 17:33 wancheng.qu Exp $
 */
@Data
public class DictResp implements Serializable {

    @ApiModelProperty(value = "操作类型：1新增。2修改。3删除")
    private Integer type ;

    @ApiModelProperty(value = "字典id")
    private Long id ;

    @ApiModelProperty(value = "字典名")
    private String name;

    @ApiModelProperty(value = "字典描述")
    private String description;

    @ApiModelProperty(value = "创建时间")
    @JsonFormat(pattern = TimeFormat.DATE_TIME)
    private LocalDateTime createdTime;

    @ApiModelProperty(value = "修改时间")
    @JsonFormat(pattern = TimeFormat.DATE_TIME)
    private LocalDateTime updateTime;

    @ApiModelProperty(value = "创建人")
    private String createUser;

    @ApiModelProperty(value = "修改")
    private String updateUser;

    @ApiModelProperty(value = "字典详情集合")
    private List<DictDetail> detail;
}